{"name": "abn.green", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbo", "dev:webpack": "next dev", "dev:fast": "next dev --turbo --port 3000", "build": "CI=true next build && npm run generate-sw", "build:turbo": "CI=true next build --turbo && npm run generate-sw", "build:fast": "npm run build:turbo", "build:production": "NODE_ENV=production CI=true next build && npm run generate-sw", "build:production:turbo": "NODE_ENV=production CI=true next build --turbo && npm run generate-sw", "build:nolint": "DISABLE_ESLINT_PLUGIN=true next build && npm run generate-sw", "build:nolint:turbo": "DISABLE_ESLINT_PLUGIN=true next build --turbo && npm run generate-sw", "build:analyze": "ANALYZE=true npm run build", "build:analyze:turbo": "ANALYZE=true npm run build:turbo", "benchmark": "node scripts/benchmark-builds.js", "benchmark:quick": "npm run benchmark", "start": "next start", "serve": "next start serve", "lint": "next lint", "prettier": "prettier --write \"**/*.{js,ts,tsx,json}\"", "test": "jest", "test:watch": "jest --watch", "generate-sw": "cp public/sw.js .next/static/sw.js", "promote": "node scripts/promote.js", "sync-data": "ts-node --project tsconfig.json -r tsconfig-paths/register src/scripts/syncData.ts", "seed-twins": "ts-node src/scripts/seed-twins.ts", "list-twins": "ts-node src/scripts/list-twins.ts", "migrate:farmers": "node src/scripts/migrateFarmers.js", "test:surveys": "node --loader ts-node/esm src/scripts/test-survey-api.ts", "init:travel-data": "ts-node src/scripts/initialize-travel-data.ts", "generate-qrcodes": "ts-node src/scripts/generate-qrcodes.ts", "test:minirent": "node src/app/minirent/apps/test-suite.js", "test:minirent:full": "./src/app/minirent/apps/test-runner.sh", "test:minirent:comprehensive": "node src/app/minirent/apps/run-all-tests.js", "test:minirent:clean": "node src/app/minirent/apps/database-cleanup.js --wipe-all --init-fresh", "test:minirent:backup": "node src/app/minirent/apps/database-cleanup.js --backup-only", "test:minirent:manager": "node src/app/minirent/apps/run-all-tests.js --app=manager --flows-only", "test:minirent:reception": "node src/app/minirent/apps/run-all-tests.js --app=reception --flows-only", "test:minirent:tenant": "node src/app/minirent/apps/run-all-tests.js --app=tenant --flows-only", "test:minirent:performance": "node src/app/minirent/apps/run-all-tests.js --performance", "test:minirent:interactive": "node src/app/minirent/apps/interactive-data-builder.js", "test:minirent:step-by-step": "node src/app/minirent/apps/step-by-step-tester.js", "test:minirent:thorough-clean": "node src/app/minirent/apps/thorough-cleanup.js", "test:minirent:verify-clean": "node src/app/minirent/apps/verify-cleanup.js", "test:env": "node scripts/test-env-vars.cjs"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@apollo/server": "^4.11.3", "@appsignal/javascript": "^1.3.9", "@appsignal/nextjs": "^1.0.0", "@as-integrations/next": "^3.2.0", "@auth/prisma-adapter": "^2.8.0", "@aws-sdk/client-s3": "^3.758.0", "@aws-sdk/s3-request-presigner": "^3.758.0", "@baseai/core": "^0.9.43", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@graphql-tools/schema": "^10.0.21", "@headlessui/react": "^2.2.3", "@hello-pangea/dnd": "^18.0.1", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^4.1.2", "@microsoft/microsoft-graph-client": "^3.0.7", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@next-auth/prisma-adapter": "^1.0.7", "@opentelemetry/api": "^1.8.0", "@opentelemetry/auto-instrumentations-node": "^0.43.0", "@opentelemetry/exporter-jaeger": "^2.0.0", "@opentelemetry/exporter-trace-otlp-http": "^0.53.0", "@opentelemetry/sdk-node": "^0.53.0", "@prisma/client": "^6.4.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.3", "@radix-ui/react-icons": "^1.3.1", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-primitive": "^2.0.2", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.1.8", "@tabler/icons-react": "^3.30.0", "@tanstack/react-query": "^5.28.5", "@tanstack/react-query-devtools": "^5.28.5", "@tanstack/react-table": "^8.21.2", "@types/file-saver": "^2.0.7", "@types/howler": "^2.2.12", "@types/imap": "^0.8.42", "@types/js-cookie": "^3.0.6", "@types/leaflet": "^1.9.16", "@types/lodash": "^4.17.15", "@types/next": "^9.0.0", "@types/next-pwa": "^5.6.9", "@types/nodemailer": "^6.4.17", "@types/pdfkit": "^0.13.9", "@types/pg": "^8.11.11", "@types/react-syntax-highlighter": "^15.5.13", "@types/sharp": "^0.32.0", "@types/uuid": "^10.0.0", "add": "^2.0.6", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "antd": "^5.26.4", "apexcharts": "^4.5.0", "axios": "^1.8.2", "bcrypt": "^5.1.1", "bcryptjs": "2.4.3", "better-sqlite3": "^11.8.1", "canvas-confetti": "^1.9.3", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "concurrently": "^9.1.0", "cookies-next": "^5.1.0", "country-flag-icons": "^1.5.18", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "embla-carousel-react": "^8.5.2", "file-saver": "^2.0.5", "firebase": "^11.10.0", "firebase-admin": "^13.4.0", "flatpickr": "^4.6.13", "framer-motion": "^12.15.0", "googleapis": "^146.0.0", "graphql": "^16.10.0", "graphql-tag": "^2.12.6", "howler": "^2.2.4", "html5-qrcode": "^2.3.8", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "imap": "^0.8.19", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.0", "jsvectormap": "^1.6.0", "leaflet": "^1.9.4", "lodash.debounce": "^4.0.8", "lodash.get": "^4.4.2", "lodash.set": "^4.3.2", "lodash.shuffle": "^4.2.0", "lucide-react": "^0.485.0", "mailparser": "^3.7.4", "meyda": "^5.6.3", "mock-aws-s3": "^4.0.2", "mongoose": "^8.10.1", "nanoid": "^5.1.5", "next": "^15.4.2", "next-auth": "^4.24.11", "next-pwa": "^5.6.0", "nock": "^14.0.1", "node-cron": "^4.2.1", "node-fetch": "^3.3.2", "nodemailer": "^7.0.3", "openai": "^5.1.1", "pdfkit": "^0.16.0", "pg": "^8.13.3", "qr-code-styling": "^1.9.2", "qrcode": "^1.5.4", "react": "19.0.0-rc-02c0e824-20241028", "react-apexcharts": "^1.7.0", "react-chartjs-2": "^5.3.0", "react-confetti": "^6.4.0", "react-day-picker": "9.5.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dnd-touch-backend": "^16.0.1", "react-dom": "19.0.0-rc-02c0e824-20241028", "react-dropzone": "^14.3.8", "react-feather": "^2.0.10", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-i18next": "^15.5.2", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-leaflet": "^5.0.0", "react-markdown": "^10.1.0", "react-share": "^5.2.2", "react-swipeable": "^7.0.2", "react-syntax-highlighter": "^15.6.1", "react-use": "^17.6.0", "recharts": "^2.15.3", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "separator": "^0.1.0", "shadcn": "^2.4.0-canary.9", "sharp": "^0.34.1", "slugify": "^1.6.6", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "three": "0.174.0", "tiktoken": "^1.0.21", "tldraw": "^3.12.1", "uuid": "^11.1.0", "wikijs": "^6.4.1", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/canvas-confetti": "^1.9.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/mailparser": "^3", "@types/node": "^20.17.44", "@types/qrcode": "^1.5.5", "@types/react": "^19.0.10", "@types/react-dnd": "^3.0.2", "@types/react-dnd-html5-backend": "^3.0.2", "@types/react-dnd-touch-backend": "^0.5.0", "@types/react-dom": "^19.0.4", "@types/three": "0.174.0", "eslint": "^8", "eslint-config-next": "15.0.2", "postcss": "^8", "prisma": "^6.4.1", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5", "vitest": "^3.0.8", "webpack-bundle-analyzer": "^4.10.2"}, "type": "module"}