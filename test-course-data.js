// Quick test to verify course data loading
import fs from 'fs';

// Load the data files
const coursesData = JSON.parse(fs.readFileSync('./data/apps/web/wise/eduwise/courses.json', 'utf8'));
const courseSectionsData = JSON.parse(fs.readFileSync('./data/apps/web/wise/eduwise/course-sections.json', 'utf8'));
const courseSectionItemsData = JSON.parse(fs.readFileSync('./data/apps/web/wise/eduwise/course-section-items.json', 'utf8'));
const lessonsData = JSON.parse(fs.readFileSync('./data/apps/web/wise/eduwise/lessons.json', 'utf8'));

console.log('Courses count:', coursesData.length);
console.log('Course sections count:', courseSectionsData.length);
console.log('Course section items count:', courseSectionItemsData.length);
console.log('Lessons count:', lessonsData.length);

// Test the first course
const firstCourse = coursesData[0];
console.log('\nFirst course:', firstCourse.name);
console.log('Course ID:', firstCourse.id);

// Find sections for first course
const courseSections = courseSectionsData.filter(section => section.course_id === firstCourse.id);
console.log('Sections for course 1:', courseSections.length);

if (courseSections.length > 0) {
  const firstSection = courseSections[0];
  console.log('First section:', firstSection.name);
  
  // Find items for first section
  const sectionItems = courseSectionItemsData.filter(item => 
    item.course_section_id === firstSection.id && !item.deleted_at
  );
  console.log('Items in first section:', sectionItems.length);
  
  sectionItems.forEach(item => {
    if (item.type === 1) {
      const lesson = lessonsData.find(l => l.id === item.item_id);
      console.log(`  Lesson ${item.item_id}: ${lesson ? lesson.name : 'NOT FOUND'}`);
    }
  });
}
