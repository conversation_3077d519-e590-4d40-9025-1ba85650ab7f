{"projects": [{"id": "project-001", "title": "E-commerce Platform Development", "description": "Development of a comprehensive e-commerce platform for retail clients", "status": "active", "priority": "high", "startDate": "2025-06-01T00:00:00.000Z", "endDate": "2025-12-31T23:59:59.000Z", "budget": 500000000, "currency": "VND", "clientId": "client-001", "projectManager": "emp-manager-001", "teamMembers": [{"employeeId": "emp-001", "role": "Senior Developer", "allocation": 100}, {"employeeId": "emp-002", "role": "Frontend Developer", "allocation": 80}, {"employeeId": "emp-003", "role": "UX Designer", "allocation": 60}], "milestones": [{"id": "milestone-001", "title": "Requirements Analysis", "description": "Complete requirements gathering and analysis", "dueDate": "2025-06-30T23:59:59.000Z", "status": "completed", "completedAt": "2025-06-28T15:00:00.000Z"}, {"id": "milestone-002", "title": "System Design", "description": "Complete system architecture and design", "dueDate": "2025-07-31T23:59:59.000Z", "status": "in_progress", "completedAt": null}, {"id": "milestone-003", "title": "Development Phase 1", "description": "Core functionality development", "dueDate": "2025-09-30T23:59:59.000Z", "status": "not_started", "completedAt": null}], "createdAt": "2025-06-01T10:00:00.000Z", "updatedAt": "2025-07-21T12:00:00.000Z", "tenantId": "default"}, {"id": "project-002", "title": "Data Analytics Dashboard", "description": "Business intelligence dashboard for client reporting", "status": "completed", "priority": "medium", "startDate": "2025-04-01T00:00:00.000Z", "endDate": "2025-06-30T23:59:59.000Z", "budget": 200000000, "currency": "VND", "clientId": "client-002", "projectManager": "emp-manager-002", "teamMembers": [{"employeeId": "emp-004", "role": "Data Scientist", "allocation": 100}, {"employeeId": "emp-005", "role": "Backend Developer", "allocation": 70}], "milestones": [{"id": "milestone-004", "title": "Data Analysis", "description": "Analyze client data requirements", "dueDate": "2025-04-30T23:59:59.000Z", "status": "completed", "completedAt": "2025-04-25T14:00:00.000Z"}, {"id": "milestone-005", "title": "Dashboard Development", "description": "Build interactive dashboard", "dueDate": "2025-06-15T23:59:59.000Z", "status": "completed", "completedAt": "2025-06-10T16:30:00.000Z"}, {"id": "milestone-006", "title": "Testing and Deployment", "description": "Test and deploy dashboard", "dueDate": "2025-06-30T23:59:59.000Z", "status": "completed", "completedAt": "2025-06-28T11:00:00.000Z"}], "createdAt": "2025-04-01T09:00:00.000Z", "updatedAt": "2025-06-30T17:00:00.000Z", "tenantId": "default"}], "lastUpdated": "2025-07-21T12:00:00.000Z", "version": "1.0.0"}