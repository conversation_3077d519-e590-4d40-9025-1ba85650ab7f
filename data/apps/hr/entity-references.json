{"entityReferences": [{"id": "ref-001", "sourceEntity": {"type": "job", "id": "job-001", "module": "hunter"}, "targetEntity": {"type": "application", "id": "app-001", "module": "hunter"}, "relationship": "has_application", "createdAt": "2025-07-20T15:30:00.000Z", "tenantId": "default"}, {"id": "ref-002", "sourceEntity": {"type": "employee", "id": "emp-001", "module": "hrm"}, "targetEntity": {"type": "performance_review", "id": "review-001", "module": "hrm"}, "relationship": "has_review", "createdAt": "2025-07-01T10:00:00.000Z", "tenantId": "default"}, {"id": "ref-003", "sourceEntity": {"type": "employee", "id": "emp-002", "module": "hrm"}, "targetEntity": {"type": "onboarding_record", "id": "record-002", "module": "onboarding"}, "relationship": "has_onboarding", "createdAt": "2025-07-15T08:00:00.000Z", "tenantId": "default"}, {"id": "ref-004", "sourceEntity": {"type": "project", "id": "project-001", "module": "projects"}, "targetEntity": {"type": "employee", "id": "emp-001", "module": "hrm"}, "relationship": "has_team_member", "createdAt": "2025-06-01T10:00:00.000Z", "tenantId": "default"}, {"id": "ref-005", "sourceEntity": {"type": "dispute", "id": "dispute-001", "module": "referee"}, "targetEntity": {"type": "evidence", "id": "evidence-001", "module": "referee"}, "relationship": "has_evidence", "createdAt": "2025-07-20T10:45:00.000Z", "tenantId": "default"}, {"id": "ref-006", "sourceEntity": {"type": "contract", "id": "contract-001", "module": "contracts"}, "targetEntity": {"type": "employee", "id": "emp-001", "module": "hrm"}, "relationship": "employs", "createdAt": "2025-01-10T14:00:00.000Z", "tenantId": "default"}, {"id": "ref-007", "sourceEntity": {"type": "gig", "id": "gig-001", "module": "upwork"}, "targetEntity": {"type": "proposal", "id": "proposal-001", "module": "upwork"}, "relationship": "has_proposal", "createdAt": "2025-07-18T11:20:00.000Z", "tenantId": "default"}, {"id": "ref-008", "sourceEntity": {"type": "rfp", "id": "rfp-001", "module": "rfp"}, "targetEntity": {"type": "rfp_proposal", "id": "rfp-proposal-001", "module": "rfp"}, "relationship": "has_proposal", "createdAt": "2025-07-16T09:30:00.000Z", "tenantId": "default"}], "lastUpdated": "2025-07-21T12:00:00.000Z", "version": "1.0.0"}