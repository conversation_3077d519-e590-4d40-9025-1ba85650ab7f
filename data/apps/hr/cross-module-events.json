{"events": [{"id": "event-001", "type": "job_application_submitted", "sourceModule": "hunter", "targetModules": ["hrm", "recruitment"], "data": {"jobId": "job-001", "candidateId": "candidate-001", "applicationId": "app-001"}, "timestamp": "2025-07-20T15:30:00.000Z", "processed": true, "processedAt": "2025-07-20T15:31:00.000Z", "tenantId": "default"}, {"id": "event-002", "type": "employee_onboarded", "sourceModule": "onboarding", "targetModules": ["hrm", "payroll"], "data": {"employeeId": "emp-002", "onboardingRecordId": "record-002", "completionDate": "2025-07-19T15:30:00.000Z"}, "timestamp": "2025-07-19T15:30:00.000Z", "processed": true, "processedAt": "2025-07-19T15:32:00.000Z", "tenantId": "default"}, {"id": "event-003", "type": "dispute_created", "sourceModule": "referee", "targetModules": ["hrm", "legal"], "data": {"disputeId": "dispute-001", "reportedBy": "freelancer-001", "reportedAgainst": "client-001", "type": "payment"}, "timestamp": "2025-07-20T10:30:00.000Z", "processed": false, "processedAt": null, "tenantId": "default"}, {"id": "event-004", "type": "performance_review_completed", "sourceModule": "hrm", "targetModules": ["payroll", "training"], "data": {"employeeId": "emp-001", "reviewId": "review-001", "overallRating": 4.2, "goals": ["goal-001", "goal-002"]}, "timestamp": "2025-07-01T10:00:00.000Z", "processed": true, "processedAt": "2025-07-01T10:05:00.000Z", "tenantId": "default"}, {"id": "event-005", "type": "project_milestone_completed", "sourceModule": "projects", "targetModules": ["hrm", "payroll"], "data": {"projectId": "project-001", "milestoneId": "milestone-001", "completedBy": ["emp-001", "emp-002", "emp-003"], "completionDate": "2025-06-28T15:00:00.000Z"}, "timestamp": "2025-06-28T15:00:00.000Z", "processed": true, "processedAt": "2025-06-28T15:10:00.000Z", "tenantId": "default"}], "lastUpdated": "2025-07-21T12:00:00.000Z", "version": "1.0.0"}