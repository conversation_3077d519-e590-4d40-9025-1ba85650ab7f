{"disputes": [{"id": "dispute-001", "title": "Payment Dispute - Project ABC", "description": "Freelancer claims payment was not received for completed work on Project ABC", "type": "payment", "status": "open", "priority": "high", "reportedBy": "freelancer-001", "reportedAgainst": "client-001", "createdAt": "2025-07-20T10:30:00.000Z", "updatedAt": "2025-07-20T10:30:00.000Z", "assignedTo": "referee-001", "evidence": ["evidence-001", "evidence-002"], "resolution": null, "tenantId": "default"}, {"id": "dispute-002", "title": "Work Quality Dispute", "description": "Client disputes the quality of delivered work and requests revision", "type": "quality", "status": "in_review", "priority": "medium", "reportedBy": "client-002", "reportedAgainst": "freelancer-002", "createdAt": "2025-07-19T14:15:00.000Z", "updatedAt": "2025-07-21T09:00:00.000Z", "assignedTo": "referee-002", "evidence": ["evidence-003"], "resolution": null, "tenantId": "default"}], "lastUpdated": "2025-07-21T12:00:00.000Z", "version": "1.0.0"}