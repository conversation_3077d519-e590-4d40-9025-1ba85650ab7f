{"applications": [{"id": "app-001", "jobId": "job-001", "candidateId": "candidate-001", "status": "pending", "appliedAt": "2025-07-20T15:30:00.000Z", "updatedAt": "2025-07-20T15:30:00.000Z", "coverLetter": "I am very interested in this position and believe my skills align well with your requirements.", "resumeUrl": "/uploads/resumes/candidate-001-resume.pdf", "tenantId": "default"}, {"id": "app-002", "jobId": "job-002", "candidateId": "candidate-002", "status": "interview_scheduled", "appliedAt": "2025-07-19T16:45:00.000Z", "updatedAt": "2025-07-21T10:00:00.000Z", "coverLetter": "I have extensive experience in data science and would love to contribute to your team.", "resumeUrl": "/uploads/resumes/candidate-002-resume.pdf", "interviewDate": "2025-07-22T14:00:00.000Z", "tenantId": "default"}, {"id": "app-003", "jobId": "job-003", "candidateId": "candidate-003", "status": "rejected", "appliedAt": "2025-07-18T11:20:00.000Z", "updatedAt": "2025-07-20T09:30:00.000Z", "coverLetter": "I am passionate about UX design and excited about this opportunity.", "resumeUrl": "/uploads/resumes/candidate-003-resume.pdf", "rejectionReason": "Experience level doesn't match requirements", "tenantId": "default"}], "lastUpdated": "2025-07-21T12:00:00.000Z", "version": "1.0.0"}