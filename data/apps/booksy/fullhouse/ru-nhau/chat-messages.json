[{"id": "msg-001", "chatId": "chat-activity-001", "activityId": "activity-001", "senderId": "user-001", "senderInfo": {"name": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "/images/users/an-nguyen.jpg", "apartment": "T1-1205"}, "message": "Mình đã book sân rồi nhé! 4h chiều bắt đầu", "messageType": "text", "timestamp": "2024-01-15T14:30:00Z", "autoDeleteAt": "2024-01-15T15:00:00Z", "isDeleted": false}, {"id": "msg-002", "chatId": "chat-activity-001", "activityId": "activity-001", "senderId": "user-002", "senderInfo": {"name": "<PERSON><PERSON>", "avatar": "/images/users/binh-le.jpg", "apartment": "T2-0815"}, "message": "Ok anh! Em mang vợt riêng nhé", "messageType": "text", "timestamp": "2024-01-15T14:32:00Z", "autoDeleteAt": "2024-01-15T15:02:00Z", "isDeleted": false}, {"id": "msg-003", "chatId": "chat-activity-001", "activityId": "activity-001", "senderId": "user-001", "senderInfo": {"name": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "/images/users/an-nguyen.jpg", "apartment": "T1-1205"}, "message": "Perfect! <PERSON><PERSON>n ai join nữa không nhỉ?", "messageType": "text", "timestamp": "2024-01-15T14:35:00Z", "autoDeleteAt": "2024-01-15T15:05:00Z", "isDeleted": false}, {"id": "msg-004", "chatId": "chat-activity-002", "activityId": "activity-002", "senderId": "user-003", "senderInfo": {"name": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "/images/users/cuong-tran.jpg", "apartment": "T3-1008"}, "message": "<PERSON>ình đi chợ mua thịt cá, ai cần gì cùng đi luôn nhé!", "messageType": "text", "timestamp": "2024-01-15T14:45:00Z", "autoDeleteAt": "2024-01-15T15:15:00Z", "isDeleted": false}, {"id": "msg-005", "chatId": "chat-activity-003", "activityId": "activity-003", "senderId": "user-004", "senderInfo": {"name": "<PERSON><PERSON><PERSON>", "avatar": "/images/users/dung-pham.jpg", "apartment": "T4-0612"}, "message": "Spa này massage đầu rấ<PERSON>, gi<PERSON> nhóm 200k/ngư<PERSON><PERSON> thôi", "messageType": "text", "timestamp": "2024-01-15T13:20:00Z", "autoDeleteAt": "2024-01-15T13:50:00Z", "isDeleted": false}, {"id": "msg-006", "chatId": "chat-activity-003", "activityId": "activity-003", "senderId": "user-005", "senderInfo": {"name": "<PERSON><PERSON><PERSON>", "avatar": "/images/users/mai-hoang.jpg", "apartment": "T4-0815"}, "message": "Wao rẻ quá! Em join nhé chị", "messageType": "text", "timestamp": "2024-01-15T13:25:00Z", "autoDeleteAt": "2024-01-15T13:55:00Z", "isDeleted": false}, {"id": "msg-007", "chatId": "chat-activity-003", "activityId": "activity-003", "senderId": "user-006", "senderInfo": {"name": "<PERSON><PERSON><PERSON><PERSON>", "avatar": "/images/users/lan-nguyen.jpg", "apartment": "T5-1203"}, "message": "Em cũng tham gia! Cần đặt cọc trước không chị?", "messageType": "text", "timestamp": "2024-01-15T13:28:00Z", "autoDeleteAt": "2024-01-15T13:58:00Z", "isDeleted": false}, {"id": "msg-008", "chatId": "chat-activity-003", "activityId": "activity-003", "senderId": "user-004", "senderInfo": {"name": "<PERSON><PERSON><PERSON>", "avatar": "/images/users/dung-pham.jpg", "apartment": "T4-0612"}, "message": "<PERSON><PERSON> <PERSON>, đặt cọc 50k/người. <PERSON><PERSON> chuyển khoản cho mọi người nhé", "messageType": "text", "timestamp": "2024-01-15T13:30:00Z", "autoDeleteAt": "2024-01-15T14:00:00Z", "isDeleted": false}, {"id": "msg-009", "chatId": "chat-activity-004", "activityId": "activity-004", "senderId": "user-007", "senderInfo": {"name": "<PERSON><PERSON>", "avatar": "/images/users/duc-le.jpg", "apartment": "T6-1105"}, "message": "<PERSON><PERSON><PERSON> bộ 5km quanh hồ, ai có thể theo kịp không? 😄", "messageType": "text", "timestamp": "2024-01-15T14:50:00Z", "autoDeleteAt": "2024-01-15T15:20:00Z", "isDeleted": false}, {"id": "msg-010", "chatId": "chat-activity-005", "activityId": "activity-005", "senderId": "user-008", "senderInfo": {"name": "<PERSON><PERSON>", "avatar": "/images/users/hoa-vu.jpg", "apartment": "T7-0920"}, "message": "Ngồi cafe Highland tầng trệt nhé, view đẹp lắm!", "messageType": "text", "timestamp": "2024-01-15T14:15:00Z", "autoDeleteAt": "2024-01-15T14:45:00Z", "isDeleted": false}, {"id": "msg-011", "chatId": "chat-activity-005", "activityId": "activity-005", "senderId": "user-009", "senderInfo": {"name": "Đặng <PERSON><PERSON><PERSON>", "avatar": "/images/users/hung-dang.jpg", "apartment": "T7-1015"}, "message": "Ok chị! <PERSON><PERSON> đang <PERSON>, x<PERSON><PERSON><PERSON> ngay", "messageType": "text", "timestamp": "2024-01-15T14:20:00Z", "autoDeleteAt": "2024-01-15T14:50:00Z", "isDeleted": false}, {"id": "msg-012", "chatId": "chat-activity-005", "activityId": "activity-005", "senderId": "user-008", "senderInfo": {"name": "<PERSON><PERSON>", "avatar": "/images/users/hoa-vu.jpg", "apartment": "T7-0920"}, "message": "Tuyệt! Mình order trước 2 ly cafe nhé", "messageType": "text", "timestamp": "2024-01-15T14:22:00Z", "autoDeleteAt": "2024-01-15T14:52:00Z", "isDeleted": false}]