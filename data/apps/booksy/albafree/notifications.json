[{"id": "notif-001", "userId": "user-002", "type": "bbeonjjeok_call", "title": "번쩍콜이 도착했습니다!", "message": "명동 카페 로스터리에서 즉시 근무 가능한 서빙 직원을 찾고 있습니다.", "data": {"jobId": "job-001", "employerId": "user-010", "urgency": "emergency"}, "isRead": false, "createdAt": "2024-01-15T11:08:00Z", "expiresAt": "2024-01-15T11:38:00Z", "priority": "high"}, {"id": "notif-002", "userId": "user-001", "type": "job_application_response", "title": "지원 결과 알림", "message": "카페 서빙 급구! 지원에 대한 응답이 도착했습니다.", "data": {"jobId": "job-001", "matchId": "match-001", "response": "pending"}, "isRead": false, "createdAt": "2024-01-15T11:10:00Z", "expiresAt": null, "priority": "normal"}, {"id": "notif-003", "userId": "user-010", "type": "new_application", "title": "새로운 지원자", "message": "김민수님이 '카페 서빙 급구!' 공고에 지원했습니다.", "data": {"jobId": "job-001", "workerId": "user-001", "matchId": "match-001"}, "isRead": true, "createdAt": "2024-01-15T11:05:00Z", "expiresAt": null, "priority": "normal"}]