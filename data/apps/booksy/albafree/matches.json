[{"id": "match-001", "jobId": "job-001", "workerId": "user-001", "employerId": "user-010", "status": "pending", "createdAt": "2024-01-15T11:05:00Z", "respondedAt": null, "acceptedAt": null, "completedAt": null, "type": "application", "bbeonjjeokCall": false, "workerResponse": null, "employerResponse": null, "rating": null, "review": null}, {"id": "match-002", "jobId": "job-001", "workerId": "user-002", "employerId": "user-010", "status": "pending", "createdAt": "2024-01-15T11:08:00Z", "respondedAt": null, "acceptedAt": null, "completedAt": null, "type": "bbeonjjeok_call", "bbeonjjeokCall": true, "workerResponse": null, "employerResponse": null, "rating": null, "review": null}, {"id": "match-003", "jobId": "job-003", "workerId": "user-002", "employerId": "user-012", "status": "accepted", "createdAt": "2024-01-14T16:30:00Z", "respondedAt": "2024-01-14T16:45:00Z", "acceptedAt": "2024-01-14T17:00:00Z", "completedAt": null, "type": "application", "bbeonjjeokCall": false, "workerResponse": "accepted", "employerResponse": "approved", "rating": null, "review": null}]