{"cuisineTypes": [{"id": "vietnamese", "name": "<PERSON><PERSON>", "description": "Ẩm thực truyền thống V<PERSON> Nam", "icon": "🇻🇳", "color": "bg-red-100 text-red-800", "isPopular": true}, {"id": "italian", "name": "Món Ý", "description": "Ẩm thực Ý ch<PERSON>h hiệu", "icon": "🇮🇹", "color": "bg-green-100 text-green-800", "isPopular": true}, {"id": "chinese", "name": "Món Trung <PERSON>", "description": "Ẩm thực Trung Hoa đa dạng", "icon": "🇨🇳", "color": "bg-yellow-100 text-yellow-800", "isPopular": true}, {"id": "japanese", "name": "<PERSON><PERSON>", "description": "Ẩm thực <PERSON>h tế", "icon": "🇯🇵", "color": "bg-pink-100 text-pink-800", "isPopular": true}, {"id": "korean", "name": "<PERSON><PERSON>", "description": "Ẩm thực <PERSON>c cay nồng", "icon": "🇰🇷", "color": "bg-orange-100 text-orange-800", "isPopular": true}, {"id": "thai", "name": "<PERSON><PERSON>", "description": "Ẩm thực Thái Lan chua cay", "icon": "🇹🇭", "color": "bg-purple-100 text-purple-800", "isPopular": false}, {"id": "western", "name": "<PERSON><PERSON>", "description": "Ẩm thực phương <PERSON>y", "icon": "🍽️", "color": "bg-blue-100 text-blue-800", "isPopular": true}, {"id": "indian", "name": "Món Ấn", "description": "Ẩm thực Ấn Độ đậm đà", "icon": "🇮🇳", "color": "bg-amber-100 text-amber-800", "isPopular": false}], "foodCategories": [{"id": "main_course", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> món ăn chính trong bữa ăn", "icon": "🍽️", "sortOrder": 1}, {"id": "appetizer", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON> ăn khai vị, nhẹ nhàng", "icon": "🥗", "sortOrder": 2}, {"id": "soup", "name": "Canh & Súp", "description": "<PERSON><PERSON><PERSON>, s<PERSON><PERSON>g", "icon": "🍲", "sortOrder": 3}, {"id": "dessert", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> tr<PERSON>, b<PERSON><PERSON>", "icon": "🍰", "sortOrder": 4}, {"id": "beverage", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>, c<PERSON> phê, trà", "icon": "🥤", "sortOrder": 5}, {"id": "snack", "name": "Đồ Ăn Vặt", "description": "Món ăn nhẹ, snack", "icon": "🍿", "sortOrder": 6}], "subcategories": [{"id": "noodles", "name": "Mì & Phở", "parentCategory": "main_course", "description": "<PERSON><PERSON><PERSON> lo<PERSON> mì, phở, bún"}, {"id": "rice", "name": "<PERSON><PERSON><PERSON>", "parentCategory": "main_course", "description": "<PERSON><PERSON><PERSON> m<PERSON> c<PERSON>m"}, {"id": "pizza", "name": "Pizza", "parentCategory": "main_course", "description": "Các loại pizza"}, {"id": "burger", "name": "Burger", "parentCategory": "main_course", "description": "Hamburger và sandwich"}, {"id": "fried_chicken", "name": "Gà Rán", "parentCategory": "main_course", "description": "Gà rán và gà nướng"}, {"id": "seafood", "name": "<PERSON><PERSON><PERSON>", "parentCategory": "main_course", "description": "<PERSON><PERSON><PERSON> m<PERSON> hải sản"}, {"id": "vegetarian", "name": "<PERSON><PERSON>", "parentCategory": "main_course", "description": "<PERSON><PERSON> ăn chay"}, {"id": "salad", "name": "Salad", "parentCategory": "appetizer", "description": "Các lo<PERSON>i salad tươi"}, {"id": "spring_rolls", "name": "<PERSON><PERSON>", "parentCategory": "appetizer", "description": "<PERSON><PERSON>, gỏi cuốn"}, {"id": "coffee", "name": "Cà <PERSON>", "parentCategory": "beverage", "description": "<PERSON><PERSON><PERSON> lo<PERSON>i cà phê"}, {"id": "milk_tea", "name": "<PERSON><PERSON><PERSON>", "parentCategory": "beverage", "description": "Trà sữa và bubble tea"}, {"id": "fresh_juice", "name": "<PERSON>ư<PERSON><PERSON>", "parentCategory": "beverage", "description": "<PERSON>ướ<PERSON> trái cây tư<PERSON>i"}, {"id": "cake", "name": "<PERSON><PERSON><PERSON>", "parentCategory": "dessert", "description": "<PERSON><PERSON><PERSON>em, b<PERSON>h ng<PERSON>t"}, {"id": "ice_cream", "name": "<PERSON><PERSON>", "parentCategory": "dessert", "description": "<PERSON><PERSON>"}], "dietaryRestrictions": [{"id": "vegetarian", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> thịt, c<PERSON>", "icon": "🥬", "color": "bg-green-100 text-green-800"}, {"id": "vegan", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> có sản phẩm động vật", "icon": "🌱", "color": "bg-green-100 text-green-800"}, {"id": "gluten_free", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> chứa gluten", "icon": "🌾", "color": "bg-yellow-100 text-yellow-800"}, {"id": "dairy_free", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON><PERSON> sản phẩm từ sữa", "icon": "🥛", "color": "bg-blue-100 text-blue-800"}, {"id": "nut_free", "name": "Không <PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON><PERSON> c<PERSON>c lo<PERSON> hạt", "icon": "🥜", "color": "bg-orange-100 text-orange-800"}, {"id": "halal", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON> thủ tiêu chuẩn <PERSON>", "icon": "☪️", "color": "bg-emerald-100 text-emerald-800"}, {"id": "low_carb", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON> bột", "icon": "🥩", "color": "bg-red-100 text-red-800"}, {"id": "keto", "name": "Keto", "description": "<PERSON><PERSON> độ ăn <PERSON>", "icon": "🥑", "color": "bg-purple-100 text-purple-800"}], "allergens": [{"id": "gluten", "name": "Gluten", "description": "<PERSON><PERSON> trong lúa mì, y<PERSON><PERSON> mạch", "severity": "high"}, {"id": "dairy", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> ph<PERSON>m từ sữa", "severity": "medium"}, {"id": "eggs", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Trứng g<PERSON>, vịt", "severity": "medium"}, {"id": "nuts", "name": "Hạ<PERSON>", "description": "<PERSON><PERSON><PERSON> hạt c<PERSON>", "severity": "high"}, {"id": "peanuts", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> phộng và sản phẩm từ đậu phộng", "severity": "high"}, {"id": "soy", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON>ậu nành và sản phẩm từ đậu nành", "severity": "medium"}, {"id": "fish", "name": "Cá", "description": "<PERSON>á và sản phẩm từ cá", "severity": "medium"}, {"id": "shellfish", "name": "<PERSON><PERSON><PERSON>", "description": "Tôm, cua, sò, ốc", "severity": "high"}, {"id": "sesame", "name": "Mè", "description": "Hạt mè và dầu mè", "severity": "medium"}], "spicyLevels": [{"level": 0, "name": "Không Cay", "description": "<PERSON><PERSON>n toàn không cay", "icon": "😊", "color": "bg-gray-100 text-gray-800"}, {"level": 1, "name": "Nhẹ", "description": "<PERSON><PERSON> nhẹ, dễ ăn", "icon": "🌶️", "color": "bg-green-100 text-green-800"}, {"level": 2, "name": "Vừa", "description": "<PERSON>ay vừa phải", "icon": "🌶️🌶️", "color": "bg-yellow-100 text-yellow-800"}, {"level": 3, "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> cay", "icon": "🌶️🌶️🌶️", "color": "bg-orange-100 text-orange-800"}, {"level": 4, "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> ca<PERSON>, cần cân nh<PERSON>c", "icon": "🌶️🌶️🌶️🌶️", "color": "bg-red-100 text-red-800"}, {"level": 5, "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> cay, chỉ dành cho người chịu đư<PERSON> cay", "icon": "🔥🔥🔥", "color": "bg-red-200 text-red-900"}], "priceRanges": [{"id": "budget", "name": "Bình Dân", "description": "Dưới 100.000đ", "minPrice": 0, "maxPrice": 100000, "icon": "💰", "color": "bg-green-100 text-green-800"}, {"id": "mid_range", "name": "<PERSON><PERSON>", "description": "100.000đ - 300.000đ", "minPrice": 100000, "maxPrice": 300000, "icon": "💰💰", "color": "bg-yellow-100 text-yellow-800"}, {"id": "premium", "name": "<PERSON>", "description": "300.000đ - 500.000đ", "minPrice": 300000, "maxPrice": 500000, "icon": "💰💰💰", "color": "bg-orange-100 text-orange-800"}, {"id": "luxury", "name": "<PERSON>", "description": "Trên 500.000đ", "minPrice": 500000, "maxPrice": null, "icon": "💎", "color": "bg-purple-100 text-purple-800"}]}