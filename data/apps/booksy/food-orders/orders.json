[{"id": "food-order-001", "orderNumber": "FO240115001", "customerId": "customer-001", "customerInfo": {"name": "<PERSON><PERSON><PERSON><PERSON>", "phone": "+84 912 345 678", "email": "<EMAIL>", "avatar": "/images/customers/an-nguyen.jpg"}, "restaurantId": "restaurant-001", "restaurantInfo": {"name": "Phở <PERSON><PERSON>", "phone": "+84 24 3825 1234", "address": "123 <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>"}, "providerId": "provider-food-001", "orderType": "delivery", "items": [{"id": "order-item-001", "menuItemId": "menu-item-001", "name": "Phở <PERSON><PERSON>i", "variantId": "variant-002", "variantName": "<PERSON>ze <PERSON>", "quantity": 2, "unitPrice": 85000, "totalPrice": 170000, "customizations": [{"id": "custom-001", "name": "<PERSON><PERSON><PERSON> độ chín của thịt", "selectedOption": {"id": "rare", "name": "<PERSON><PERSON><PERSON>", "priceAdjustment": 0}}, {"id": "custom-002", "name": "<PERSON><PERSON>ê<PERSON>", "selectedOptions": [{"id": "extra_herbs", "name": "<PERSON><PERSON><PERSON><PERSON> rau thơm", "priceAdjustment": 5000}, {"id": "bean_sprouts", "name": "Giá đỗ", "priceAdjustment": 5000}]}], "specialInstructions": "<PERSON><PERSON>, kh<PERSON>ng hành tây", "itemTotal": 190000}, {"id": "order-item-002", "menuItemId": "menu-item-001", "name": "Phở <PERSON><PERSON>i", "variantId": "variant-001", "variantName": "Size Nhỏ", "quantity": 1, "unitPrice": 65000, "totalPrice": 65000, "customizations": [], "specialInstructions": "", "itemTotal": 65000}], "pricing": {"subtotal": 255000, "deliveryFee": 15000, "serviceFee": 5000, "discount": 51000, "discountReason": "Giảm 20% đơn hàng đầu tiên", "tax": 0, "total": 224000, "currency": "VND"}, "deliveryInfo": {"address": {"street": "Tòa T1, Times City", "ward": "<PERSON>", "district": "Hai Bà Trưng", "city": "<PERSON><PERSON>", "fullAddress": "Tòa T1, Times City, Minh <PERSON>, <PERSON>, <PERSON><PERSON>", "coordinates": {"latitude": 20.9967, "longitude": 105.8686}}, "deliveryInstructions": "<PERSON><PERSON><PERSON> điện khi đến tầng trệt, sẽ xuống nhận", "estimatedDeliveryTime": {"min": 25, "max": 35}, "actualDeliveryTime": null, "deliveryPersonId": "delivery-001", "deliveryPersonInfo": {"name": "<PERSON><PERSON><PERSON><PERSON>", "phone": "+84 ***********", "vehicle": "motorbike", "licensePlate": "29A1-12345"}}, "paymentInfo": {"method": "momo", "status": "paid", "transactionId": "MOMO240115001", "paidAt": "2024-01-15T10:15:00Z", "amount": 224000}, "timeline": {"orderedAt": "2024-01-15T10:15:00Z", "confirmedAt": "2024-01-15T10:17:00Z", "preparingAt": "2024-01-15T10:18:00Z", "readyAt": "2024-01-15T10:28:00Z", "pickedUpAt": "2024-01-15T10:30:00Z", "deliveredAt": "2024-01-15T10:52:00Z", "completedAt": "2024-01-15T10:52:00Z"}, "status": "completed", "statusHistory": [{"status": "pending", "timestamp": "2024-01-15T10:15:00Z", "note": "<PERSON><PERSON><PERSON> hàng đ<PERSON><PERSON><PERSON> t<PERSON>o"}, {"status": "confirmed", "timestamp": "2024-01-15T10:17:00Z", "note": "<PERSON><PERSON><PERSON> hàng xác nhận đơn hàng"}, {"status": "preparing", "timestamp": "2024-01-15T10:18:00Z", "note": "<PERSON><PERSON><PERSON> đ<PERSON>u chuẩn bị món ăn"}, {"status": "ready", "timestamp": "2024-01-15T10:28:00Z", "note": "Món ăn đã sẵn sàng"}, {"status": "picked_up", "timestamp": "2024-01-15T10:30:00Z", "note": "Shipper đã nhận hàng"}, {"status": "out_for_delivery", "timestamp": "2024-01-15T10:31:00Z", "note": "<PERSON><PERSON> giao hàng"}, {"status": "delivered", "timestamp": "2024-01-15T10:52:00Z", "note": "<PERSON><PERSON><PERSON> hàng thành công"}, {"status": "completed", "timestamp": "2024-01-15T10:52:00Z", "note": "<PERSON><PERSON><PERSON> hàng hoàn thành"}], "rating": {"foodRating": 5, "deliveryRating": 4, "overallRating": 4.5, "review": "Phở ngon, nước dùng đậm đà. <PERSON><PERSON><PERSON> hàng hơi chậm một chút.", "reviewDate": "2024-01-15T11:30:00Z"}, "promotions": [{"id": "promo-001", "title": "Giảm 20% đơn hàng đầu tiên", "discountAmount": 51000}], "specialRequests": ["<PERSON><PERSON><PERSON><PERSON> cần đũa, có sẵn ở nhà", "<PERSON><PERSON><PERSON> r<PERSON><PERSON>"], "isFirstOrder": true, "estimatedPreparationTime": 15, "actualPreparationTime": 10, "createdAt": "2024-01-15T10:15:00Z", "updatedAt": "2024-01-15T10:52:00Z"}, {"id": "food-order-002", "orderNumber": "FO240115002", "customerId": "customer-002", "customerInfo": {"name": "<PERSON><PERSON>", "phone": "+84 913 456 789", "email": "<EMAIL>", "avatar": "/images/customers/binh-le.jpg"}, "restaurantId": "restaurant-002", "restaurantInfo": {"name": "Pizza Italia Express", "phone": "+84 24 3825 5678", "address": "456 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>"}, "providerId": "provider-food-002", "orderType": "delivery", "items": [{"id": "order-item-003", "menuItemId": "menu-item-002", "name": "Pizza Margherita", "variantId": "variant-004", "variantName": "Size M (25cm)", "quantity": 2, "unitPrice": 259000, "totalPrice": 518000, "customizations": [{"id": "custom-004", "name": "Đế pizza", "selectedOption": {"id": "thick_crust", "name": "Đế dày", "priceAdjustment": 15000}}, {"id": "custom-005", "name": "Topping thêm", "selectedOptions": [{"id": "extra_cheese", "name": "Thêm phô mai", "priceAdjustment": 20000}, {"id": "mushrooms", "name": "<PERSON><PERSON><PERSON>", "priceAdjustment": 15000}]}], "specialInstructions": "Nướng giòn", "itemTotal": 588000}], "pricing": {"subtotal": 588000, "deliveryFee": 20000, "serviceFee": 10000, "discount": 196000, "discountReason": "Mua 2 tặng 1 Pizza size M", "tax": 0, "total": 422000, "currency": "VND"}, "deliveryInfo": {"address": {"street": "Tòa R3, Royal City", "ward": "<PERSON><PERSON> Xuân Trung", "district": "<PERSON><PERSON>", "city": "<PERSON><PERSON>", "fullAddress": "Tòa R3, Royal City, Thanh Xuân Trung, <PERSON>h <PERSON>ân, <PERSON><PERSON>", "coordinates": {"latitude": 21.0122, "longitude": 105.8019}}, "deliveryInstructions": "<PERSON><PERSON> ở bàn bảo vệ tầng trệt", "estimatedDeliveryTime": {"min": 30, "max": 40}, "actualDeliveryTime": null, "deliveryPersonId": "delivery-002", "deliveryPersonInfo": {"name": "<PERSON><PERSON><PERSON>", "phone": "+84 ***********", "vehicle": "motorbike", "licensePlate": "30A2-67890"}}, "paymentInfo": {"method": "cash", "status": "pending", "transactionId": null, "paidAt": null, "amount": 422000}, "timeline": {"orderedAt": "2024-01-15T14:20:00Z", "confirmedAt": "2024-01-15T14:22:00Z", "preparingAt": "2024-01-15T14:23:00Z", "readyAt": null, "pickedUpAt": null, "deliveredAt": null, "completedAt": null}, "status": "preparing", "statusHistory": [{"status": "pending", "timestamp": "2024-01-15T14:20:00Z", "note": "<PERSON><PERSON><PERSON> hàng đ<PERSON><PERSON><PERSON> t<PERSON>o"}, {"status": "confirmed", "timestamp": "2024-01-15T14:22:00Z", "note": "<PERSON><PERSON><PERSON> hàng xác nhận đơn hàng"}, {"status": "preparing", "timestamp": "2024-01-15T14:23:00Z", "note": "<PERSON><PERSON><PERSON> đ<PERSON>u chuẩn bị món ăn"}], "rating": null, "promotions": [{"id": "promo-002", "title": "Mua 2 tặng 1 Pizza size M", "discountAmount": 196000}], "specialRequests": ["<PERSON><PERSON>t thành 8 miếng", "<PERSON><PERSON><PERSON> riêng sốt cà chua"], "isFirstOrder": false, "estimatedPreparationTime": 18, "actualPreparationTime": null, "createdAt": "2024-01-15T14:20:00Z", "updatedAt": "2024-01-15T14:23:00Z"}]