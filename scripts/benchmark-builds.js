#!/usr/bin/env node

/**
 * Build Performance Benchmark Script
 * 
 * This script compares build performance between Webpack and Turbopack
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class BuildBenchmark {
  constructor() {
    this.results = {
      webpack: {},
      turbopack: {},
      comparison: {}
    };
  }

  async runBenchmark() {
    console.log('🚀 Starting Build Performance Benchmark\n');
    
    // Clean cache before benchmarking
    this.cleanCache();
    
    // Benchmark Webpack
    console.log('📦 Benchmarking Webpack build...');
    this.results.webpack = await this.benchmarkWebpack();
    
    // Clean cache between builds
    this.cleanCache();
    
    // Benchmark Turbopack
    console.log('⚡ Benchmarking Turbopack build...');
    this.results.turbopack = await this.benchmarkTurbopack();
    
    // Calculate comparison
    this.calculateComparison();
    
    // Generate report
    this.generateReport();
  }

  cleanCache() {
    console.log('🧹 Cleaning build cache...');
    try {
      execSync('rm -rf .next', { stdio: 'pipe' });
    } catch (error) {
      // Cache might not exist, ignore error
    }
  }

  async benchmarkWebpack() {
    const startTime = Date.now();
    
    try {
      const output = execSync('npm run build', { 
        stdio: 'pipe',
        encoding: 'utf8',
        timeout: 600000 // 10 minutes timeout
      });
      
      const endTime = Date.now();
      const buildTime = endTime - startTime;
      
      // Parse build output for additional metrics
      const metrics = this.parseBuildOutput(output);
      
      return {
        success: true,
        buildTime,
        ...metrics,
        output: output.slice(-1000) // Last 1000 chars
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        buildTime: Date.now() - startTime
      };
    }
  }

  async benchmarkTurbopack() {
    const startTime = Date.now();
    
    try {
      const output = execSync('npm run build:turbo', { 
        stdio: 'pipe',
        encoding: 'utf8',
        timeout: 600000 // 10 minutes timeout
      });
      
      const endTime = Date.now();
      const buildTime = endTime - startTime;
      
      // Parse build output for additional metrics
      const metrics = this.parseBuildOutput(output);
      
      return {
        success: true,
        buildTime,
        ...metrics,
        output: output.slice(-1000) // Last 1000 chars
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        buildTime: Date.now() - startTime
      };
    }
  }

  parseBuildOutput(output) {
    const metrics = {};
    
    // Extract bundle size information
    const sizeMatch = output.match(/Total size:\s*(\d+(?:\.\d+)?)\s*(\w+)/);
    if (sizeMatch) {
      metrics.bundleSize = `${sizeMatch[1]} ${sizeMatch[2]}`;
    }
    
    // Extract page count
    const pageMatch = output.match(/(\d+)\s*static pages/);
    if (pageMatch) {
      metrics.staticPages = parseInt(pageMatch[1]);
    }
    
    // Extract warnings/errors
    const warningMatches = output.match(/⚠/g);
    metrics.warnings = warningMatches ? warningMatches.length : 0;
    
    const errorMatches = output.match(/✗|Error:/g);
    metrics.errors = errorMatches ? errorMatches.length : 0;
    
    return metrics;
  }

  calculateComparison() {
    if (this.results.webpack.success && this.results.turbopack.success) {
      const webpackTime = this.results.webpack.buildTime;
      const turbopackTime = this.results.turbopack.buildTime;
      
      this.results.comparison = {
        speedImprovement: ((webpackTime - turbopackTime) / webpackTime * 100).toFixed(1),
        timeSaved: webpackTime - turbopackTime,
        turbopackFaster: turbopackTime < webpackTime
      };
    }
  }

  generateReport() {
    console.log('\n📊 Build Performance Report');
    console.log('=' .repeat(50));
    
    // Webpack results
    console.log('\n📦 Webpack Results:');
    if (this.results.webpack.success) {
      console.log(`  ✅ Build Time: ${this.formatTime(this.results.webpack.buildTime)}`);
      console.log(`  📄 Static Pages: ${this.results.webpack.staticPages || 'N/A'}`);
      console.log(`  ⚠️  Warnings: ${this.results.webpack.warnings || 0}`);
      console.log(`  ❌ Errors: ${this.results.webpack.errors || 0}`);
    } else {
      console.log(`  ❌ Build Failed: ${this.results.webpack.error}`);
    }
    
    // Turbopack results
    console.log('\n⚡ Turbopack Results:');
    if (this.results.turbopack.success) {
      console.log(`  ✅ Build Time: ${this.formatTime(this.results.turbopack.buildTime)}`);
      console.log(`  📄 Static Pages: ${this.results.turbopack.staticPages || 'N/A'}`);
      console.log(`  ⚠️  Warnings: ${this.results.turbopack.warnings || 0}`);
      console.log(`  ❌ Errors: ${this.results.turbopack.errors || 0}`);
    } else {
      console.log(`  ❌ Build Failed: ${this.results.turbopack.error}`);
    }
    
    // Comparison
    if (this.results.comparison.speedImprovement) {
      console.log('\n🏆 Performance Comparison:');
      if (this.results.comparison.turbopackFaster) {
        console.log(`  🚀 Turbopack is ${this.results.comparison.speedImprovement}% faster`);
        console.log(`  ⏱️  Time saved: ${this.formatTime(this.results.comparison.timeSaved)}`);
      } else {
        console.log(`  📦 Webpack is ${Math.abs(this.results.comparison.speedImprovement)}% faster`);
      }
    }
    
    // Save detailed results
    this.saveResults();
    
    console.log('\n📝 Detailed results saved to: benchmark-results.json');
  }

  formatTime(milliseconds) {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    const remainingMs = milliseconds % 1000;
    
    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    } else if (seconds > 0) {
      return `${seconds}.${Math.floor(remainingMs / 100)}s`;
    } else {
      return `${milliseconds}ms`;
    }
  }

  saveResults() {
    const resultsPath = path.join(process.cwd(), 'benchmark-results.json');
    const detailedResults = {
      timestamp: new Date().toISOString(),
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch
      },
      ...this.results
    };
    
    fs.writeFileSync(resultsPath, JSON.stringify(detailedResults, null, 2));
  }
}

// Run benchmark if called directly
if (require.main === module) {
  const benchmark = new BuildBenchmark();
  benchmark.runBenchmark().catch(console.error);
}

module.exports = BuildBenchmark;
