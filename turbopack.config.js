/**
 * Turbopack Configuration for ABN.green
 * 
 * This file contains configuration settings for Turbopack migration.
 * Use this to manage the transition from Webpack to Turbopack.
 */

const turbopackConfig = {
  // Development settings
  development: {
    enabled: true,
    port: 3000,
    features: {
      fastRefresh: true,
      hotReload: true,
      optimizedImports: true,
    }
  },

  // Production settings
  production: {
    enabled: false, // Set to true when Turbopack production is stable
    experimental: true,
    features: {
      bundleAnalyzer: false,
      sourceMap: true,
      minification: true,
    }
  },

  // External packages that should be handled by Node.js
  serverExternalPackages: [
    'fs',
    'fs/promises',
    'path',
    'crypto',
    'net',
    'tls',
    'dns',
    'pg',
    'pg-native',
    'sqlite3',
    'bcrypt',
    'nock',
    'mongodb-client-encryption',
    'aws4',
    'snappy',
    'kerberos',
    '@mongodb-js/zstd',
    'gcp-metadata',
    'socks',
  ],

  // Turbopack-specific rules
  rules: {
    '*.svg': {
      loaders: ['@svgr/webpack'],
      as: '*.js',
    },
  },

  // Resolve aliases
  resolveAlias: {
    '@': './src',
  },

  // Performance optimizations
  optimizations: {
    // Package imports to optimize
    optimizePackageImports: [
      'lucide-react',
      '@radix-ui/react-icons',
      'react-icons',
      'lodash',
    ],
    
    // Tree shaking settings
    treeShaking: true,
    
    // Code splitting
    codeSplitting: {
      chunks: 'all',
      automaticNameDelimiter: '~',
    }
  },

  // Migration status
  migration: {
    status: 'in-progress',
    developmentReady: true,
    productionReady: false,
    lastUpdated: new Date().toISOString(),
    notes: [
      'Development with Turbopack is working and significantly faster',
      'Production builds with Turbopack are experimental and have issues',
      'Using hybrid approach: Turbopack for dev, Webpack for production',
      'Need to resolve server-side import issues for full Turbopack adoption'
    ]
  }
};

module.exports = turbopackConfig;
