const fs = require('fs');
const path = require('path');

// Path to the lessons.json file
const lessonsFilePath = path.join(__dirname, 'data/apps/web/wise/eduwise/lessons.json');

try {
  // Read the current lessons.json file
  console.log('Reading lessons.json file...');
  const lessonsData = fs.readFileSync(lessonsFilePath, 'utf8');
  
  // Parse the JSON data
  const lessons = JSON.parse(lessonsData);
  
  console.log(`Found ${lessons.length} lessons in the file.`);
  
  // Count how many lessons have non-empty content
  const lessonsWithContent = lessons.filter(lesson => lesson.content && lesson.content.trim() !== '');
  console.log(`${lessonsWithContent.length} lessons have non-empty content.`);
  
  // Set all content fields to empty string
  let updatedCount = 0;
  lessons.forEach(lesson => {
    if (lesson.content && lesson.content.trim() !== '') {
      lesson.content = '';
      updatedCount++;
    } else if (!Object.prototype.hasOwnProperty.call(lesson, 'content')) {
      // Add content field if it doesn't exist
      lesson.content = '';
      updatedCount++;
    }
  });
  
  console.log(`Updated ${updatedCount} lessons.`);
  
  // Write the updated data back to the file
  console.log('Writing updated data back to lessons.json...');
  fs.writeFileSync(lessonsFilePath, JSON.stringify(lessons, null, 2), 'utf8');
  
  console.log('✅ Successfully cleared all lesson content fields!');
  
  // Verify the changes
  const updatedData = JSON.parse(fs.readFileSync(lessonsFilePath, 'utf8'));
  const remainingContent = updatedData.filter(lesson => lesson.content && lesson.content.trim() !== '');
  
  if (remainingContent.length === 0) {
    console.log('✅ Verification successful: All content fields are now empty.');
  } else {
    console.log(`⚠️  Warning: ${remainingContent.length} lessons still have content.`);
  }
  
} catch (error) {
  console.error('❌ Error processing lessons.json:', error.message);
  process.exit(1);
}
