@import "react-day-picker/dist/style.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out forwards;
  }
  
  .animate-fade-out {
    animation: fadeOut 0.3s ease-in-out forwards;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@import 'leaflet/dist/leaflet.css';


/* Center pin animation */
.center-pin-container {
  pointer-events: none;
  transition: transform 0.1s ease-out;
}

.center-pin-container svg {
  filter: drop-shadow(0 2px 2px rgba(0, 0, 0, 0.3));
}

/* Optional bounce animation when map stops moving */
@keyframes pinBounce {
  0%, 100% { transform: translate(-50%, -100%); }
  50% { transform: translate(-50%, -110%); }
}

.map-moving .center-pin-container > div {
  animation: none;
}

.center-pin-container > div {
  animation: pinBounce 0.3s ease-out;
}

/* Layer control styling */
.leaflet-control-layers {
  border: none !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
}

.leaflet-control-layers-base {
  padding: 6px !important;
}

.leaflet-control-layers-base label {
  margin-bottom: 4px !important;
}

/* Dark mode support for layer control */
@media (prefers-color-scheme: dark) {
  .leaflet-control-layers {
    background-color: #1f2937 !important;
    color: white !important;
  }
  
  .leaflet-control-layers-base label {
    color: white !important;
  }
}

/* Prevent text selection during scrolling */
.number-scroller {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Hide scrollbar */
.number-scroller::-webkit-scrollbar {
  display: none;
}

/* Add the animation for notifications */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}

/* Markdown content styling in chat messages */
.markdown-content {
  line-height: 1.6;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1em;
  margin-bottom: 0.5em;
}

.markdown-content h1:first-child,
.markdown-content h2:first-child,
.markdown-content h3:first-child,
.markdown-content h4:first-child,
.markdown-content h5:first-child,
.markdown-content h6:first-child {
  margin-top: 0;
}

.markdown-content p:last-child {
  margin-bottom: 0;
}

.markdown-content ul:last-child,
.markdown-content ol:last-child {
  margin-bottom: 0;
}

/* Code highlighting styles */
.markdown-content pre code {
  color: inherit;
  background: none;
  padding: 0;
}

/* Syntax highlighting fallback */
.markdown-content .hljs {
  background: #1f2937 !important;
  color: #f9fafb !important;
  border-radius: 0.5rem;
}

.markdown-content .hljs-keyword {
  color: #8b5cf6 !important;
}

.markdown-content .hljs-string {
  color: #10b981 !important;
}

.markdown-content .hljs-number {
  color: #f59e0b !important;
}

.markdown-content .hljs-comment {
  color: #6b7280 !important;
  font-style: italic;
}