'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { 
  ArrowRight, 
  Lightbulb, 
  Banknote, 
  DollarSign, 
  Info, 
  BarChart, 
  RotateCcw,
  Trophy
} from 'lucide-react';

interface Scenario {
  id: number;
  title: string;
  description: string;
  optionA: string;
  optionB: string;
  optionAResult: string;
  optionBResult: string;
  optionAValue: number;
  optionBValue: number;
  expectedBias: 'A' | 'B';  // Which option people typically choose due to loss aversion
  explanation: string;
}

export const LossAversionGame: React.FC = () => {
  const [gameStarted, setGameStarted] = useState(false);
  const [gameEnded, setGameEnded] = useState(false);
  const [currentScenarioIndex, setCurrentScenarioIndex] = useState(0);
  const [selectedOption, setSelectedOption] = useState<'A' | 'B' | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [portfolio, setPortfolio] = useState(10000);
  const [score, setScore] = useState(0);
  const [biasScore, setBiasScore] = useState(0);
  const [showExplanation, setShowExplanation] = useState(false);
  
  const scenarios: Scenario[] = [
    {
      id: 1,
      title: "Investment Choice",
      description: "You have two investment options:",
      optionA: "Option A: A guaranteed gain of $500",
      optionB: "Option B: A 50% chance to gain $1,100 and a 50% chance to gain nothing",
      optionAResult: "You gained $500 safely.",
      optionBResult: "The coin flip determined you would gain...",
      optionAValue: 500,
      optionBValue: 0, // Will be randomly determined
      expectedBias: 'A',
      explanation: "Most people choose the guaranteed gain (Option A) even though Option B has a higher expected value ($550). This demonstrates loss aversion - we prefer certainty when facing potential gains."
    },
    {
      id: 2,
      title: "Loss Scenario",
      description: "You must choose between two options:",
      optionA: "Option A: A guaranteed loss of $500",
      optionB: "Option B: A 50% chance to lose $1,100 and a 50% chance to lose nothing",
      optionAResult: "You lost $500.",
      optionBResult: "The coin flip determined you would lose...",
      optionAValue: -500,
      optionBValue: 0, // Will be randomly determined
      expectedBias: 'B',
      explanation: "Most people choose the risky option (B) when facing losses, even though both options have the same expected value. This is loss aversion - we're willing to take risks to avoid certain losses."
    },
    {
      id: 3,
      title: "Stock Market Decision",
      description: "Your stock has decreased in value by 15%. You can:",
      optionA: "Option A: Sell now and accept the 15% loss",
      optionB: "Option B: Hold the stock with a 50% chance of recovering (+5%) and 50% chance of further decline (-25%)",
      optionAResult: "You sold and accepted the 15% loss ($1,500).",
      optionBResult: "The market shifted and your decision resulted in...",
      optionAValue: -1500,
      optionBValue: 0, // Will be randomly determined
      expectedBias: 'B',
      explanation: "Loss aversion often makes investors hold losing stocks too long. The certainty of realizing a loss feels worse than the possibility of an even larger loss in the future."
    },
    {
      id: 4,
      title: "Bonus Structure",
      description: "Your company offers you two bonus options:",
      optionA: "Option A: A guaranteed bonus of $2,000",
      optionB: "Option B: A bonus that's tied to company performance: 75% chance of $3,000 or 25% chance of $0",
      optionAResult: "You received a guaranteed $2,000 bonus.",
      optionBResult: "Based on company performance, your bonus is...",
      optionAValue: 2000,
      optionBValue: 0, // Will be randomly determined
      expectedBias: 'A',
      explanation: "The expected value of Option B is $2,250, but most people choose the guaranteed $2,000. Loss aversion makes us unwilling to risk getting nothing, even when the expected outcome is better."
    },
    {
      id: 5,
      title: "Insurance Decision",
      description: "You're buying a $1,000 electronic device. You can:",
      optionA: "Option A: Pay $200 for an extended warranty that covers all repairs for 3 years",
      optionB: "Option B: Skip the warranty and deal with potential repair costs if they occur (15% chance of a $600 repair)",
      optionAResult: "You paid $200 for peace of mind with the warranty.",
      optionBResult: "Without a warranty, over 3 years you experienced...",
      optionAValue: -200,
      optionBValue: 0, // Will be randomly determined
      expectedBias: 'A',
      explanation: "The expected cost of repairs is only $90 (15% × $600), so the $200 warranty is overpriced. Yet loss aversion drives many people to buy warranties because the possibility of a larger expense feels worse than the certain smaller expense."
    }
  ];

  useEffect(() => {
    // Determine random outcomes for option B in each scenario
    scenarios.forEach(scenario => {
      if (scenario.id === 1) {
        // 50% chance of gaining $1100
        scenario.optionBValue = Math.random() < 0.5 ? 1100 : 0;
      } else if (scenario.id === 2) {
        // 50% chance of losing $1100
        scenario.optionBValue = Math.random() < 0.5 ? -1100 : 0;
      } else if (scenario.id === 3) {
        // 50% chance of +5% or -25%
        scenario.optionBValue = Math.random() < 0.5 ? 500 : -2500;
      } else if (scenario.id === 4) {
        // 75% chance of $3000 bonus
        scenario.optionBValue = Math.random() < 0.75 ? 3000 : 0;
      } else if (scenario.id === 5) {
        // 15% chance of a $600 repair
        scenario.optionBValue = Math.random() < 0.15 ? -600 : 0;
      }
    });
  }, []);

  const startGame = () => {
    setGameStarted(true);
    setGameEnded(false);
    setCurrentScenarioIndex(0);
    setPortfolio(10000);
    setScore(0);
    setBiasScore(0);
    setSelectedOption(null);
    setShowResult(false);
    setShowExplanation(false);
  };

  const restartGame = () => {
    startGame();
  };

  const handleOptionSelect = (option: 'A' | 'B') => {
    setSelectedOption(option);
    setShowResult(true);
    
    const currentScenario = scenarios[currentScenarioIndex];
    
    // Update portfolio based on selection
    if (option === 'A') {
      setPortfolio(prev => prev + currentScenario.optionAValue);
    } else {
      setPortfolio(prev => prev + currentScenario.optionBValue);
    }
    
    // Score based on expected value
    const optionAExpectedValue = currentScenario.optionAValue;
    let optionBExpectedValue = 0;
    
    if (currentScenario.id === 1) {
      optionBExpectedValue = 550; // 50% × $1,100
    } else if (currentScenario.id === 2) {
      optionBExpectedValue = -550; // 50% × -$1,100
    } else if (currentScenario.id === 3) {
      optionBExpectedValue = -1000; // 50% × 5% + 50% × -25% of $10,000
    } else if (currentScenario.id === 4) {
      optionBExpectedValue = 2250; // 75% × $3,000
    } else if (currentScenario.id === 5) {
      optionBExpectedValue = -90; // 15% × -$600
    }
    
    // Award points for mathematically optimal choice
    if ((option === 'A' && optionAExpectedValue > optionBExpectedValue) ||
        (option === 'B' && optionBExpectedValue > optionAExpectedValue)) {
      setScore(prev => prev + 1);
    }
    
    // Track bias score - did they follow the typical bias?
    if (option === currentScenario.expectedBias) {
      setBiasScore(prev => prev + 1);
    }
  };

  const nextScenario = () => {
    if (currentScenarioIndex < scenarios.length - 1) {
      setCurrentScenarioIndex(prev => prev + 1);
      setSelectedOption(null);
      setShowResult(false);
      setShowExplanation(false);
    } else {
      setGameEnded(true);
    }
  };

  const currentScenario = scenarios[currentScenarioIndex];
  const progress = ((currentScenarioIndex + 1) / scenarios.length) * 100;
  
  // Display functions
  const getResultText = () => {
    if (selectedOption === 'A') {
      return currentScenario.optionAResult;
    } else {
      const baseText = currentScenario.optionBResult;
      const valueText = currentScenario.optionBValue === 0 
        ? "nothing!" 
        : `$${Math.abs(currentScenario.optionBValue)}${currentScenario.optionBValue > 0 ? " gain" : " loss"}!`;
      return `${baseText} ${valueText}`;
    }
  };

  const toggleExplanation = () => {
    setShowExplanation(!showExplanation);
  };
  
  const getBiasCategory = () => {
    if (biasScore <= 1) return "Highly Rational";
    if (biasScore <= 2) return "Mostly Rational";
    if (biasScore <= 3) return "Somewhat Biased";
    if (biasScore <= 4) return "Significantly Biased";
    return "Highly Loss Averse";
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <Card className="w-full">
        <CardHeader className="bg-gradient-to-r from-blue-100 to-green-100">
          <CardTitle className="text-center text-2xl flex justify-center items-center gap-2">
            <BarChart className="h-6 w-6" />
            Loss Aversion Experiment
          </CardTitle>
          <CardDescription className="text-center">
            Explore how loss aversion affects your financial decisions
          </CardDescription>
        </CardHeader>

        <CardContent className="pt-6">
          {!gameStarted ? (
            <div className="text-center space-y-6">
              <div className="bg-blue-50 p-4 rounded-lg text-left">
                <h3 className="text-lg font-semibold flex items-center gap-2 mb-2">
                  <Lightbulb className="h-5 w-5 text-yellow-500" />
                  About Loss Aversion
                </h3>
                <p className="mb-2">
                  Loss aversion is a cognitive bias where people strongly prefer avoiding losses to acquiring equivalent gains. 
                  Studies suggest that losses are psychologically twice as powerful as gains.
                </p>
                <p>
                  In this experiment, you'll face various financial scenarios that test how you respond to potential gains and losses.
                </p>
              </div>
              <div>
                <p className="mb-6">You'll start with a portfolio of $10,000. Your decisions will affect your final balance.</p>
                <Button onClick={startGame} className="px-8">Start Experiment</Button>
              </div>
            </div>
          ) : gameEnded ? (
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-xl font-bold flex justify-center items-center gap-2 mb-2">
                  <Trophy className="h-6 w-6 text-yellow-500" />
                  Experiment Complete!
                </h3>
                <p className="text-lg">Final Portfolio Value: <span className="font-bold text-green-600">${portfolio.toLocaleString()}</span></p>
              </div>

              <div className="bg-white rounded-lg p-4 border border-gray-200">
                <h4 className="font-semibold text-lg mb-2">Your Results</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p>Optimal Choices:</p>
                    <p className="text-2xl font-bold">{score} / {scenarios.length}</p>
                  </div>
                  <div>
                    <p>Loss Aversion Bias:</p>
                    <p className="text-2xl font-bold">{getBiasCategory()}</p>
                    <p className="text-sm text-gray-500">({biasScore} / {scenarios.length} typical responses)</p>
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-semibold flex items-center gap-2 mb-2">
                  <Lightbulb className="h-5 w-5 text-yellow-500" />
                  What This Means
                </h4>
                <p className="mb-2">
                  Loss aversion affects nearly everyone, but awareness of this bias can help you make more rational financial decisions.
                </p>
                <p>
                  When making financial choices, try to evaluate the expected value of each option rather than focusing solely on avoiding losses.
                </p>
              </div>

              <div className="text-center mt-6">
                <Button onClick={restartGame} className="flex items-center gap-2">
                  <RotateCcw className="h-4 w-4" />
                  Try Again
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex justify-between items-center mb-2">
                <div className="flex items-center gap-2">
                  <Banknote className="h-5 w-5 text-green-500" />
                  <span className="font-semibold">Portfolio: ${portfolio.toLocaleString()}</span>
                </div>
                <div className="text-sm text-gray-500">
                  Scenario {currentScenarioIndex + 1} of {scenarios.length}
                </div>
              </div>
              
              <Progress value={progress} className="h-2 mb-4" />
              
              <div className="bg-gray-50 p-4 rounded-lg mb-4">
                <h3 className="text-xl font-semibold mb-2">{currentScenario.title}</h3>
                <p className="mb-4">{currentScenario.description}</p>
                
                {!showResult ? (
                  <div className="space-y-3">
                    <Button
                      onClick={() => handleOptionSelect('A')}
                      variant="outline"
                      className="w-full justify-start text-left h-auto py-3 px-4"
                    >
                      {currentScenario.optionA}
                    </Button>
                    
                    <Button
                      onClick={() => handleOptionSelect('B')}
                      variant="outline"
                      className="w-full justify-start text-left h-auto py-3 px-4"
                    >
                      {currentScenario.optionB}
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className={`p-3 rounded-lg ${selectedOption === 'A' ? 'bg-blue-100 border border-blue-200' : 'bg-green-100 border border-green-200'}`}>
                      <p className="font-medium">Result:</p>
                      <p>{getResultText()}</p>
                      <p className="mt-2 font-semibold">
                        Portfolio change: {(selectedOption === 'A' ? currentScenario.optionAValue : currentScenario.optionBValue) > 0 ? '+' : ''}
                        ${(selectedOption === 'A' ? currentScenario.optionAValue : currentScenario.optionBValue).toLocaleString()}
                      </p>
                    </div>
                    
                    <div className="flex justify-between">
                      <Button
                        onClick={toggleExplanation}
                        variant="ghost"
                        className="flex items-center gap-1"
                      >
                        <Lightbulb className="h-4 w-4" />
                        {showExplanation ? "Hide Explanation" : "Show Explanation"}
                      </Button>
                      
                      <Button onClick={nextScenario} className="flex items-center gap-1">
                        {currentScenarioIndex < scenarios.length - 1 ? "Next Scenario" : "See Results"}
                        <ArrowRight className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    {showExplanation && (
                      <div className="bg-yellow-50 p-3 rounded-lg border border-yellow-200">
                        <p className="text-sm">{currentScenario.explanation}</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export { LossAversionGame };
export default LossAversionGame;