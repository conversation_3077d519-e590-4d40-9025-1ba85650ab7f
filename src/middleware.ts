import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';

// Domain to path mapping
const domainMappings: Record<string, string> = {
  're.abnasia.org': '/real-estate',
  'cars.abnasia.org': '/cars',
  'farm.abnasia.org': '/farm',
  'games.abnasia.org': '/games',
  'a.abnasia.org': '/appagent',
  'f.abnasia.org': '/farmerapp',
  'cars.localhost': '/cars',
  'farmer.localhost': '/farmerapp',
  'games.localhost': '/games',
  'wise.localhost': '/web/wise/eduwise',
  'abnasia.localhost': '/web/abnasia',
  'qr.abnasia.org': '/qr',
  'teome.abnasia.org': '/web/minilanding',
  'dev.abnasia.org': '/web/abnasia',
  'abnasia.org': '/web/abnasia',
  'edu.abnasia.org': '/platforms/edu',
  'wise.abnasia.org': '/web/wise/eduwise',
  'daotao.wisevietnam.org': '/web/wise/eduwise',
  'md.abnasia.org': '/tools/markdown/preview'
};

// Paths that always should be accessible without auth
const publicPaths = [
  '/login',
  '/auth/login',
  '/auth/signin',
  '/register',
  '/reset-password',
  '/api/auth',
  '/api/backbone/oneid/auth',
  '/api/farmers',
  '/api/test-farmer',
  '/api/farming-activities',
  '/farmerapp/self-service',
  '/_next',
  '/favicon.ico',
  '/images',
  '/fonts',
  '/sw.js',
  '/workbox',
  '/farmerapp',
  '/games',
  '/books',
  '/book-recommendations',
  '/aikols',
  '/appagent',
  '/workdone',
  '/giapha',
  '/abnhealth',
  '/australia',
  '/gooddeals',
  '/watchdogs',
  '/real-estate',
  '/cars',
  '/manifest.json',
  '/robots.txt',
  '/sitemap.xml',
  '/service-worker.js',
  '/qr',
  '/contests',
  '/edufun',
  '/canvas',
  '/portal',
  '/go',
  '/personalfinance',
  '/families',
  '/minirent',
  '/tools',
  '/vouchers',
  '/bomero',
  '/abncx',
  '/web',
  '/hr',
  '/companies',
  '/tools/okr',
  '/greenos',
  '/edu/english/repeatme',
  '/booksy',
  '/learning',
  '/liveevents',
  '/tools/abncommunication',
  '/exchanges/artx',
  '/exchanges/xapi',
  '/platforms/edu/login',
  '/platforms/edu/auth',
  '/platforms/edu/access-denied',
  '/platforms/healthcare',
  '/platforms/retails',
  '/platforms/edu',
  '/platforms',
  '/hr/login',
  '/hr/access-denied',
  '/backbone/oneid/login',
  '/backbone',
  '/ecommerce'
];

// OneID protected paths - these require OneID authentication
const oneIDProtectedPaths = [
  '/web/dev/1',
  '/hr'
];

// Platform protected paths - these require platform-specific authentication
const platformProtectedPaths = [
  // '/platforms/edu' - removed to allow public access
];

// EduWise backend authentication handler
async function handleEduWiseBackendAuth(request: NextRequest, pathname: string) {
  try {
    console.log(`[MIDDLEWARE] Checking EduWise backend auth for: ${pathname}`);
    
    // Skip login page itself
    if (pathname === '/web/wise/eduwise/backend/login') {
      console.log(`[MIDDLEWARE] Allowing access to backend login page`);
      return NextResponse.next();
    }
    
    // Get OneID token from Authorization header or cookie
    const authHeader = request.headers.get('authorization');
    let token = null;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
      console.log(`[MIDDLEWARE] Found token in Authorization header`);
    } else {
      // Try to get token from various cookie names
      token = request.cookies.get('oneid-token')?.value ||
              request.cookies.get('hr_oneid_token')?.value ||
              request.cookies.get('universal_auth_token')?.value ||
              request.cookies.get('edu_oneid_token')?.value;
      if (token) {
        console.log(`[MIDDLEWARE] Found token in cookie`);
      }
    }

    if (!token) {
      console.log(`[MIDDLEWARE] No token found, redirecting to eduwise login`);
      return redirectToEduWiseLogin(request, pathname);
    }

    // Verify token and check admin role
    console.log(`[MIDDLEWARE] Verifying token and checking admin role...`);
    try {
      let userData = null;
      
      // Handle different token formats
      if (token.startsWith('universal_token_')) {
        console.log(`[MIDDLEWARE] Universal token detected`);
        // For universal tokens, just verify it's well-formed
        const parts = token.split('_');
        if (parts.length >= 3) {
          const timestamp = parseInt(parts[2]);
          const tokenAge = Date.now() - timestamp;
          const maxAge = 24 * 60 * 60 * 1000; // 24 hours
          
          if (tokenAge > maxAge) {
            console.log(`[MIDDLEWARE] Universal token expired`);
            return redirectToEduWiseLogin(request, pathname);
          }
        } else {
          console.log(`[MIDDLEWARE] Malformed universal token`);
          return redirectToEduWiseLogin(request, pathname);
        }
      } else {
        // Try to decode as base64 JSON token
        userData = JSON.parse(Buffer.from(token, 'base64').toString());
        console.log(`[MIDDLEWARE] Token decoded for user:`, userData.username);

        // Check if token is expired
        if (userData.exp && userData.exp < Math.floor(Date.now() / 1000)) {
          console.log(`[MIDDLEWARE] Token expired`);
          return redirectToEduWiseLogin(request, pathname);
        }

        // Check if user has eduwise admin role
        if (userData.metadata?.eduwise?.role !== 'admin') {
          console.log(`[MIDDLEWARE] User does not have eduwise admin role:`, userData.metadata?.eduwise?.role);
          return NextResponse.redirect(new URL('/web/wise/eduwise/backend/login?error=insufficient_permissions', request.url));
        }
      }

      console.log(`[MIDDLEWARE] EduWise backend access granted for ${pathname}`);
      return NextResponse.next();

    } catch (error) {
      console.log(`[MIDDLEWARE] Token verification failed:`, error);
      return redirectToEduWiseLogin(request, pathname);
    }

  } catch (error) {
    console.error(`[MIDDLEWARE] EduWise backend auth error:`, error);
    return redirectToEduWiseLogin(request, pathname);
  }
}

function redirectToEduWiseLogin(request: NextRequest, pathname: string) {
  const baseUrl = getBaseUrl(request);
  const callbackUrl = encodeURIComponent(`${baseUrl}${pathname}`);
  const loginUrl = `${baseUrl}/web/wise/eduwise/login?redirect=${callbackUrl}`;
  
  console.log(`[MIDDLEWARE] EduWise backend redirect: ${pathname} -> ${loginUrl}`);
  return NextResponse.redirect(loginUrl);
}

// OneID authentication handler
async function handleOneIDAuth(request: NextRequest, pathname: string) {
  try {
    // Get OneID token from Authorization header or cookie
    const authHeader = request.headers.get('authorization');
    let token = null;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
      console.log(`[MIDDLEWARE] Found token in Authorization header`);
    } else {
      // Try to get token from various cookie names
      token = request.cookies.get('oneid-token')?.value ||
              request.cookies.get('hr_oneid_token')?.value ||
              request.cookies.get('universal_auth_token')?.value ||
              request.cookies.get('edu_oneid_token')?.value;
      if (token) {
        console.log(`[MIDDLEWARE] Found token in cookie`);
      }
    }

    if (!token) {
      console.log(`[MIDDLEWARE] No OneID token found in header or cookie, redirecting to login`);
      console.log(`[MIDDLEWARE] Available cookies:`, request.cookies.getAll().map(c => c.name));
      return redirectToOneIDLogin(request, pathname);
    }

    // Handle different token formats
    console.log(`[MIDDLEWARE] Verifying token...`);
    try {
      // Check if it's a universal token (simple string format)
      if (token.startsWith('universal_token_')) {
        console.log(`[MIDDLEWARE] Universal token detected (simple format)`);
        // For universal tokens, just verify it's well-formed
        const parts = token.split('_');
        if (parts.length >= 3) {
          const timestamp = parseInt(parts[2]);
          const tokenAge = Date.now() - timestamp;
          const maxAge = 24 * 60 * 60 * 1000; // 24 hours
          
          if (tokenAge > maxAge) {
            console.log(`[MIDDLEWARE] Universal token expired, redirecting to login`);
            return redirectToOneIDLogin(request, pathname);
          }
          
          console.log(`[MIDDLEWARE] Universal token is valid`);
        } else {
          console.log(`[MIDDLEWARE] Malformed universal token`);
          return redirectToOneIDLogin(request, pathname);
        }
      } else {
        // Try to decode as base64 JSON token (for test tokens)
        const tokenData = JSON.parse(Buffer.from(token, 'base64').toString());
        console.log(`[MIDDLEWARE] Base64 token decoded successfully for user:`, tokenData.username);

        // Check if token is expired
        if (tokenData.exp && tokenData.exp < Math.floor(Date.now() / 1000)) {
          console.log(`[MIDDLEWARE] Base64 token expired, redirecting to login`);
          return redirectToOneIDLogin(request, pathname);
        }

        console.log(`[MIDDLEWARE] Base64 token is valid`);
      }
    } catch (error) {
      console.log(`[MIDDLEWARE] Token verification failed:`, error);
      return redirectToOneIDLogin(request, pathname);
    }

    // For test system, allow access if token is valid
    console.log(`[MIDDLEWARE] OneID access granted for ${pathname} (test mode)`);
    return NextResponse.next();

  } catch (error) {
    console.error(`[MIDDLEWARE] OneID auth error:`, error);
    return redirectToOneIDLogin(request, pathname);
  }
}

// Platform authentication handler for edu platform
async function handlePlatformAuth(request: NextRequest, pathname: string) {
  try {
    console.log(`[MIDDLEWARE] Checking platform auth for: ${pathname}`);
    
    // Get the token for platform authentication
    const token = await getToken({ 
      req: request, 
      secret: process.env.NEXTAUTH_SECRET || 'DEVELOPMENT_SECRET_KEY_DO_NOT_USE_IN_PRODUCTION',
    });
    
    if (!token) {
      console.log(`[MIDDLEWARE] No platform token found, redirecting to platform login`);
      return redirectToPlatformLogin(request, pathname);
    }

    // Check for platform-specific permissions
    if (pathname.startsWith('/platforms/edu')) {
      // For demo purposes, deny access and show the requested message
      console.log(`[MIDDLEWARE] Demo user access denied to edu platform`);
      return redirectToAccessDenied(request, pathname);
    }

    console.log(`[MIDDLEWARE] Platform access granted for ${pathname}`);
    return NextResponse.next();

  } catch (error) {
    console.error(`[MIDDLEWARE] Platform auth error:`, error);
    return redirectToPlatformLogin(request, pathname);
  }
}

function redirectToPlatformLogin(request: NextRequest, pathname: string) {
  const baseUrl = getBaseUrl(request);
  const callbackUrl = encodeURIComponent(`${baseUrl}${pathname}${request.nextUrl.search}`);

  // Use platform-specific login page
  const loginUrl = `${baseUrl}/platforms/edu/login?redirect=${callbackUrl}`;

  console.log(`[MIDDLEWARE] Platform login redirect: ${pathname} -> ${loginUrl}`);
  return NextResponse.redirect(loginUrl);
}

function redirectToAccessDenied(request: NextRequest, pathname: string) {
  const baseUrl = getBaseUrl(request);
  
  // Create access denied URL with the required message
  const accessDeniedUrl = `${baseUrl}/platforms/edu/access-denied?reason=Missing%20required%20permissions:%20platform:edu:access`;

  console.log(`[MIDDLEWARE] Access denied redirect: ${pathname} -> ${accessDeniedUrl}`);
  return NextResponse.redirect(accessDeniedUrl);
}

function redirectToOneIDLogin(request: NextRequest, pathname: string) {
  const baseUrl = getBaseUrl(request);
  const callbackUrl = encodeURIComponent(`${baseUrl}${pathname}${request.nextUrl.search}`);

  // Use HR-specific login page for HR routes
  let loginUrl: string;
  if (pathname.startsWith('/hr')) {
    loginUrl = `${baseUrl}/hr/login?redirect=${callbackUrl}`;
  } else {
    // Use common OneID login page for all other OneID-protected routes
    loginUrl = `${baseUrl}/backbone/oneid/login?redirect=${callbackUrl}`;
  }

  console.log(`[MIDDLEWARE] Pathname: ${pathname}, LoginUrl: ${loginUrl}`);
  return NextResponse.redirect(loginUrl);
}

function getBaseUrl(request: NextRequest): string {
  const protocol = request.nextUrl.protocol;
  const host = request.nextUrl.host;
  return `${protocol}//${host}`;
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const hostname = request.headers.get('host') || request.nextUrl.hostname;

  console.log(`[MIDDLEWARE] Processing: ${pathname}`);
  const domainWithoutPort = hostname.split(':')[0];
  
  console.log(`\n[MIDDLEWARE] Request: ${hostname}${pathname}`);
  
  // Check if this is an API request or asset request first
  const isApiRequest = pathname.startsWith('/api/');
  const isAssetRequest = pathname.match(/\.(jpg|jpeg|png|gif|svg|css|js|woff|woff2|ttf|eot)$/i);
  
  // Handle EduWise backend protection
  if (pathname.startsWith('/web/wise/eduwise/backend')) {
    console.log(`[MIDDLEWARE] EduWise backend access attempt: ${pathname}`);
    return await handleEduWiseBackendAuth(request, pathname);
  }
  
  // Handle domain mappings
  if (domainMappings[domainWithoutPort]) {
    const basePath = domainMappings[domainWithoutPort];
    console.log(`[MIDDLEWARE] Domain mapping: ${domainWithoutPort} -> ${basePath}`);
    
    const newUrl = request.nextUrl.clone();
    
    // Special case for root path
    if (pathname === '/' || pathname === '') {
      newUrl.pathname = basePath;
      console.log(`[MIDDLEWARE] Rewriting: / -> ${basePath}`);
      return NextResponse.rewrite(newUrl);
    }
    
    // If path starts with basePath, redirect to clean URL (eat the base path)
    if (pathname.startsWith(basePath)) {
      const cleanPath = pathname.substring(basePath.length) || '/';
      console.log(`[MIDDLEWARE] Redirecting from internal path ${pathname} to clean URL ${cleanPath}`);
      const redirectUrl = request.nextUrl.clone();
      redirectUrl.pathname = cleanPath;
      return NextResponse.redirect(redirectUrl);
    }
    
    // API requests should be passed through without modification
    if (isApiRequest) {
      console.log(`[MIDDLEWARE] API request, passing through: ${pathname}`);
      return NextResponse.next();
    }
    
    // Asset requests should be passed through without modification
    if (isAssetRequest) {
      console.log(`[MIDDLEWARE] Asset request, passing through: ${pathname}`);
      return NextResponse.next();
    }
    
    // Define paths that should go to root (not mapped to the app)
    // const rootOnlyPaths = ['/login', '/auth', '/api', '/hr', '/platforms'];
    const rootOnlyPaths = ['/fuck'];
    
    // Check if this path should go to root
    const shouldGoToRoot = rootOnlyPaths.some(rootPath => pathname.startsWith(rootPath));
    
    if (shouldGoToRoot) {
      console.log(`[MIDDLEWARE] Root-only path ${pathname} on mapped domain - passing through to root`);
      return NextResponse.next();
    }
    
    // For all other paths, map them to the internal app
    const mappedPath = `${basePath}${pathname}`;
    console.log(`[MIDDLEWARE] Mapping clean URL ${pathname} to internal path ${mappedPath}`);
    newUrl.pathname = mappedPath;
    return NextResponse.rewrite(newUrl);
  }
  
  // No domain mapping - proceed with standard auth checks
  
  // Check for OneID protected paths FIRST (before public paths check)
  if (oneIDProtectedPaths.some(path => pathname.startsWith(path)) && !pathname.includes('/login')) {
    console.log(`[MIDDLEWARE] OneID protected path detected: ${pathname}`);
    return await handleOneIDAuth(request, pathname);
  }

  // Check for platform protected paths (before public paths check)
  if (platformProtectedPaths.some(path => pathname.startsWith(path)) && 
      !pathname.includes('/login') && 
      !pathname.includes('/auth') && 
      !pathname.includes('/access-denied')) {
    console.log(`[MIDDLEWARE] Platform protected path detected: ${pathname}`);
    return await handlePlatformAuth(request, pathname);
  }

  // Always allow public paths and API/asset requests
  if (publicPaths.some(path => pathname.startsWith(path)) || isApiRequest || isAssetRequest) {
    console.log(`[MIDDLEWARE] Public path/API/Asset, allowing access: ${pathname}`);
    return NextResponse.next();
  }
  
  // For protected routes, check authentication
  console.log(`[MIDDLEWARE] Protected route, checking auth: ${pathname}`);
  const token = await getToken({ 
    req: request, 
    secret: process.env.NEXTAUTH_SECRET || 'DEVELOPMENT_SECRET_KEY_DO_NOT_USE_IN_PRODUCTION',
  });
  
  if (!token) {
    // Get protocol and host from request URL
    const protocol = request.nextUrl.protocol;
    const host = request.nextUrl.host;
    
    // Construct the base URL for login
    let baseUrl = process.env.NEXTAUTH_URL;
    if (!baseUrl) {
      // Ensure we have a valid protocol and host
      if (!protocol || !host) {
        console.error('[Middleware] Missing protocol or host for URL construction');
        return NextResponse.next();
      }
      baseUrl = `${protocol}//${host}`;
    }
    
    // Ensure baseUrl is properly formatted
    try {
      // Construct the callback URL 
      const callbackPath = encodeURI(`${pathname}${request.nextUrl.search}`);
      const callbackUrl = `${baseUrl}${callbackPath}`;
      
      // Construct the login URL
      const loginUrl = new URL('/auth/login', baseUrl);
      loginUrl.searchParams.set('callbackUrl', callbackUrl);
      
      console.log(`[Middleware] Redirecting to login. URL: ${loginUrl.toString()}`);
      return NextResponse.redirect(loginUrl);
    } catch (error) {
      console.error('[Middleware] Error constructing redirect URL:', error);
      return NextResponse.next();
    }
  }
  
  return NextResponse.next();
}

// Configure which paths the middleware should run on
export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}; 
