'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Brain,
  FileSearch,
  AlertTriangle,
  Activity,
  Clock,
  CheckCircle,
  XCircle,
  TrendingUp,
  TrendingDown,
  PlayCircle,
  PauseCircle,
  BarChart3,
  Users,
  Shield,
  Eye,
  Building2,
  ArrowRight,
  CheckSquare,
  Zap,
  Target,
  Maximize2,
  Minimize2,
  ArrowLeft
} from 'lucide-react';
import { Button } from '@/components/ui/button.1';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

interface Agent {
  id: string;
  name: string;
  type: string;
  status: 'active' | 'inactive' | 'error';
  description: string;
  last_activity: string;
}

interface AuditSession {
  id: string;
  name: string;
  type: string;
  status: 'active' | 'paused' | 'completed';
  progress: {
    completion_percentage: number;
    documents_processed: number;
    anomalies_detected: number;
    compliance_issues: number;
  };
  created_at: string;
}

interface Alert {
  id: string;
  title: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
  description: string;
}

// Helper Components
const CircularProgress = ({ value, size = 120, strokeWidth = 8, color = '#2563eb', label = '', unit = '' }) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const offset = circumference - (value / 100) * circumference;

  return (
    <div className="relative inline-flex items-center justify-center">
      <svg width={size} height={size} className="transform -rotate-90">
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="#e5e7eb"
          strokeWidth={strokeWidth}
          fill="none"
        />
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color}
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={circumference}
          strokeDashoffset={offset}
          strokeLinecap="round"
          className="transition-all duration-1000 ease-out"
        />
      </svg>
      <div className="absolute text-center">
        <div className="text-2xl font-bold text-gray-900">{Math.round(value)}{unit}</div>
        {label && <div className="text-xs text-gray-600 mt-1">{label}</div>}
      </div>
    </div>
  );
};

const DataPanel = ({ title, value, unit, color = "blue", icon: Icon }: {
  title: string;
  value: string | number;
  unit: string;
  color?: string;
  icon?: any;
}) => (
  <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
    <div className="flex justify-between items-center mb-2">
      <span className="text-gray-600 text-sm uppercase tracking-wide flex items-center">
        {Icon && <Icon className="w-4 h-4 mr-2" />}
        {title}
      </span>
      <div className={`w-2 h-2 bg-${color}-500 rounded-full`}></div>
    </div>
    <div className={`text-2xl font-bold text-gray-900`}>
      {value}<span className="text-sm ml-1 text-gray-600">{unit}</span>
    </div>
  </div>
);

export default function AuditMindDashboard() {
  const router = useRouter();
  const [agents, setAgents] = useState<Agent[]>([]);
  const [sessions, setSessions] = useState<AuditSession[]>([]);

  const [time, setTime] = useState(new Date());
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [systemStatus, setSystemStatus] = useState('OPERATIONAL');

  // Mock alerts data
  const alerts = [
    { message: 'High anomaly detection rate in Client ABC', severity: 'high', timestamp: '14:32' },
    { message: 'Agent scheduler_001 performance degraded', severity: 'medium', timestamp: '14:15' },
    { message: 'Compliance check completed successfully', severity: 'low', timestamp: '13:45' },
    { message: 'New audit session started for XYZ Corp', severity: 'low', timestamp: '13:20' }
  ];
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Load data from JSON files
    const loadData = async () => {
      try {
        // Load data from API routes
        const [agentsResponse, sessionsResponse] = await Promise.all([
          fetch('/api/platforms/finance/auditmind/agents'),
          fetch('/api/platforms/finance/auditmind/audit-sessions')
        ]);

        const agentsData = await agentsResponse.json();
        const sessionsData = await sessionsResponse.json();

        setAgents(agentsData.agents || []);
        setSessions(sessionsData.sessions || []);
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();

    // Update time every second
    const timer = setInterval(() => {
      setTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPERATIONAL': return 'border-green-500 bg-green-50 text-green-700';
      case 'WARNING': return 'border-yellow-500 bg-yellow-50 text-yellow-700';
      case 'CRITICAL': return 'border-red-500 bg-red-50 text-red-700';
      default: return 'border-gray-500 bg-gray-50 text-gray-700';
    }
  };

  const getAgentStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600';
      case 'inactive': return 'text-gray-500';
      case 'error': return 'text-red-600';
      default: return 'text-gray-500';
    }
  };

  const toggleFullscreen = () => {
    if (!isFullscreen) {
      document.documentElement.requestFullscreen?.();
    } else {
      document.exitFullscreen?.();
    }
    setIsFullscreen(!isFullscreen);
  };

  // Calculate metrics
  const activeAgents = agents.filter(a => a.status === 'active').length;
  const activeSessions = sessions.filter(s => s.status === 'active').length;
  const totalDocuments = sessions.reduce((sum, s) => sum + s.progress.documents_processed, 0);
  const totalAnomalies = sessions.reduce((sum, s) => sum + s.progress.anomalies_detected, 0);
  const avgProgress = sessions.length > 0 ? sessions.reduce((sum, s) => sum + s.progress.completion_percentage, 0) / sessions.length : 0;



  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading AuditMind Dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">

      {/* Control Bar */}
      {!isFullscreen && (
        <div className="bg-white border-b border-gray-200 p-4 flex justify-between items-center shadow-sm">
          <button
            onClick={() => router.push('/platforms/finance')}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>Back to Finance Platform</span>
          </button>

          <div className="flex items-center space-x-4">
            <div className="text-gray-700 text-sm font-medium">
              AUDITMIND DASHBOARD
            </div>
            <button
              onClick={toggleFullscreen}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              {isFullscreen ? <Minimize2 className="w-5 h-5" /> : <Maximize2 className="w-5 h-5" />}
              <span>{isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}</span>
            </button>
          </div>
        </div>
      )}

      <div className="p-6 text-gray-900 overflow-hidden">
        {/* Header */}
        <div className="relative mb-8">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Brain className="w-8 h-8 mr-3 text-blue-600" />
              ABN AuditMind Dashboard
            </h1>
            <div className="text-right">
              <div className="text-gray-500 text-sm">SYSTEM TIME</div>
              <div className="text-xl font-bold text-gray-900">
                {time.toLocaleTimeString()}
              </div>
              <div className="text-sm text-gray-500">
                {time.toLocaleDateString()}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-6">
            <div className={`px-4 py-2 border rounded-lg ${getStatusColor(systemStatus)}`}>
              <Shield className="w-4 h-4 inline mr-2" />
              STATUS: {systemStatus}
            </div>
            <div className="flex space-x-2">
              {[...Array(5)].map((_, i) => (
                <div
                  key={i}
                  className={`w-3 h-3 rounded-full ${
                    i < 4 ? 'bg-green-500' : 'bg-yellow-500'
                  }`}
                ></div>
              ))}
            </div>
            <div className="text-gray-700 text-sm font-medium">
              AI AGENTS ACTIVE: {activeAgents}/{agents.length}
            </div>
          </div>
        </div>

        {/* Main Dashboard Grid - Core Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* AI Agent Health */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
            <h3 className="text-gray-700 text-sm uppercase tracking-wide mb-4 flex items-center font-medium">
              <Brain className="w-4 h-4 mr-2 text-blue-600" />
              AI Agent Health
            </h3>
            <CircularProgress value={(activeAgents / agents.length) * 100} color="#2563eb" />
          </div>

          {/* Audit Progress */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
            <h3 className="text-gray-700 text-sm uppercase tracking-wide mb-4 flex items-center font-medium">
              <Activity className="w-4 h-4 mr-2 text-green-600" />
              Audit Progress
            </h3>
            <CircularProgress value={avgProgress} color="#16a34a" />
          </div>

          {/* Compliance Score */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
            <h3 className="text-gray-700 text-sm uppercase tracking-wide mb-4 flex items-center font-medium">
              <Shield className="w-4 h-4 mr-2 text-orange-600" />
              Compliance
            </h3>
            <CircularProgress value={85} color="#ea580c" />
          </div>

          {/* System Monitor */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
            <h3 className="text-gray-700 text-sm uppercase tracking-wide mb-4 font-medium">System Monitor</h3>
            <div className="flex justify-center">
              <div className="relative">
                <div className="w-24 h-24 border-2 border-gray-200 rounded-full flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-lg font-bold text-gray-900">{agents.length}</div>
                    <div className="text-xs text-gray-500">Agents</div>
                  </div>
                </div>
                <div className="absolute top-0 right-0 w-3 h-3 bg-green-500 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Operational Data Panels */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <DataPanel
            title="Active Sessions"
            value={activeSessions}
            unit=""
            color="blue"
            icon={Activity}
          />
          <DataPanel
            title="Documents Processed"
            value={totalDocuments.toLocaleString()}
            unit=""
            color="green"
            icon={FileSearch}
          />
          <DataPanel
            title="Anomalies Detected"
            value={totalAnomalies}
            unit=""
            color="orange"
            icon={AlertTriangle}
          />
          <DataPanel
            title="System Uptime"
            value="99.7"
            unit="%"
            color="green"
            icon={Zap}
          />
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
            <h3 className="text-gray-700 text-sm uppercase tracking-wide mb-4 flex items-center font-medium">
              <Target className="w-4 h-4 mr-2 text-blue-600" />
              Quick Actions
            </h3>
            <div className="space-y-3">
              <button
                onClick={() => router.push('/platforms/finance/auditmind/onboarding')}
                className="w-full bg-blue-50 border border-blue-200 rounded-lg p-3 text-blue-700 hover:bg-blue-100 transition-colors flex items-center"
              >
                <PlayCircle className="w-4 h-4 mr-2" />
                Start New Audit
              </button>
              <button
                onClick={() => router.push('/platforms/finance/auditmind/consolidated')}
                className="w-full bg-green-50 border border-green-200 rounded-lg p-3 text-green-700 hover:bg-green-100 transition-colors flex items-center"
              >
                <BarChart3 className="w-4 h-4 mr-2" />
                Consolidated View
              </button>
              <button
                onClick={() => router.push('/platforms/finance/auditmind/boards')}
                className="w-full bg-gray-50 border border-gray-200 rounded-lg p-3 text-gray-700 hover:bg-gray-100 transition-colors flex items-center"
              >
                <Eye className="w-4 h-4 mr-2" />
                View Boards
              </button>
            </div>
          </div>

          {/* AI Agents Status */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
            <h3 className="text-gray-700 text-sm uppercase tracking-wide mb-4 flex items-center font-medium">
              <Brain className="w-4 h-4 mr-2 text-blue-600" />
              AI Agents Status
            </h3>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {agents.slice(0, 6).map((agent, i) => (
                <div key={i} className="flex justify-between items-center text-sm">
                  <span className="text-gray-700">{agent.name}</span>
                  <div className="flex items-center">
                    <div className={`w-2 h-2 rounded-full mr-2 ${getAgentStatusColor(agent.status).replace('text-', 'bg-')}`}></div>
                    <span className={getAgentStatusColor(agent.status)}>{agent.status}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Recent Alerts */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
            <h3 className="text-gray-700 text-sm uppercase tracking-wide mb-4 flex items-center font-medium">
              <AlertTriangle className="w-4 h-4 mr-2 text-orange-600" />
              System Alerts
            </h3>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {alerts.slice(0, 4).map((alert, i) => (
                <div key={i} className="text-sm">
                  <div className="flex items-center justify-between">
                    <span className={`${alert.severity === 'high' ? 'text-red-600' : alert.severity === 'medium' ? 'text-yellow-600' : 'text-green-600'}`}>
                      {alert.message.substring(0, 30)}...
                    </span>
                    <span className="text-gray-500 text-xs">{alert.timestamp}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Keyboard Shortcuts Info */}
      {!isFullscreen && (
        <div className="fixed bottom-4 right-4 bg-white border border-gray-200 rounded-lg p-3 text-xs text-gray-600 shadow-lg z-20">
          <div className="font-bold mb-2 text-gray-900">Keyboard Shortcuts:</div>
          <div>F11 - Toggle Fullscreen</div>
          <div>ESC - Exit Fullscreen</div>
          <div>CTRL+R - Refresh Data</div>
        </div>
      )}
    </div>
  );
}
