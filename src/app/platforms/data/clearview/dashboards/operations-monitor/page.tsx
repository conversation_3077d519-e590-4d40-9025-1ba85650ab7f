'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Grid3X3 } from 'lucide-react';
import { Button } from '@/components/ui/button.1';

interface OperationsData {
  system: {
    uptime: number;
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    networkIO: number;
  };
  services: Array<{
    name: string;
    status: 'running' | 'stopped' | 'error';
    uptime: number;
    responseTime: number;
    requests: number;
  }>;
  alerts: Array<{
    id: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    message: string;
    timestamp: string;
    resolved: boolean;
  }>;
  performance: {
    avgResponseTime: number;
    throughput: number;
    errorRate: number;
    availability: number;
  };
}

export default function OperationsMonitorDashboard() {
  const router = useRouter();
  const [data, setData] = useState<OperationsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(new Date());

  useEffect(() => {
    const loadData = async () => {
      try {
        const response = await fetch('/api/platforms/data/clearview/operations-monitor');
        const opsData = await response.json();
        setData(opsData);
        setLastUpdate(new Date());
      } catch (error) {
        console.error('Error loading operations data:', error);
        // Fallback data
        setData({
          system: {
            uptime: 99.97,
            cpuUsage: 67.3,
            memoryUsage: 45.8,
            diskUsage: 23.1,
            networkIO: 12.4
          },
          services: [],
          alerts: [],
          performance: {
            avgResponseTime: 145,
            throughput: 1250,
            errorRate: 0.2,
            availability: 99.97
          }
        });
      } finally {
        setLoading(false);
      }
    };

    loadData();
    const interval = setInterval(loadData, 10000); // Update every 10 seconds
    return () => clearInterval(interval);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleString('en-US', {
      month: 'short',
      day: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatPercent = (value: number) => `${value.toFixed(1)}%`;
  const formatNumber = (value: number) => value.toLocaleString();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-green-600';
      case 'stopped': return 'text-yellow-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getAlertColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-50';
      case 'high': return 'text-orange-600 bg-orange-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-blue-600 bg-blue-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  if (loading || !data) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white text-black font-mono text-xs">
      {/* Header */}
      <div className="border-b border-gray-300 p-2 flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => router.push('/platforms/data/clearview')}>
            <ArrowLeft className="h-3 w-3 mr-1" />
            Back to ClearView
          </Button>
          <div>
            <span className="font-bold">Operations Monitor Dashboard</span>
            <span className="ml-4 text-gray-600">Last Update: {formatTime(lastUpdate)}</span>
          </div>
        </div>
        <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
          <Grid3X3 className="h-3 w-3 mr-1" />
          Refresh
        </Button>
      </div>

      <div className="p-4 grid grid-cols-6 gap-4">
        {/* System Resources */}
        <div className="col-span-2 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">System Resources</div>
          <div className="space-y-1">
            <div className="flex justify-between">
              <span>System Uptime</span>
              <span className="font-bold text-green-600">{formatPercent(data.system.uptime)}</span>
            </div>
            <div className="flex justify-between">
              <span>CPU Usage</span>
              <span className={`font-bold ${
                data.system.cpuUsage > 80 ? 'text-red-600' :
                data.system.cpuUsage > 60 ? 'text-yellow-600' : 'text-green-600'
              }`}>
                {formatPercent(data.system.cpuUsage)}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Memory Usage</span>
              <span className={`font-bold ${
                data.system.memoryUsage > 80 ? 'text-red-600' :
                data.system.memoryUsage > 60 ? 'text-yellow-600' : 'text-green-600'
              }`}>
                {formatPercent(data.system.memoryUsage)}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Disk Usage</span>
              <span className="font-bold text-green-600">{formatPercent(data.system.diskUsage)}</span>
            </div>
            <div className="flex justify-between">
              <span>Network I/O</span>
              <span className="font-bold">{data.system.networkIO} MB/s</span>
            </div>
          </div>
        </div>

        {/* Services Status */}
        <div className="col-span-2 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">Services Status</div>
          <div className="space-y-1">
            {data.services.map((service, i) => (
              <div key={i} className="flex justify-between text-xs">
                <span className="truncate w-24">{service.name}</span>
                <span className={`font-bold ${getStatusColor(service.status)}`}>
                  {service.status.toUpperCase()}
                </span>
                <span className="font-bold">{service.responseTime}ms</span>
              </div>
            ))}
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="col-span-2 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">Performance Metrics</div>
          <div className="space-y-1">
            <div className="flex justify-between">
              <span>Avg Response Time</span>
              <span className="font-bold">{data.performance.avgResponseTime}ms</span>
            </div>
            <div className="flex justify-between">
              <span>Throughput</span>
              <span className="font-bold">{formatNumber(data.performance.throughput)} req/min</span>
            </div>
            <div className="flex justify-between">
              <span>Error Rate</span>
              <span className={`font-bold ${
                data.performance.errorRate > 1 ? 'text-red-600' :
                data.performance.errorRate > 0.5 ? 'text-yellow-600' : 'text-green-600'
              }`}>
                {formatPercent(data.performance.errorRate)}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Availability</span>
              <span className="font-bold text-green-600">{formatPercent(data.performance.availability)}</span>
            </div>
          </div>
        </div>

        {/* Active Alerts */}
        <div className="col-span-6 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">Active Alerts</div>
          <div className="space-y-1">
            {data.alerts.filter(alert => !alert.resolved).map((alert, i) => (
              <div key={i} className={`flex justify-between text-xs p-2 rounded ${getAlertColor(alert.severity)}`}>
                <span className="font-bold">{alert.severity.toUpperCase()}</span>
                <span className="flex-1 mx-2">{alert.message}</span>
                <span>{new Date(alert.timestamp).toLocaleTimeString()}</span>
              </div>
            ))}
            {data.alerts.filter(alert => !alert.resolved).length === 0 && (
              <div className="text-center text-gray-500 py-4">
                No active alerts - All systems operational
              </div>
            )}
          </div>
        </div>

        {/* Service Details */}
        <div className="col-span-3 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">Service Details</div>
          <div className="space-y-1">
            {data.services.map((service, i) => (
              <div key={i} className="border border-gray-200 p-2 rounded">
                <div className="flex justify-between items-center mb-1">
                  <span className="font-bold">{service.name}</span>
                  <span className={`px-2 py-1 rounded text-xs font-bold ${
                    service.status === 'running' ? 'bg-green-100 text-green-800' :
                    service.status === 'stopped' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {service.status.toUpperCase()}
                  </span>
                </div>
                <div className="grid grid-cols-3 gap-2 text-xs">
                  <div>
                    <span className="text-gray-600">Uptime:</span>
                    <span className="font-bold ml-1">{formatPercent(service.uptime)}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Response:</span>
                    <span className="font-bold ml-1">{service.responseTime}ms</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Requests:</span>
                    <span className="font-bold ml-1">{formatNumber(service.requests)}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Alerts History */}
        <div className="col-span-3 space-y-2">
          <div className="font-bold border-b border-gray-300 pb-1">Recent Alerts History</div>
          <div className="space-y-1">
            {data.alerts.slice(0, 10).map((alert, i) => (
              <div key={i} className="flex justify-between text-xs p-1 border-b border-gray-100">
                <span className={`font-bold ${
                  alert.severity === 'critical' ? 'text-red-600' :
                  alert.severity === 'high' ? 'text-orange-600' :
                  alert.severity === 'medium' ? 'text-yellow-600' : 'text-blue-600'
                }`}>
                  {alert.severity.toUpperCase()}
                </span>
                <span className="flex-1 mx-2 truncate">{alert.message}</span>
                <span className={alert.resolved ? 'text-green-600' : 'text-red-600'}>
                  {alert.resolved ? 'RESOLVED' : 'ACTIVE'}
                </span>
                <span className="text-gray-500 ml-2">
                  {new Date(alert.timestamp).toLocaleTimeString()}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}