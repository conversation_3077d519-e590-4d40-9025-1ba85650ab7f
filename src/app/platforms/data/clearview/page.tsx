'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { BarChart3, Grid3X3, TrendingUp, Users, FileText, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button.1';

interface Dashboard {
  id: string;
  name: string;
  description: string;
  category: string;
  icon?: string;
  path?: string;
  url?: string;
  status: 'active' | 'maintenance' | 'coming_soon';
  lastUpdated: string;
  metrics?: {
    [key: string]: string | number; // Allow for flexible metrics structure
  };
}

interface DashboardData {
  dashboards: Dashboard[];
  categories: string[];
  systemStatus: {
    uptime: string;
    lastSync: string;
    totalDashboards: number;
    activeDashboards: number;
  };
}

export default function ClearViewDashboard() {
  const router = useRouter();
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [lastUpdate, setLastUpdate] = useState(new Date());

  useEffect(() => {
    const loadData = async () => {
      try {
        const response = await fetch('/api/platforms/data/clearview/dashboards');
        const dashboardData = await response.json();
        setData(dashboardData);
        setLastUpdate(new Date());
      } catch (error) {
        console.error('Error loading dashboard data:', error);
        // Fallback data
        setData({
          dashboards: [],
          categories: ['Finance', 'Operations', 'Analytics', 'Monitoring'],
          systemStatus: {
            uptime: '99.9%',
            lastSync: new Date().toISOString(),
            totalDashboards: 0,
            activeDashboards: 0
          }
        });
      } finally {
        setLoading(false);
      }
    };

    loadData();
    const interval = setInterval(loadData, 60000); // Update every minute
    return () => clearInterval(interval);
  }, []);

  const getIcon = (iconName: string) => {
    const icons: Record<string, React.ReactNode> = {
      'BarChart3': <BarChart3 className="h-6 w-6" />,
      'Grid3X3': <Grid3X3 className="h-6 w-6" />,
      'TrendingUp': <TrendingUp className="h-6 w-6" />,
      'Users': <Users className="h-6 w-6" />,
      'FileText': <FileText className="h-6 w-6" />,
      'Settings': <Settings className="h-6 w-6" />
    };
    return icons[iconName] || <BarChart3 className="h-6 w-6" />;
  };

  const filteredDashboards = data?.dashboards.filter(dashboard => {
    const matchesCategory = selectedCategory === 'all' || dashboard.category === selectedCategory;
    const matchesSearch = dashboard.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         dashboard.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  }) || [];

  const formatTime = (date: Date) => {
    return date.toLocaleString('en-US', {
      month: 'short',
      day: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  if (loading || !data) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white text-black font-mono text-xs">
      {/* Header */}
      <div className="border-b border-gray-300 p-2 flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <div>
            <span className="font-bold">ABN ClearView Dashboard Hub</span>
            <span className="ml-4 text-gray-600">Last Update: {formatTime(lastUpdate)}</span>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-green-600 font-bold">System: {data.systemStatus.uptime}</span>
          <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
            Refresh
          </Button>
        </div>
      </div>

      <div className="p-4">
        {/* Controls */}
        <div className="mb-4 flex flex-wrap gap-4 items-center">
          <div className="flex items-center space-x-2">
            <label className="font-semibold">Category:</label>
            <select 
              value={selectedCategory} 
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="border border-gray-300 rounded px-2 py-1 text-xs"
            >
              <option value="all">All Categories</option>
              {data.categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>
          <div className="flex items-center space-x-2">
            <label className="font-semibold">Search:</label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search dashboards..."
              className="border border-gray-300 rounded px-2 py-1 text-xs w-48"
            />
          </div>
        </div>

        {/* System Overview */}
        <div className="mb-6 grid grid-cols-4 gap-4">
          <div className="border border-gray-300 p-3 rounded">
            <div className="font-bold text-gray-700">Total Dashboards</div>
            <div className="text-2xl font-bold">{data.systemStatus.totalDashboards}</div>
          </div>
          <div className="border border-gray-300 p-3 rounded">
            <div className="font-bold text-gray-700">Active Dashboards</div>
            <div className="text-2xl font-bold text-green-600">{data.systemStatus.activeDashboards}</div>
          </div>
          <div className="border border-gray-300 p-3 rounded">
            <div className="font-bold text-gray-700">System Uptime</div>
            <div className="text-2xl font-bold text-green-600">{data.systemStatus.uptime}</div>
          </div>
          <div className="border border-gray-300 p-3 rounded">
            <div className="font-bold text-gray-700">Last Sync</div>
            <div className="text-sm font-bold">{new Date(data.systemStatus.lastSync).toLocaleTimeString()}</div>
          </div>
        </div>

        {/* Dashboard Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {filteredDashboards.map((dashboard) => (
            <div key={dashboard.id} className="border border-gray-300 rounded p-4 hover:shadow-lg transition-shadow">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <div className="text-blue-600">
                    {getIcon(dashboard.icon || 'BarChart3')}
                  </div>
                  <div>
                    <h3 className="font-bold text-sm">{dashboard.name}</h3>
                    <span className="text-xs text-gray-600">{dashboard.category}</span>
                  </div>
                </div>
                <span className={`px-2 py-1 rounded text-xs font-bold ${
                  dashboard.status === 'active' ? 'bg-green-100 text-green-800' :
                  dashboard.status === 'maintenance' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {dashboard.status.replace('_', ' ').toUpperCase()}
                </span>
              </div>
              
              <p className="text-xs text-gray-700 mb-3 line-clamp-2">{dashboard.description}</p>
              
              <div className="space-y-1 mb-3">
                {dashboard.metrics && Object.entries(dashboard.metrics).slice(0, 3).map(([key, value]) => (
                  <div key={key} className="flex justify-between text-xs">
                    <span>{key.charAt(0).toUpperCase() + key.slice(1)}:</span>
                    <span className="font-bold">
                      {typeof value === 'number' ? value.toLocaleString() : value}
                    </span>
                  </div>
                ))}
                {!dashboard.metrics && (
                  <div className="text-xs text-gray-500">No metrics available</div>
                )}
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-500">Updated: {dashboard.lastUpdated}</span>
                <Button 
                  size="sm" 
                  onClick={() => router.push(dashboard.path || dashboard.url || '/dashboard')}
                  disabled={dashboard.status !== 'active'}
                  className="text-xs"
                >
                  {dashboard.status === 'active' ? 'Open' : 'Unavailable'}
                </Button>
              </div>
            </div>
          ))}
        </div>

        {filteredDashboards.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500 text-sm">No dashboards found matching your criteria.</div>
          </div>
        )}
      </div>
    </div>
  );
}