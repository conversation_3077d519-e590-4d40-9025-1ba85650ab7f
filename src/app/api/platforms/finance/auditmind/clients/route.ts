import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET() {
  try {
    const dataPath = path.join(process.cwd(), 'data/apps/platforms/finance/auditmind/clients.json');
    
    // Check if file exists
    if (!fs.existsSync(dataPath)) {
      // Return fallback data if file doesn't exist
      return NextResponse.json({
        clients: [
          {
            id: "client-001",
            name: "TechCorp Inc.",
            industry: "Technology",
            size: "Large",
            riskLevel: "Medium",
            lastAudit: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            status: "active",
            contact: {
              name: "<PERSON>",
              email: "<EMAIL>",
              phone: "******-0123"
            }
          },
          {
            id: "client-002",
            name: "RetailPlus Ltd.",
            industry: "Retail",
            size: "Medium",
            riskLevel: "Low",
            lastAudit: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
            status: "active",
            contact: {
              name: "<PERSON>",
              email: "<EMAIL>",
              phone: "******-0456"
            }
          }
        ]
      });
    }

    const data = fs.readFileSync(dataPath, 'utf8');
    const clientsData = JSON.parse(data);
    
    return NextResponse.json(clientsData);
  } catch (error) {
    console.error('Error loading clients data:', error);
    
    // Return fallback data on error
    return NextResponse.json({
      clients: []
    });
  }
}
