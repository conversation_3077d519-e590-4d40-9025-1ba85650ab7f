import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET() {
  try {
    const dataPath = path.join(process.cwd(), 'data/apps/platforms/finance/auditmind/audit_sessions.json');
    
    // Check if file exists
    if (!fs.existsSync(dataPath)) {
      // Return fallback data if file doesn't exist
      return NextResponse.json({
        sessions: [
          {
            id: "session-001",
            clientId: "client-001",
            clientName: "TechCorp Inc.",
            type: "Financial Audit",
            status: "in_progress",
            startDate: new Date().toISOString(),
            assignedAgents: ["agent-001", "agent-002"],
            progress: 65,
            findings: 3,
            priority: "high"
          },
          {
            id: "session-002",
            clientId: "client-002", 
            clientName: "RetailPlus Ltd.",
            type: "Compliance Review",
            status: "completed",
            startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            assignedAgents: ["agent-003"],
            progress: 100,
            findings: 1,
            priority: "medium"
          }
        ]
      });
    }

    const data = fs.readFileSync(dataPath, 'utf8');
    const sessionsData = JSON.parse(data);
    
    return NextResponse.json(sessionsData);
  } catch (error) {
    console.error('Error loading audit sessions data:', error);
    
    // Return fallback data on error
    return NextResponse.json({
      sessions: []
    });
  }
}
