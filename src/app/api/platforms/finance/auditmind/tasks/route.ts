import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET() {
  try {
    const dataPath = path.join(process.cwd(), 'data/apps/platforms/finance/auditmind/tasks.json');
    
    // Check if file exists
    if (!fs.existsSync(dataPath)) {
      // Return fallback data if file doesn't exist
      return NextResponse.json({
        tasks: [
          {
            id: "task-001",
            title: "Financial Statement Analysis",
            description: "Analyze Q3 financial statements for TechCorp Inc.",
            type: "analysis",
            status: "in_progress",
            priority: "high",
            assignedAgent: "agent-001",
            clientId: "client-001",
            dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            progress: 75,
            estimatedHours: 8,
            actualHours: 6
          },
          {
            id: "task-002",
            title: "Risk Assessment Review",
            description: "Conduct risk assessment for RetailPlus Ltd.",
            type: "risk",
            status: "completed",
            priority: "medium",
            assignedAgent: "agent-002",
            clientId: "client-002",
            dueDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            progress: 100,
            estimatedHours: 4,
            actualHours: 3.5
          }
        ]
      });
    }

    const data = fs.readFileSync(dataPath, 'utf8');
    const tasksData = JSON.parse(data);
    
    return NextResponse.json(tasksData);
  } catch (error) {
    console.error('Error loading tasks data:', error);
    
    // Return fallback data on error
    return NextResponse.json({
      tasks: []
    });
  }
}
