import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET() {
  try {
    const dataPath = path.join(process.cwd(), 'data/apps/platforms/finance/auditmind/agents.json');
    
    // Check if file exists
    if (!fs.existsSync(dataPath)) {
      // Return fallback data if file doesn't exist
      return NextResponse.json({
        agents: [
          {
            id: "agent-001",
            name: "Financial Analyzer",
            type: "analysis",
            status: "active",
            specialization: "Financial Statement Analysis",
            accuracy: 95.2,
            tasksCompleted: 1250,
            lastActive: new Date().toISOString()
          },
          {
            id: "agent-002", 
            name: "Risk Assessor",
            type: "risk",
            status: "active",
            specialization: "Risk Assessment",
            accuracy: 92.8,
            tasksCompleted: 890,
            lastActive: new Date().toISOString()
          }
        ]
      });
    }

    const data = fs.readFileSync(dataPath, 'utf8');
    const agentsData = JSON.parse(data);
    
    return NextResponse.json(agentsData);
  } catch (error) {
    console.error('Error loading agents data:', error);
    
    // Return fallback data on error
    return NextResponse.json({
      agents: []
    });
  }
}
