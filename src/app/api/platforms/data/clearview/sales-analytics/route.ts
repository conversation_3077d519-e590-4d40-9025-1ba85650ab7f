import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET() {
  try {
    const dataPath = path.join(process.cwd(), 'data/apps/platforms/data/clearview/sales-analytics.json');
    
    // Check if file exists
    if (!fs.existsSync(dataPath)) {
      // Return fallback data if file doesn't exist
      return NextResponse.json({
        metrics: {
          totalRevenue: 2450000,
          monthlyGrowth: 12.5,
          totalOrders: 1850,
          averageOrderValue: 1324,
          conversionRate: 3.2,
          customerAcquisitionCost: 85
        },
        salesData: [],
        topProducts: [],
        regions: [],
        lastUpdated: new Date().toISOString()
      });
    }

    const data = fs.readFileSync(dataPath, 'utf8');
    const salesData = JSON.parse(data);
    
    return NextResponse.json(salesData);
  } catch (error) {
    console.error('Error loading sales data:', error);
    
    // Return fallback data on error
    return NextResponse.json({
      metrics: {
        totalRevenue: 2450000,
        monthlyGrowth: 12.5,
        totalOrders: 1850,
        averageOrderValue: 1324,
        conversionRate: 3.2,
        customerAcquisitionCost: 85
      },
      salesData: [],
      topProducts: [],
      regions: [],
      lastUpdated: new Date().toISOString()
    });
  }
}
