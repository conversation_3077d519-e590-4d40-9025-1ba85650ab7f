import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET() {
  try {
    const dataPath = path.join(process.cwd(), 'data/apps/platforms/data/clearview/dashboards.json');
    
    // Check if file exists
    if (!fs.existsSync(dataPath)) {
      // Return fallback data if file doesn't exist
      return NextResponse.json({
        dashboards: [],
        categories: [],
        systemStatus: {
          uptime: "99.9%",
          lastSync: new Date().toISOString(),
          totalDashboards: 0,
          activeDashboards: 0
        }
      });
    }

    const data = fs.readFileSync(dataPath, 'utf8');
    const dashboardData = JSON.parse(data);
    
    return NextResponse.json(dashboardData);
  } catch (error) {
    console.error('Error loading dashboard data:', error);
    
    // Return fallback data on error
    return NextResponse.json({
      dashboards: [],
      categories: [],
      systemStatus: {
        uptime: "99.9%",
        lastSync: new Date().toISOString(),
        totalDashboards: 0,
        activeDashboards: 0
      }
    });
  }
}
