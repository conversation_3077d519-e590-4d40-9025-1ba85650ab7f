import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET() {
  try {
    const dataPath = path.join(process.cwd(), 'data/apps/platforms/data/clearview/operations-monitor.json');
    
    // Check if file exists
    if (!fs.existsSync(dataPath)) {
      // Return fallback data if file doesn't exist
      return NextResponse.json({
        metrics: {
          systemUptime: "99.9%",
          activeUsers: 1250,
          serverLoad: 45,
          responseTime: 120,
          errorRate: 0.1,
          throughput: 850
        },
        alerts: [],
        services: [],
        lastUpdated: new Date().toISOString()
      });
    }

    const data = fs.readFileSync(dataPath, 'utf8');
    const operationsData = JSON.parse(data);
    
    return NextResponse.json(operationsData);
  } catch (error) {
    console.error('Error loading operations data:', error);
    
    // Return fallback data on error
    return NextResponse.json({
      metrics: {
        systemUptime: "99.9%",
        activeUsers: 1250,
        serverLoad: 45,
        responseTime: 120,
        errorRate: 0.1,
        throughput: 850
      },
      alerts: [],
      services: [],
      lastUpdated: new Date().toISOString()
    });
  }
}
