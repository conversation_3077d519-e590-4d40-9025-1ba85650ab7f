import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const MENU_ITEMS_FILE = path.join(process.cwd(), 'data/apps/booksy/food-orders/menu-items.json');

// Ensure data directory exists
function ensureDataDirectory() {
  const dataDir = path.dirname(MENU_ITEMS_FILE);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
}

// Read menu items from file
function readMenuItems() {
  ensureDataDirectory();
  try {
    if (fs.existsSync(MENU_ITEMS_FILE)) {
      const data = fs.readFileSync(MENU_ITEMS_FILE, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error('Error reading menu items:', error);
    return [];
  }
}

// Write menu items to file
function writeMenuItems(menuItems: any[]) {
  ensureDataDirectory();
  try {
    fs.writeFileSync(MENU_ITEMS_FILE, JSON.stringify(menuItems, null, 2));
  } catch (error) {
    console.error('Error writing menu items:', error);
    throw error;
  }
}

// GET - Fetch menu items
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const restaurantId = searchParams.get('restaurantId');
    const providerId = searchParams.get('providerId');
    const category = searchParams.get('category');
    const subcategory = searchParams.get('subcategory');
    const search = searchParams.get('search');
    const featured = searchParams.get('featured');
    const popular = searchParams.get('popular');
    const signature = searchParams.get('signature');
    const available = searchParams.get('available');
    
    const menuItems = readMenuItems();
    
    let filteredItems = menuItems;
    
    // Filter by restaurant ID
    if (restaurantId) {
      filteredItems = filteredItems.filter((item: any) => 
        item.restaurantId === restaurantId
      );
    }
    
    // Filter by provider ID
    if (providerId) {
      filteredItems = filteredItems.filter((item: any) => 
        item.providerId === providerId
      );
    }
    
    // Filter by category
    if (category && category !== 'all') {
      filteredItems = filteredItems.filter((item: any) => 
        item.category === category
      );
    }
    
    // Filter by subcategory
    if (subcategory && subcategory !== 'all') {
      filteredItems = filteredItems.filter((item: any) => 
        item.subcategory === subcategory
      );
    }
    
    // Filter by search query
    if (search) {
      const searchLower = search.toLowerCase();
      filteredItems = filteredItems.filter((item: any) => 
        item.name.toLowerCase().includes(searchLower) ||
        item.description.toLowerCase().includes(searchLower) ||
        item.tags.some((tag: string) => tag.toLowerCase().includes(searchLower))
      );
    }
    
    // Filter by featured status
    if (featured === 'true') {
      filteredItems = filteredItems.filter((item: any) => 
        item.isFeatured === true
      );
    }
    
    // Filter by popular status
    if (popular === 'true') {
      filteredItems = filteredItems.filter((item: any) => 
        item.isPopular === true
      );
    }
    
    // Filter by signature status
    if (signature === 'true') {
      filteredItems = filteredItems.filter((item: any) => 
        item.isSignature === true
      );
    }
    
    // Filter by availability
    if (available === 'true') {
      filteredItems = filteredItems.filter((item: any) => 
        item.availability?.isAvailable === true
      );
    }
    
    // Sort by popularity, rating, and order count
    filteredItems.sort((a: any, b: any) => {
      // Signature items first
      if (a.isSignature && !b.isSignature) return -1;
      if (!a.isSignature && b.isSignature) return 1;
      
      // Popular items next
      if (a.isPopular && !b.isPopular) return -1;
      if (!a.isPopular && b.isPopular) return 1;
      
      // Then by rating
      if (b.rating !== a.rating) return b.rating - a.rating;
      
      // Finally by order count
      return b.orderCount - a.orderCount;
    });
    
    return NextResponse.json(filteredItems);
  } catch (error) {
    console.error('Error fetching menu items:', error);
    return NextResponse.json({ error: 'Failed to fetch menu items' }, { status: 500 });
  }
}

// POST - Create new menu item
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.name || !body.restaurantId || !body.category || !body.price?.basePrice) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    const menuItems = readMenuItems();
    
    const now = new Date().toISOString();
    
    const newMenuItem = {
      id: `menu-item-${Date.now()}`,
      restaurantId: body.restaurantId,
      providerId: body.providerId,
      name: body.name,
      slug: body.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, ''),
      description: body.description || '',
      shortDescription: body.shortDescription || body.description?.substring(0, 100) || '',
      category: body.category,
      subcategory: body.subcategory || '',
      cuisineType: body.cuisineType || 'vietnamese',
      price: {
        basePrice: body.price.basePrice,
        currency: body.price.currency || 'VND',
        comparePrice: body.price.comparePrice || null,
        costPrice: body.price.costPrice || 0
      },
      variants: body.variants || [
        {
          id: 'variant-default',
          name: 'Mặc định',
          price: body.price.basePrice,
          description: ''
        }
      ],
      images: body.images || [],
      ingredients: body.ingredients || [],
      allergens: body.allergens || [],
      dietaryInfo: {
        isVegetarian: body.dietaryInfo?.isVegetarian || false,
        isVegan: body.dietaryInfo?.isVegan || false,
        isGlutenFree: body.dietaryInfo?.isGlutenFree || false,
        isHalal: body.dietaryInfo?.isHalal || false,
        isDairyFree: body.dietaryInfo?.isDairyFree || false,
        isNutFree: body.dietaryInfo?.isNutFree || false,
        isSpicy: body.dietaryInfo?.isSpicy || false,
        spicyLevel: body.dietaryInfo?.spicyLevel || 0
      },
      nutritionalInfo: body.nutritionalInfo || {},
      preparationTime: {
        min: body.preparationTime?.min || 10,
        max: body.preparationTime?.max || 15,
        unit: 'minutes'
      },
      availability: {
        isAvailable: body.availability?.isAvailable ?? true,
        availableFrom: body.availability?.availableFrom || '06:00',
        availableTo: body.availability?.availableTo || '22:00',
        daysAvailable: body.availability?.daysAvailable || ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
        stockQuantity: body.availability?.stockQuantity || null,
        isUnlimited: body.availability?.isUnlimited ?? true
      },
      customizations: body.customizations || [],
      tags: body.tags || [],
      isSignature: body.isSignature || false,
      isPopular: body.isPopular || false,
      isFeatured: body.isFeatured || false,
      isNew: body.isNew || false,
      rating: 0,
      totalReviews: 0,
      orderCount: 0,
      status: body.status || 'active',
      seo: {
        title: body.seo?.title || body.name,
        description: body.seo?.description || body.description,
        keywords: body.seo?.keywords || []
      },
      createdAt: now,
      updatedAt: now
    };
    
    menuItems.push(newMenuItem);
    writeMenuItems(menuItems);
    
    return NextResponse.json(newMenuItem, { status: 201 });
  } catch (error) {
    console.error('Error creating menu item:', error);
    return NextResponse.json({ error: 'Failed to create menu item' }, { status: 500 });
  }
}

// PUT - Update menu item
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    
    if (!body.id) {
      return NextResponse.json({ error: 'Menu item ID is required' }, { status: 400 });
    }
    
    const menuItems = readMenuItems();
    const itemIndex = menuItems.findIndex((item: any) => item.id === body.id);
    
    if (itemIndex === -1) {
      return NextResponse.json({ error: 'Menu item not found' }, { status: 404 });
    }
    
    const existingItem = menuItems[itemIndex];
    const now = new Date().toISOString();
    
    // Update menu item fields
    const updatedItem = {
      ...existingItem,
      ...body,
      updatedAt: now
    };
    
    // Update slug if name changed
    if (body.name && body.name !== existingItem.name) {
      updatedItem.slug = body.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
    }
    
    menuItems[itemIndex] = updatedItem;
    writeMenuItems(menuItems);
    
    return NextResponse.json(updatedItem);
  } catch (error) {
    console.error('Error updating menu item:', error);
    return NextResponse.json({ error: 'Failed to update menu item' }, { status: 500 });
  }
}

// DELETE - Delete menu item
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json({ error: 'Menu item ID is required' }, { status: 400 });
    }
    
    const menuItems = readMenuItems();
    const itemIndex = menuItems.findIndex((item: any) => item.id === id);
    
    if (itemIndex === -1) {
      return NextResponse.json({ error: 'Menu item not found' }, { status: 404 });
    }
    
    menuItems.splice(itemIndex, 1);
    writeMenuItems(menuItems);
    
    return NextResponse.json({ message: 'Menu item deleted successfully' });
  } catch (error) {
    console.error('Error deleting menu item:', error);
    return NextResponse.json({ error: 'Failed to delete menu item' }, { status: 500 });
  }
}
