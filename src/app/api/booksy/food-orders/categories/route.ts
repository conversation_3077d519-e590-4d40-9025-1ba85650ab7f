import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const CATEGORIES_FILE = path.join(process.cwd(), 'data/apps/booksy/food-orders/food-categories.json');

// Ensure data directory exists
function ensureDataDirectory() {
  const dataDir = path.dirname(CATEGORIES_FILE);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
}

// Read categories from file
function readCategories() {
  ensureDataDirectory();
  try {
    if (fs.existsSync(CATEGORIES_FILE)) {
      const data = fs.readFileSync(CATEGORIES_FILE, 'utf8');
      return JSON.parse(data);
    }
    return {
      cuisineTypes: [],
      foodCategories: [],
      subcategories: [],
      dietaryRestrictions: [],
      allergens: [],
      spicyLevels: [],
      priceRanges: []
    };
  } catch (error) {
    console.error('Error reading categories:', error);
    return {
      cuisineTypes: [],
      foodCategories: [],
      subcategories: [],
      dietaryRestrictions: [],
      allergens: [],
      spicyLevels: [],
      priceRanges: []
    };
  }
}

// GET - Fetch food categories and filters
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // cuisineTypes, foodCategories, etc.
    
    const categories = readCategories();
    
    if (type && categories[type]) {
      return NextResponse.json(categories[type]);
    }
    
    return NextResponse.json(categories);
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json({ error: 'Failed to fetch categories' }, { status: 500 });
  }
}

// POST - Add new category item
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    if (!body.type || !body.item) {
      return NextResponse.json({ error: 'Type and item are required' }, { status: 400 });
    }
    
    const categories = readCategories();
    
    if (!categories[body.type]) {
      return NextResponse.json({ error: 'Invalid category type' }, { status: 400 });
    }
    
    // Add ID if not provided
    if (!body.item.id) {
      body.item.id = `${body.type}-${Date.now()}`;
    }
    
    categories[body.type].push(body.item);
    
    // Write back to file
    fs.writeFileSync(CATEGORIES_FILE, JSON.stringify(categories, null, 2));
    
    return NextResponse.json(body.item, { status: 201 });
  } catch (error) {
    console.error('Error adding category item:', error);
    return NextResponse.json({ error: 'Failed to add category item' }, { status: 500 });
  }
}
