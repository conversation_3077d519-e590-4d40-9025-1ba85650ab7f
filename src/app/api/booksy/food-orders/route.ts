import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const ORDERS_FILE = path.join(process.cwd(), 'data/apps/booksy/food-orders/orders.json');
const RESTAURANTS_FILE = path.join(process.cwd(), 'data/apps/booksy/food-orders/restaurants.json');
const MENU_ITEMS_FILE = path.join(process.cwd(), 'data/apps/booksy/food-orders/menu-items.json');

// Ensure data directory exists
function ensureDataDirectory() {
  const dataDir = path.dirname(ORDERS_FILE);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
}

// Read orders from file
function readOrders() {
  ensureDataDirectory();
  try {
    if (fs.existsSync(ORDERS_FILE)) {
      const data = fs.readFileSync(ORDERS_FILE, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error('Error reading orders:', error);
    return [];
  }
}

// Write orders to file
function writeOrders(orders: any[]) {
  ensureDataDirectory();
  try {
    fs.writeFileSync(ORDERS_FILE, JSON.stringify(orders, null, 2));
  } catch (error) {
    console.error('Error writing orders:', error);
    throw error;
  }
}

// Read restaurants from file
function readRestaurants() {
  try {
    if (fs.existsSync(RESTAURANTS_FILE)) {
      const data = fs.readFileSync(RESTAURANTS_FILE, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error('Error reading restaurants:', error);
    return [];
  }
}

// Read menu items from file
function readMenuItems() {
  try {
    if (fs.existsSync(MENU_ITEMS_FILE)) {
      const data = fs.readFileSync(MENU_ITEMS_FILE, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error('Error reading menu items:', error);
    return [];
  }
}

// Generate order number
function generateOrderNumber() {
  const now = new Date();
  const dateStr = now.toISOString().slice(2, 10).replace(/-/g, '');
  const timeStr = String(Date.now()).slice(-3);
  return `FO${dateStr}${timeStr}`;
}

// Calculate estimated delivery time
function calculateDeliveryTime(restaurant: any, items: any[]) {
  const maxPrepTime = Math.max(...items.map(item => item.preparationTime?.max || 15));
  const baseDeliveryTime = restaurant.deliveryInfo?.estimatedDeliveryTime?.min || 20;
  
  return {
    min: maxPrepTime + baseDeliveryTime,
    max: maxPrepTime + (restaurant.deliveryInfo?.estimatedDeliveryTime?.max || 35)
  };
}

// GET - Fetch orders
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const customerId = searchParams.get('customerId');
    const restaurantId = searchParams.get('restaurantId');
    const status = searchParams.get('status');
    
    const orders = readOrders();
    
    let filteredOrders = orders;
    
    if (customerId) {
      filteredOrders = filteredOrders.filter((order: any) => order.customerId === customerId);
    }
    
    if (restaurantId) {
      filteredOrders = filteredOrders.filter((order: any) => order.restaurantId === restaurantId);
    }
    
    if (status) {
      filteredOrders = filteredOrders.filter((order: any) => order.status === status);
    }
    
    // Sort by creation date (newest first)
    filteredOrders.sort((a: any, b: any) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    
    return NextResponse.json(filteredOrders);
  } catch (error) {
    console.error('Error fetching orders:', error);
    return NextResponse.json({ error: 'Failed to fetch orders' }, { status: 500 });
  }
}

// POST - Create new order
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.customerId || !body.restaurantId || !body.items || !Array.isArray(body.items) || body.items.length === 0) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    const orders = readOrders();
    const restaurants = readRestaurants();
    const menuItems = readMenuItems();
    
    // Find restaurant
    const restaurant = restaurants.find((r: any) => r.id === body.restaurantId);
    if (!restaurant) {
      return NextResponse.json({ error: 'Restaurant not found' }, { status: 404 });
    }
    
    // Validate and process items
    const processedItems = [];
    let subtotal = 0;
    
    for (const orderItem of body.items) {
      const menuItem = menuItems.find((m: any) => m.id === orderItem.menuItemId);
      if (!menuItem) {
        return NextResponse.json({ error: `Menu item ${orderItem.menuItemId} not found` }, { status: 404 });
      }
      
      // Find variant
      const variant = menuItem.variants.find((v: any) => v.id === orderItem.variantId);
      if (!variant) {
        return NextResponse.json({ error: `Variant ${orderItem.variantId} not found` }, { status: 404 });
      }
      
      // Calculate item total
      let itemTotal = variant.price * orderItem.quantity;
      
      // Add customization costs
      if (orderItem.customizations) {
        for (const customization of orderItem.customizations) {
          if (customization.selectedOptions) {
            for (const option of customization.selectedOptions) {
              itemTotal += (option.priceAdjustment || 0) * orderItem.quantity;
            }
          } else if (customization.selectedOption) {
            itemTotal += (customization.selectedOption.priceAdjustment || 0) * orderItem.quantity;
          }
        }
      }
      
      processedItems.push({
        id: `order-item-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        menuItemId: orderItem.menuItemId,
        name: menuItem.name,
        variantId: orderItem.variantId,
        variantName: variant.name,
        quantity: orderItem.quantity,
        unitPrice: variant.price,
        totalPrice: variant.price * orderItem.quantity,
        customizations: orderItem.customizations || [],
        specialInstructions: orderItem.specialInstructions || '',
        itemTotal
      });
      
      subtotal += itemTotal;
    }
    
    // Calculate pricing
    const deliveryFee = subtotal >= restaurant.deliveryInfo.freeDeliveryThreshold ? 0 : restaurant.deliveryInfo.deliveryFee;
    const serviceFee = Math.round(subtotal * 0.02); // 2% service fee
    const discount = body.discount || 0;
    const tax = 0; // No tax for now
    const total = subtotal + deliveryFee + serviceFee - discount + tax;
    
    // Calculate delivery time
    const estimatedDeliveryTime = calculateDeliveryTime(restaurant, processedItems);
    
    const now = new Date().toISOString();
    
    const newOrder = {
      id: `food-order-${Date.now()}`,
      orderNumber: generateOrderNumber(),
      customerId: body.customerId,
      customerInfo: body.customerInfo,
      restaurantId: body.restaurantId,
      restaurantInfo: {
        name: restaurant.name,
        phone: restaurant.contact.phone,
        address: restaurant.contact.address.fullAddress
      },
      providerId: restaurant.providerId,
      orderType: body.orderType || 'delivery',
      items: processedItems,
      pricing: {
        subtotal,
        deliveryFee,
        serviceFee,
        discount,
        discountReason: body.discountReason || '',
        tax,
        total,
        currency: 'VND'
      },
      deliveryInfo: body.deliveryInfo,
      paymentInfo: {
        method: body.paymentMethod || 'cash',
        status: 'pending',
        transactionId: null,
        paidAt: null,
        amount: total
      },
      timeline: {
        orderedAt: now,
        confirmedAt: null,
        preparingAt: null,
        readyAt: null,
        pickedUpAt: null,
        deliveredAt: null,
        completedAt: null
      },
      status: 'pending',
      statusHistory: [
        {
          status: 'pending',
          timestamp: now,
          note: 'Đơn hàng được tạo'
        }
      ],
      rating: null,
      promotions: body.promotions || [],
      specialRequests: body.specialRequests || [],
      isFirstOrder: body.isFirstOrder || false,
      estimatedPreparationTime: Math.max(...processedItems.map(item => {
        const menuItem = menuItems.find((m: any) => m.id === item.menuItemId);
        return menuItem?.preparationTime?.max || 15;
      })),
      actualPreparationTime: null,
      estimatedDeliveryTime,
      createdAt: now,
      updatedAt: now
    };
    
    orders.push(newOrder);
    writeOrders(orders);
    
    return NextResponse.json(newOrder, { status: 201 });
  } catch (error) {
    console.error('Error creating order:', error);
    return NextResponse.json({ error: 'Failed to create order' }, { status: 500 });
  }
}

// PUT - Update order
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    
    if (!body.id) {
      return NextResponse.json({ error: 'Order ID is required' }, { status: 400 });
    }
    
    const orders = readOrders();
    const orderIndex = orders.findIndex((order: any) => order.id === body.id);
    
    if (orderIndex === -1) {
      return NextResponse.json({ error: 'Order not found' }, { status: 404 });
    }
    
    const existingOrder = orders[orderIndex];
    const now = new Date().toISOString();
    
    // Update order fields
    const updatedOrder = {
      ...existingOrder,
      ...body,
      updatedAt: now
    };
    
    // Handle status changes
    if (body.status && body.status !== existingOrder.status) {
      // Update timeline
      const timelineField = getTimelineField(body.status);
      if (timelineField) {
        updatedOrder.timeline[timelineField] = now;
      }
      
      // Add to status history
      updatedOrder.statusHistory.push({
        status: body.status,
        timestamp: now,
        note: body.statusNote || getStatusNote(body.status)
      });
    }
    
    orders[orderIndex] = updatedOrder;
    writeOrders(orders);
    
    return NextResponse.json(updatedOrder);
  } catch (error) {
    console.error('Error updating order:', error);
    return NextResponse.json({ error: 'Failed to update order' }, { status: 500 });
  }
}

function getTimelineField(status: string): string | null {
  const mapping: { [key: string]: string } = {
    'confirmed': 'confirmedAt',
    'preparing': 'preparingAt',
    'ready': 'readyAt',
    'picked_up': 'pickedUpAt',
    'out_for_delivery': 'pickedUpAt',
    'delivered': 'deliveredAt',
    'completed': 'completedAt'
  };
  return mapping[status] || null;
}

function getStatusNote(status: string): string {
  const notes: { [key: string]: string } = {
    'confirmed': 'Nhà hàng xác nhận đơn hàng',
    'preparing': 'Bắt đầu chuẩn bị món ăn',
    'ready': 'Món ăn đã sẵn sàng',
    'picked_up': 'Shipper đã nhận hàng',
    'out_for_delivery': 'Đang giao hàng',
    'delivered': 'Giao hàng thành công',
    'completed': 'Đơn hàng hoàn thành',
    'cancelled': 'Đơn hàng bị hủy'
  };
  return notes[status] || `Trạng thái cập nhật: ${status}`;
}
