import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const MENU_ITEMS_FILE = path.join(process.cwd(), 'data/apps/booksy/food-orders/menu-items.json');
const RESTAURANTS_FILE = path.join(process.cwd(), 'data/apps/booksy/food-orders/restaurants.json');
const ABNSHOPS_PRODUCTS_FILE = path.join(process.cwd(), 'data/apps/platforms/abnshops/ecommerce/products.json');
const ABNSHOPS_STORES_FILE = path.join(process.cwd(), 'data/apps/platforms/abnshops/ecommerce/stores.json');

// Read data from files
function readMenuItems() {
  try {
    if (fs.existsSync(MENU_ITEMS_FILE)) {
      const data = fs.readFileSync(MENU_ITEMS_FILE, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error('Error reading menu items:', error);
    return [];
  }
}

function readRestaurants() {
  try {
    if (fs.existsSync(RESTAURANTS_FILE)) {
      const data = fs.readFileSync(RESTAURANTS_FILE, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error('Error reading restaurants:', error);
    return [];
  }
}

function readABNShopsProducts() {
  try {
    if (fs.existsSync(ABNSHOPS_PRODUCTS_FILE)) {
      const data = fs.readFileSync(ABNSHOPS_PRODUCTS_FILE, 'utf8');
      return JSON.parse(data);
    }
    return { products: [] };
  } catch (error) {
    console.error('Error reading ABN Shops products:', error);
    return { products: [] };
  }
}

function readABNShopsStores() {
  try {
    if (fs.existsSync(ABNSHOPS_STORES_FILE)) {
      const data = fs.readFileSync(ABNSHOPS_STORES_FILE, 'utf8');
      return JSON.parse(data);
    }
    return { stores: [] };
  } catch (error) {
    console.error('Error reading ABN Shops stores:', error);
    return { stores: [] };
  }
}

function writeABNShopsProducts(productsData: any) {
  try {
    const dir = path.dirname(ABNSHOPS_PRODUCTS_FILE);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(ABNSHOPS_PRODUCTS_FILE, JSON.stringify(productsData, null, 2));
  } catch (error) {
    console.error('Error writing ABN Shops products:', error);
    throw error;
  }
}

function writeABNShopsStores(storesData: any) {
  try {
    const dir = path.dirname(ABNSHOPS_STORES_FILE);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(ABNSHOPS_STORES_FILE, JSON.stringify(storesData, null, 2));
  } catch (error) {
    console.error('Error writing ABN Shops stores:', error);
    throw error;
  }
}

// Convert menu item to ABN Shops product
function convertMenuItemToProduct(menuItem: any, restaurant: any, storeId: string) {
  const now = new Date().toISOString();
  
  return {
    id: `food_${menuItem.id}`,
    storeId: storeId,
    name: menuItem.name,
    description: menuItem.description,
    shortDescription: menuItem.shortDescription || menuItem.description.substring(0, 100),
    category: 'food',
    subcategory: menuItem.subcategory,
    brand: restaurant.name,
    status: menuItem.status === 'active' ? 'active' : 'draft',
    visibility: 'public',
    basePrice: menuItem.price.basePrice / 1000, // Convert VND to thousands for display
    comparePrice: null,
    costPrice: menuItem.price.costPrice ? menuItem.price.costPrice / 1000 : 0,
    currency: 'VND',
    images: menuItem.images.map((img: any, index: number) => ({
      id: `img_${menuItem.id}_${index}`,
      url: img.url,
      alt: img.alt,
      isPrimary: img.isPrimary,
      sortOrder: index + 1
    })),
    variants: menuItem.variants.map((variant: any) => ({
      id: `var_${variant.id}`,
      name: variant.name,
      price: variant.price / 1000,
      description: variant.description,
      sku: `${menuItem.id}_${variant.id}`,
      inventory: {
        trackQuantity: false,
        quantity: 999,
        allowBackorder: true
      }
    })),
    attributes: [
      {
        name: 'Preparation Time',
        value: `${menuItem.preparationTime.min}-${menuItem.preparationTime.max} minutes`
      },
      {
        name: 'Cuisine Type',
        value: menuItem.cuisineType
      },
      {
        name: 'Dietary Info',
        value: Object.entries(menuItem.dietaryInfo || {})
          .filter(([key, value]) => value === true)
          .map(([key]) => key.replace(/([A-Z])/g, ' $1').trim())
          .join(', ')
      }
    ].filter(attr => attr.value),
    tags: [
      ...menuItem.tags,
      menuItem.cuisineType,
      menuItem.category,
      menuItem.subcategory,
      ...(menuItem.isSignature ? ['signature'] : []),
      ...(menuItem.isPopular ? ['popular'] : []),
      ...(menuItem.isFeatured ? ['featured'] : [])
    ].filter(Boolean),
    collections: [
      restaurant.name,
      menuItem.category,
      menuItem.cuisineType
    ],
    seo: {
      title: `${menuItem.name} - ${restaurant.name}`,
      description: `${menuItem.description} Đặt ngay tại ${restaurant.name}`,
      keywords: [menuItem.name, restaurant.name, menuItem.cuisineType, menuItem.category],
      slug: `${menuItem.name}-${restaurant.name}`.toLowerCase().replace(/[^a-z0-9]+/g, '-')
    },
    inventory: {
      trackQuantity: false,
      totalQuantity: 999,
      totalReserved: 0,
      totalAvailable: 999,
      lowStockThreshold: 10,
      allowBackorder: true,
      inventoryPolicy: 'continue'
    },
    shipping: {
      weight: 0.5,
      dimensions: { length: 20, width: 20, height: 10, unit: 'cm' },
      requiresShipping: true,
      shippingClass: 'food',
      freeShipping: false,
      shippingCost: restaurant.deliveryInfo?.deliveryFee / 1000 || 15
    },
    analytics: {
      sales: menuItem.orderCount || 0,
      revenue: (menuItem.orderCount || 0) * menuItem.price.basePrice / 1000,
      views: 0,
      clicks: 0,
      conversionRate: 0,
      rating: menuItem.rating || 0,
      reviews: menuItem.totalReviews || 0,
      wishlistAdds: 0,
      cartAdds: 0
    },
    customFields: [
      {
        name: 'restaurant_id',
        value: restaurant.id,
        type: 'text'
      },
      {
        name: 'provider_id',
        value: restaurant.providerId,
        type: 'text'
      },
      {
        name: 'menu_item_id',
        value: menuItem.id,
        type: 'text'
      },
      {
        name: 'allergens',
        value: menuItem.allergens?.join(', ') || '',
        type: 'text'
      },
      {
        name: 'spicy_level',
        value: menuItem.dietaryInfo?.spicyLevel?.toString() || '0',
        type: 'number'
      }
    ],
    createdAt: menuItem.createdAt || now,
    updatedAt: now,
    publishedAt: menuItem.status === 'active' ? now : null
  };
}

// GET - Get integration status and synced products
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const providerId = searchParams.get('providerId');
    const restaurantId = searchParams.get('restaurantId');
    
    if (!providerId) {
      return NextResponse.json({ error: 'Provider ID is required' }, { status: 400 });
    }
    
    const restaurants = readRestaurants();
    const menuItems = readMenuItems();
    const abnShopsProducts = readABNShopsProducts();
    const abnShopsStores = readABNShopsStores();
    
    // Filter restaurants by provider
    const providerRestaurants = restaurants.filter((r: any) => r.providerId === providerId);
    
    // Filter by specific restaurant if provided
    const targetRestaurants = restaurantId 
      ? providerRestaurants.filter((r: any) => r.id === restaurantId)
      : providerRestaurants;
    
    // Get synced products for these restaurants
    const syncedProducts = abnShopsProducts.products.filter((product: any) => 
      product.customFields?.some((field: any) => 
        field.name === 'provider_id' && field.value === providerId
      )
    );
    
    // Get integration statistics
    const stats = {
      totalRestaurants: targetRestaurants.length,
      totalMenuItems: menuItems.filter((item: any) => 
        targetRestaurants.some((r: any) => r.id === item.restaurantId)
      ).length,
      syncedProducts: syncedProducts.length,
      totalStores: abnShopsStores.stores?.filter((store: any) => 
        store.ownerId === providerId
      ).length || 0
    };
    
    return NextResponse.json({
      success: true,
      data: {
        restaurants: targetRestaurants,
        syncedProducts,
        stats
      }
    });
  } catch (error) {
    console.error('Error getting integration status:', error);
    return NextResponse.json({ error: 'Failed to get integration status' }, { status: 500 });
  }
}

// POST - Sync menu items to ABN Shops
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { providerId, restaurantId, storeId, syncOptions = {} } = body;
    
    if (!providerId || !restaurantId) {
      return NextResponse.json({ error: 'Provider ID and Restaurant ID are required' }, { status: 400 });
    }
    
    const restaurants = readRestaurants();
    const menuItems = readMenuItems();
    const abnShopsProducts = readABNShopsProducts();
    const abnShopsStores = readABNShopsStores();
    
    // Find the restaurant
    const restaurant = restaurants.find((r: any) => r.id === restaurantId && r.providerId === providerId);
    if (!restaurant) {
      return NextResponse.json({ error: 'Restaurant not found' }, { status: 404 });
    }
    
    // Get menu items for this restaurant
    const restaurantMenuItems = menuItems.filter((item: any) => 
      item.restaurantId === restaurantId && 
      (syncOptions.includeInactive || item.status === 'active')
    );
    
    let targetStoreId = storeId;
    
    // Create ABN Shop store if not provided
    if (!targetStoreId) {
      const newStore = {
        id: `store_food_${restaurant.id}`,
        ownerId: providerId,
        name: `${restaurant.name} - Online Store`,
        description: restaurant.description,
        category: 'food',
        subcategory: 'restaurant',
        status: 'active',
        visibility: 'public',
        theme: 'food-delivery',
        settings: {
          currency: 'VND',
          timezone: 'Asia/Ho_Chi_Minh',
          language: 'vi',
          allowGuestCheckout: true,
          requireAccountForPurchase: false,
          enableReviews: true,
          enableWishlist: true,
          enableCompareProducts: false
        },
        contact: restaurant.contact,
        businessInfo: restaurant.businessInfo,
        branding: {
          logo: restaurant.images?.[0]?.url || '',
          primaryColor: '#f97316',
          secondaryColor: '#fb923c',
          accentColor: '#fed7aa'
        },
        seo: {
          title: `${restaurant.name} - Online Food Store`,
          description: `Order delicious food online from ${restaurant.name}. ${restaurant.description}`,
          keywords: [restaurant.name, 'food delivery', 'online ordering', restaurant.cuisineType].flat()
        },
        analytics: {
          googleAnalyticsId: '',
          facebookPixelId: '',
          enableTracking: true
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      if (!abnShopsStores.stores) {
        abnShopsStores.stores = [];
      }
      abnShopsStores.stores.push(newStore);
      writeABNShopsStores(abnShopsStores);
      targetStoreId = newStore.id;
    }
    
    // Convert menu items to products
    const newProducts = restaurantMenuItems.map((menuItem: any) => 
      convertMenuItemToProduct(menuItem, restaurant, targetStoreId)
    );
    
    // Remove existing products for this restaurant if replacing
    if (syncOptions.replaceExisting) {
      abnShopsProducts.products = abnShopsProducts.products.filter((product: any) => 
        !product.customFields?.some((field: any) => 
          field.name === 'restaurant_id' && field.value === restaurantId
        )
      );
    }
    
    // Add new products
    abnShopsProducts.products.push(...newProducts);
    writeABNShopsProducts(abnShopsProducts);
    
    return NextResponse.json({
      success: true,
      data: {
        storeId: targetStoreId,
        syncedProducts: newProducts.length,
        products: newProducts
      }
    });
  } catch (error) {
    console.error('Error syncing to ABN Shops:', error);
    return NextResponse.json({ error: 'Failed to sync to ABN Shops' }, { status: 500 });
  }
}

// PUT - Update sync settings
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { providerId, restaurantId, syncSettings } = body;
    
    if (!providerId || !restaurantId) {
      return NextResponse.json({ error: 'Provider ID and Restaurant ID are required' }, { status: 400 });
    }
    
    // In a real implementation, you would store sync settings in a database
    // For now, we'll just return success
    
    return NextResponse.json({
      success: true,
      data: {
        providerId,
        restaurantId,
        syncSettings
      }
    });
  } catch (error) {
    console.error('Error updating sync settings:', error);
    return NextResponse.json({ error: 'Failed to update sync settings' }, { status: 500 });
  }
}

// DELETE - Remove synced products
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const providerId = searchParams.get('providerId');
    const restaurantId = searchParams.get('restaurantId');
    
    if (!providerId || !restaurantId) {
      return NextResponse.json({ error: 'Provider ID and Restaurant ID are required' }, { status: 400 });
    }
    
    const abnShopsProducts = readABNShopsProducts();
    
    // Remove products for this restaurant
    const originalCount = abnShopsProducts.products.length;
    abnShopsProducts.products = abnShopsProducts.products.filter((product: any) => 
      !product.customFields?.some((field: any) => 
        field.name === 'restaurant_id' && field.value === restaurantId
      )
    );
    
    const removedCount = originalCount - abnShopsProducts.products.length;
    writeABNShopsProducts(abnShopsProducts);
    
    return NextResponse.json({
      success: true,
      data: {
        removedProducts: removedCount
      }
    });
  } catch (error) {
    console.error('Error removing synced products:', error);
    return NextResponse.json({ error: 'Failed to remove synced products' }, { status: 500 });
  }
}
