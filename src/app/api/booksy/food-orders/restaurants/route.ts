import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const RESTAURANTS_FILE = path.join(process.cwd(), 'data/apps/booksy/food-orders/restaurants.json');

// Ensure data directory exists
function ensureDataDirectory() {
  const dataDir = path.dirname(RESTAURANTS_FILE);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
}

// Read restaurants from file
function readRestaurants() {
  ensureDataDirectory();
  try {
    if (fs.existsSync(RESTAURANTS_FILE)) {
      const data = fs.readFileSync(RESTAURANTS_FILE, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error('Error reading restaurants:', error);
    return [];
  }
}

// Write restaurants to file
function writeRestaurants(restaurants: any[]) {
  ensureDataDirectory();
  try {
    fs.writeFileSync(RESTAURANTS_FILE, JSON.stringify(restaurants, null, 2));
  } catch (error) {
    console.error('Error writing restaurants:', error);
    throw error;
  }
}

// GET - Fetch restaurants
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const cuisineType = searchParams.get('cuisineType');
    const priceRange = searchParams.get('priceRange');
    const search = searchParams.get('search');
    const featured = searchParams.get('featured');
    const providerId = searchParams.get('providerId');
    
    const restaurants = readRestaurants();
    
    let filteredRestaurants = restaurants;
    
    // Filter by provider ID
    if (providerId) {
      filteredRestaurants = filteredRestaurants.filter((restaurant: any) => 
        restaurant.providerId === providerId
      );
    }
    
    // Filter by category
    if (category && category !== 'all') {
      filteredRestaurants = filteredRestaurants.filter((restaurant: any) => 
        restaurant.category === category
      );
    }
    
    // Filter by cuisine type
    if (cuisineType && cuisineType !== 'all') {
      filteredRestaurants = filteredRestaurants.filter((restaurant: any) => 
        restaurant.cuisineType.includes(cuisineType)
      );
    }
    
    // Filter by price range
    if (priceRange && priceRange !== 'all') {
      filteredRestaurants = filteredRestaurants.filter((restaurant: any) => 
        restaurant.priceRange === priceRange
      );
    }
    
    // Filter by search query
    if (search) {
      const searchLower = search.toLowerCase();
      filteredRestaurants = filteredRestaurants.filter((restaurant: any) => 
        restaurant.name.toLowerCase().includes(searchLower) ||
        restaurant.description.toLowerCase().includes(searchLower) ||
        restaurant.tags.some((tag: string) => tag.toLowerCase().includes(searchLower))
      );
    }
    
    // Filter by featured status
    if (featured === 'true') {
      filteredRestaurants = filteredRestaurants.filter((restaurant: any) => 
        restaurant.isFeatured === true
      );
    }
    
    // Sort by rating (highest first) and then by featured status
    filteredRestaurants.sort((a: any, b: any) => {
      if (a.isFeatured && !b.isFeatured) return -1;
      if (!a.isFeatured && b.isFeatured) return 1;
      return b.rating - a.rating;
    });
    
    return NextResponse.json(filteredRestaurants);
  } catch (error) {
    console.error('Error fetching restaurants:', error);
    return NextResponse.json({ error: 'Failed to fetch restaurants' }, { status: 500 });
  }
}

// POST - Create new restaurant
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.name || !body.providerId || !body.category) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    const restaurants = readRestaurants();
    
    const now = new Date().toISOString();
    
    const newRestaurant = {
      id: `restaurant-${Date.now()}`,
      providerId: body.providerId,
      name: body.name,
      slug: body.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, ''),
      description: body.description || '',
      shortDescription: body.shortDescription || body.description?.substring(0, 100) || '',
      cuisineType: body.cuisineType || ['vietnamese'],
      category: body.category,
      subcategory: body.subcategory || '',
      rating: 0,
      totalReviews: 0,
      priceRange: body.priceRange || 'budget',
      averageOrderValue: 0,
      status: body.status || 'active',
      isVerified: body.isVerified || false,
      isFeatured: body.isFeatured || false,
      images: body.images || [],
      contact: body.contact || {},
      businessInfo: body.businessInfo || {},
      operatingHours: body.operatingHours || {
        monday: { open: '08:00', close: '22:00', isOpen: true },
        tuesday: { open: '08:00', close: '22:00', isOpen: true },
        wednesday: { open: '08:00', close: '22:00', isOpen: true },
        thursday: { open: '08:00', close: '22:00', isOpen: true },
        friday: { open: '08:00', close: '22:00', isOpen: true },
        saturday: { open: '08:00', close: '22:00', isOpen: true },
        sunday: { open: '08:00', close: '22:00', isOpen: true }
      },
      deliveryInfo: {
        isDeliveryAvailable: body.deliveryInfo?.isDeliveryAvailable ?? true,
        deliveryFee: body.deliveryInfo?.deliveryFee || 15000,
        freeDeliveryThreshold: body.deliveryInfo?.freeDeliveryThreshold || 200000,
        estimatedDeliveryTime: body.deliveryInfo?.estimatedDeliveryTime || { min: 20, max: 35 },
        deliveryRadius: body.deliveryInfo?.deliveryRadius || 5,
        deliveryZones: body.deliveryInfo?.deliveryZones || []
      },
      paymentMethods: body.paymentMethods || ['cash', 'momo', 'zalopay'],
      specialFeatures: body.specialFeatures || [],
      tags: body.tags || [],
      promotions: body.promotions || [],
      stats: {
        totalOrders: 0,
        completedOrders: 0,
        cancelledOrders: 0,
        averageRating: 0,
        responseTime: '10 phút',
        acceptanceRate: 100,
        onTimeDeliveryRate: 100
      },
      createdAt: now,
      updatedAt: now
    };
    
    restaurants.push(newRestaurant);
    writeRestaurants(restaurants);
    
    return NextResponse.json(newRestaurant, { status: 201 });
  } catch (error) {
    console.error('Error creating restaurant:', error);
    return NextResponse.json({ error: 'Failed to create restaurant' }, { status: 500 });
  }
}

// PUT - Update restaurant
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    
    if (!body.id) {
      return NextResponse.json({ error: 'Restaurant ID is required' }, { status: 400 });
    }
    
    const restaurants = readRestaurants();
    const restaurantIndex = restaurants.findIndex((restaurant: any) => restaurant.id === body.id);
    
    if (restaurantIndex === -1) {
      return NextResponse.json({ error: 'Restaurant not found' }, { status: 404 });
    }
    
    const existingRestaurant = restaurants[restaurantIndex];
    const now = new Date().toISOString();
    
    // Update restaurant fields
    const updatedRestaurant = {
      ...existingRestaurant,
      ...body,
      updatedAt: now
    };
    
    // Update slug if name changed
    if (body.name && body.name !== existingRestaurant.name) {
      updatedRestaurant.slug = body.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
    }
    
    restaurants[restaurantIndex] = updatedRestaurant;
    writeRestaurants(restaurants);
    
    return NextResponse.json(updatedRestaurant);
  } catch (error) {
    console.error('Error updating restaurant:', error);
    return NextResponse.json({ error: 'Failed to update restaurant' }, { status: 500 });
  }
}

// DELETE - Delete restaurant
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json({ error: 'Restaurant ID is required' }, { status: 400 });
    }
    
    const restaurants = readRestaurants();
    const restaurantIndex = restaurants.findIndex((restaurant: any) => restaurant.id === id);
    
    if (restaurantIndex === -1) {
      return NextResponse.json({ error: 'Restaurant not found' }, { status: 404 });
    }
    
    restaurants.splice(restaurantIndex, 1);
    writeRestaurants(restaurants);
    
    return NextResponse.json({ message: 'Restaurant deleted successfully' });
  } catch (error) {
    console.error('Error deleting restaurant:', error);
    return NextResponse.json({ error: 'Failed to delete restaurant' }, { status: 500 });
  }
}
