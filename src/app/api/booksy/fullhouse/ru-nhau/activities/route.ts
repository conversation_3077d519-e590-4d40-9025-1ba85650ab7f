import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const ACTIVITIES_FILE = path.join(process.cwd(), 'data/apps/booksy/fullhouse/ru-nhau/activities.json');
const CHAT_MESSAGES_FILE = path.join(process.cwd(), 'data/apps/booksy/fullhouse/ru-nhau/chat-messages.json');

// Ensure data directory exists
function ensureDataDirectory() {
  const dataDir = path.dirname(ACTIVITIES_FILE);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
}

// Read activities from file
function readActivities() {
  ensureDataDirectory();
  try {
    if (fs.existsSync(ACTIVITIES_FILE)) {
      const data = fs.readFileSync(ACTIVITIES_FILE, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error('Error reading activities:', error);
    return [];
  }
}

// Write activities to file
function writeActivities(activities: any[]) {
  ensureDataDirectory();
  try {
    fs.writeFileSync(ACTIVITIES_FILE, JSON.stringify(activities, null, 2));
  } catch (error) {
    console.error('Error writing activities:', error);
    throw error;
  }
}

// Read chat messages from file
function readChatMessages() {
  try {
    if (fs.existsSync(CHAT_MESSAGES_FILE)) {
      const data = fs.readFileSync(CHAT_MESSAGES_FILE, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error('Error reading chat messages:', error);
    return [];
  }
}

// Write chat messages to file
function writeChatMessages(messages: any[]) {
  try {
    fs.writeFileSync(CHAT_MESSAGES_FILE, JSON.stringify(messages, null, 2));
  } catch (error) {
    console.error('Error writing chat messages:', error);
    throw error;
  }
}

// Calculate distance between two coordinates (in meters)
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = lat1 * Math.PI/180;
  const φ2 = lat2 * Math.PI/180;
  const Δφ = (lat2-lat1) * Math.PI/180;
  const Δλ = (lon1-lon2) * Math.PI/180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
          Math.cos(φ1) * Math.cos(φ2) *
          Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  return R * c;
}

// Clean up expired activities and messages
function cleanupExpiredData() {
  const now = new Date();
  
  // Clean up activities
  const activities = readActivities();
  const activeActivities = activities.filter((activity: any) => {
    const autoDeleteTime = new Date(activity.autoDeleteAt);
    const isExpired = now > autoDeleteTime;
    const hasOnlyCreator = activity.currentMembers <= 1;
    const lastActivityTime = new Date(activity.lastActivity);
    const isInactive = (now.getTime() - lastActivityTime.getTime()) > (30 * 60 * 1000); // 30 minutes
    
    return !isExpired && !(hasOnlyCreator && isInactive);
  });
  
  if (activeActivities.length !== activities.length) {
    writeActivities(activeActivities);
  }
  
  // Clean up chat messages
  const messages = readChatMessages();
  const activeMessages = messages.filter((message: any) => {
    const autoDeleteTime = new Date(message.autoDeleteAt);
    return now <= autoDeleteTime && !message.isDeleted;
  });
  
  if (activeMessages.length !== messages.length) {
    writeChatMessages(activeMessages);
  }
}

// GET - Fetch activities
export async function GET(request: NextRequest) {
  try {
    // Clean up expired data first
    cleanupExpiredData();
    
    const { searchParams } = new URL(request.url);
    const userLat = parseFloat(searchParams.get('lat') || '0');
    const userLon = parseFloat(searchParams.get('lon') || '0');
    const radius = parseInt(searchParams.get('radius') || '1000'); // Default 1km
    const category = searchParams.get('category');
    const activityType = searchParams.get('activityType');
    const userId = searchParams.get('userId');
    
    const activities = readActivities();
    
    let filteredActivities = activities.filter((activity: any) => activity.status === 'active');
    
    // Filter by location if coordinates provided
    if (userLat && userLon) {
      filteredActivities = filteredActivities.filter((activity: any) => {
        if (!activity.location?.coordinates) return true;
        
        const distance = calculateDistance(
          userLat, userLon,
          activity.location.coordinates.latitude,
          activity.location.coordinates.longitude
        );
        
        return distance <= radius;
      });
    }
    
    // Filter by category
    if (category && category !== 'all') {
      filteredActivities = filteredActivities.filter((activity: any) => 
        activity.category === category
      );
    }
    
    // Filter by activity type
    if (activityType) {
      filteredActivities = filteredActivities.filter((activity: any) => 
        activity.activityType === activityType
      );
    }
    
    // Add distance information
    if (userLat && userLon) {
      filteredActivities = filteredActivities.map((activity: any) => {
        if (activity.location?.coordinates) {
          const distance = calculateDistance(
            userLat, userLon,
            activity.location.coordinates.latitude,
            activity.location.coordinates.longitude
          );
          return { ...activity, distance: Math.round(distance) };
        }
        return activity;
      });
    }
    
    // Sort by creation time (newest first)
    filteredActivities.sort((a: any, b: any) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
    
    return NextResponse.json(filteredActivities);
  } catch (error) {
    console.error('Error fetching activities:', error);
    return NextResponse.json({ error: 'Failed to fetch activities' }, { status: 500 });
  }
}

// POST - Create new activity
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.title || !body.creatorId || !body.category || !body.activityType) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    const activities = readActivities();
    const now = new Date().toISOString();
    const autoDeleteAt = new Date(Date.now() + 30 * 60 * 1000).toISOString(); // 30 minutes from now
    
    const newActivity = {
      id: `activity-${Date.now()}`,
      title: body.title,
      description: body.description || '',
      category: body.category,
      activityType: body.activityType,
      creatorId: body.creatorId,
      creatorInfo: body.creatorInfo,
      location: body.location || {},
      maxMembers: body.maxMembers || 6,
      currentMembers: 1,
      members: [
        {
          userId: body.creatorId,
          name: body.creatorInfo.name,
          avatar: body.creatorInfo.avatar,
          apartment: body.creatorInfo.apartment,
          joinedAt: now,
          isCreator: true
        }
      ],
      scheduledTime: body.scheduledTime || new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1 hour from now
      duration: body.duration || 120,
      status: 'active',
      tags: body.tags || [],
      requirements: body.requirements || [],
      chatId: `chat-activity-${Date.now()}`,
      lastActivity: now,
      autoDeleteAt,
      createdAt: now,
      updatedAt: now
    };
    
    activities.push(newActivity);
    writeActivities(activities);
    
    return NextResponse.json(newActivity, { status: 201 });
  } catch (error) {
    console.error('Error creating activity:', error);
    return NextResponse.json({ error: 'Failed to create activity' }, { status: 500 });
  }
}

// PUT - Update activity (join/leave/update)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { activityId, action, userId, userInfo } = body;
    
    if (!activityId || !action || !userId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    const activities = readActivities();
    const activityIndex = activities.findIndex((activity: any) => activity.id === activityId);
    
    if (activityIndex === -1) {
      return NextResponse.json({ error: 'Activity not found' }, { status: 404 });
    }
    
    const activity = activities[activityIndex];
    const now = new Date().toISOString();
    
    switch (action) {
      case 'join':
        // Check if user already joined
        if (activity.members.some((member: any) => member.userId === userId)) {
          return NextResponse.json({ error: 'User already joined' }, { status: 400 });
        }
        
        // Check if activity is full
        if (activity.currentMembers >= activity.maxMembers) {
          return NextResponse.json({ error: 'Activity is full' }, { status: 400 });
        }
        
        // Add user to activity
        activity.members.push({
          userId,
          name: userInfo.name,
          avatar: userInfo.avatar,
          apartment: userInfo.apartment,
          joinedAt: now,
          isCreator: false
        });
        activity.currentMembers = activity.members.length;
        activity.lastActivity = now;
        activity.updatedAt = now;
        break;
        
      case 'leave':
        // Remove user from activity
        activity.members = activity.members.filter((member: any) => member.userId !== userId);
        activity.currentMembers = activity.members.length;
        activity.lastActivity = now;
        activity.updatedAt = now;
        
        // If creator leaves or no members left, mark as inactive
        if (activity.currentMembers === 0 || 
            (activity.members.length > 0 && !activity.members.some((m: any) => m.isCreator))) {
          activity.status = 'inactive';
        }
        break;
        
      case 'update':
        // Update activity details (only creator can do this)
        const isCreator = activity.members.some((member: any) => 
          member.userId === userId && member.isCreator
        );
        
        if (!isCreator) {
          return NextResponse.json({ error: 'Only creator can update activity' }, { status: 403 });
        }
        
        // Update allowed fields
        if (body.title) activity.title = body.title;
        if (body.description) activity.description = body.description;
        if (body.scheduledTime) activity.scheduledTime = body.scheduledTime;
        if (body.duration) activity.duration = body.duration;
        if (body.maxMembers) activity.maxMembers = body.maxMembers;
        if (body.requirements) activity.requirements = body.requirements;
        
        activity.lastActivity = now;
        activity.updatedAt = now;
        break;
        
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
    
    activities[activityIndex] = activity;
    writeActivities(activities);
    
    return NextResponse.json(activity);
  } catch (error) {
    console.error('Error updating activity:', error);
    return NextResponse.json({ error: 'Failed to update activity' }, { status: 500 });
  }
}

// DELETE - Delete activity
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const activityId = searchParams.get('activityId');
    const userId = searchParams.get('userId');
    
    if (!activityId || !userId) {
      return NextResponse.json({ error: 'Activity ID and User ID are required' }, { status: 400 });
    }
    
    const activities = readActivities();
    const activityIndex = activities.findIndex((activity: any) => activity.id === activityId);
    
    if (activityIndex === -1) {
      return NextResponse.json({ error: 'Activity not found' }, { status: 404 });
    }
    
    const activity = activities[activityIndex];
    
    // Check if user is the creator
    const isCreator = activity.members.some((member: any) => 
      member.userId === userId && member.isCreator
    );
    
    if (!isCreator) {
      return NextResponse.json({ error: 'Only creator can delete activity' }, { status: 403 });
    }
    
    // Remove activity
    activities.splice(activityIndex, 1);
    writeActivities(activities);
    
    // Also clean up related chat messages
    const messages = readChatMessages();
    const filteredMessages = messages.filter((message: any) => 
      message.activityId !== activityId
    );
    writeChatMessages(filteredMessages);
    
    return NextResponse.json({ message: 'Activity deleted successfully' });
  } catch (error) {
    console.error('Error deleting activity:', error);
    return NextResponse.json({ error: 'Failed to delete activity' }, { status: 500 });
  }
}
