import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const CHAT_MESSAGES_FILE = path.join(process.cwd(), 'data/apps/booksy/fullhouse/ru-nhau/chat-messages.json');
const ACTIVITIES_FILE = path.join(process.cwd(), 'data/apps/booksy/fullhouse/ru-nhau/activities.json');

// Ensure data directory exists
function ensureDataDirectory() {
  const dataDir = path.dirname(CHAT_MESSAGES_FILE);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
}

// Read chat messages from file
function readChatMessages() {
  ensureDataDirectory();
  try {
    if (fs.existsSync(CHAT_MESSAGES_FILE)) {
      const data = fs.readFileSync(CHAT_MESSAGES_FILE, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error('Error reading chat messages:', error);
    return [];
  }
}

// Write chat messages to file
function writeChatMessages(messages: any[]) {
  ensureDataDirectory();
  try {
    fs.writeFileSync(CHAT_MESSAGES_FILE, JSON.stringify(messages, null, 2));
  } catch (error) {
    console.error('Error writing chat messages:', error);
    throw error;
  }
}

// Read activities from file
function readActivities() {
  try {
    if (fs.existsSync(ACTIVITIES_FILE)) {
      const data = fs.readFileSync(ACTIVITIES_FILE, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error('Error reading activities:', error);
    return [];
  }
}

// Write activities to file
function writeActivities(activities: any[]) {
  try {
    fs.writeFileSync(ACTIVITIES_FILE, JSON.stringify(activities, null, 2));
  } catch (error) {
    console.error('Error writing activities:', error);
    throw error;
  }
}

// Clean up expired messages
function cleanupExpiredMessages() {
  const now = new Date();
  const messages = readChatMessages();
  
  const activeMessages = messages.filter((message: any) => {
    const autoDeleteTime = new Date(message.autoDeleteAt);
    return now <= autoDeleteTime && !message.isDeleted;
  });
  
  if (activeMessages.length !== messages.length) {
    writeChatMessages(activeMessages);
  }
  
  return activeMessages;
}

// Update activity last activity time
function updateActivityLastActivity(activityId: string) {
  const activities = readActivities();
  const activityIndex = activities.findIndex((activity: any) => activity.id === activityId);
  
  if (activityIndex !== -1) {
    activities[activityIndex].lastActivity = new Date().toISOString();
    writeActivities(activities);
  }
}

// GET - Fetch chat messages
export async function GET(request: NextRequest) {
  try {
    // Clean up expired messages first
    const activeMessages = cleanupExpiredMessages();
    
    const { searchParams } = new URL(request.url);
    const chatId = searchParams.get('chatId');
    const activityId = searchParams.get('activityId');
    const userId = searchParams.get('userId');
    const limit = parseInt(searchParams.get('limit') || '50');
    
    let filteredMessages = activeMessages;
    
    // Filter by chat ID
    if (chatId) {
      filteredMessages = filteredMessages.filter((message: any) => 
        message.chatId === chatId
      );
    }
    
    // Filter by activity ID
    if (activityId) {
      filteredMessages = filteredMessages.filter((message: any) => 
        message.activityId === activityId
      );
    }
    
    // Sort by timestamp (newest first) and limit
    filteredMessages.sort((a: any, b: any) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
    
    if (limit > 0) {
      filteredMessages = filteredMessages.slice(0, limit);
    }
    
    // Reverse to show oldest first in UI
    filteredMessages.reverse();
    
    return NextResponse.json(filteredMessages);
  } catch (error) {
    console.error('Error fetching chat messages:', error);
    return NextResponse.json({ error: 'Failed to fetch chat messages' }, { status: 500 });
  }
}

// POST - Send new message
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.chatId || !body.senderId || !body.message) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    // Validate user is member of the activity (if activityId provided)
    if (body.activityId) {
      const activities = readActivities();
      const activity = activities.find((activity: any) => activity.id === body.activityId);
      
      if (!activity) {
        return NextResponse.json({ error: 'Activity not found' }, { status: 404 });
      }
      
      const isMember = activity.members.some((member: any) => member.userId === body.senderId);
      if (!isMember) {
        return NextResponse.json({ error: 'User is not a member of this activity' }, { status: 403 });
      }
    }
    
    const messages = readChatMessages();
    const now = new Date().toISOString();
    const autoDeleteAt = new Date(Date.now() + 30 * 60 * 1000).toISOString(); // 30 minutes from now
    
    const newMessage = {
      id: `msg-${Date.now()}`,
      chatId: body.chatId,
      activityId: body.activityId || null,
      senderId: body.senderId,
      senderInfo: body.senderInfo,
      message: body.message,
      messageType: body.messageType || 'text',
      timestamp: now,
      autoDeleteAt,
      isDeleted: false,
      reactions: [],
      replyTo: body.replyTo || null
    };
    
    messages.push(newMessage);
    writeChatMessages(messages);
    
    // Update activity last activity time
    if (body.activityId) {
      updateActivityLastActivity(body.activityId);
    }
    
    return NextResponse.json(newMessage, { status: 201 });
  } catch (error) {
    console.error('Error sending message:', error);
    return NextResponse.json({ error: 'Failed to send message' }, { status: 500 });
  }
}

// PUT - Update message (edit/react)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { messageId, action, userId } = body;
    
    if (!messageId || !action || !userId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    const messages = readChatMessages();
    const messageIndex = messages.findIndex((message: any) => message.id === messageId);
    
    if (messageIndex === -1) {
      return NextResponse.json({ error: 'Message not found' }, { status: 404 });
    }
    
    const message = messages[messageIndex];
    
    switch (action) {
      case 'edit':
        // Only sender can edit their message
        if (message.senderId !== userId) {
          return NextResponse.json({ error: 'Only sender can edit message' }, { status: 403 });
        }
        
        if (!body.newMessage) {
          return NextResponse.json({ error: 'New message content required' }, { status: 400 });
        }
        
        message.message = body.newMessage;
        message.isEdited = true;
        message.editedAt = new Date().toISOString();
        break;
        
      case 'react':
        if (!body.reaction) {
          return NextResponse.json({ error: 'Reaction required' }, { status: 400 });
        }
        
        // Initialize reactions array if not exists
        if (!message.reactions) {
          message.reactions = [];
        }
        
        // Check if user already reacted with this emoji
        const existingReactionIndex = message.reactions.findIndex((reaction: any) => 
          reaction.userId === userId && reaction.emoji === body.reaction
        );
        
        if (existingReactionIndex !== -1) {
          // Remove existing reaction
          message.reactions.splice(existingReactionIndex, 1);
        } else {
          // Add new reaction
          message.reactions.push({
            userId,
            emoji: body.reaction,
            timestamp: new Date().toISOString()
          });
        }
        break;
        
      case 'delete':
        // Only sender can delete their message
        if (message.senderId !== userId) {
          return NextResponse.json({ error: 'Only sender can delete message' }, { status: 403 });
        }
        
        message.isDeleted = true;
        message.deletedAt = new Date().toISOString();
        message.message = '[Tin nhắn đã bị xóa]';
        break;
        
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
    
    messages[messageIndex] = message;
    writeChatMessages(messages);
    
    return NextResponse.json(message);
  } catch (error) {
    console.error('Error updating message:', error);
    return NextResponse.json({ error: 'Failed to update message' }, { status: 500 });
  }
}

// DELETE - Delete message
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const messageId = searchParams.get('messageId');
    const userId = searchParams.get('userId');
    
    if (!messageId || !userId) {
      return NextResponse.json({ error: 'Message ID and User ID are required' }, { status: 400 });
    }
    
    const messages = readChatMessages();
    const messageIndex = messages.findIndex((message: any) => message.id === messageId);
    
    if (messageIndex === -1) {
      return NextResponse.json({ error: 'Message not found' }, { status: 404 });
    }
    
    const message = messages[messageIndex];
    
    // Only sender can delete their message
    if (message.senderId !== userId) {
      return NextResponse.json({ error: 'Only sender can delete message' }, { status: 403 });
    }
    
    // Mark as deleted instead of removing completely
    message.isDeleted = true;
    message.deletedAt = new Date().toISOString();
    message.message = '[Tin nhắn đã bị xóa]';
    
    messages[messageIndex] = message;
    writeChatMessages(messages);
    
    return NextResponse.json({ message: 'Message deleted successfully' });
  } catch (error) {
    console.error('Error deleting message:', error);
    return NextResponse.json({ error: 'Failed to delete message' }, { status: 500 });
  }
}
