import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const GLOBAL_CHAT_FILE = path.join(process.cwd(), 'data/apps/booksy/fullhouse/ru-nhau/global-chat.json');
const USER_LOCATIONS_FILE = path.join(process.cwd(), 'data/apps/booksy/fullhouse/ru-nhau/user-locations.json');

// Building coordinates for proximity checking
const BUILDING_COORDINATES = {
  'Times City': { latitude: 20.9967, longitude: 105.8686 },
  'Royal City': { latitude: 20.9845, longitude: 105.8234 },
  'Landmark 72': { latitude: 21.0285, longitude: 105.7918 },
  'Lotte Center': { latitude: 21.0227, longitude: 105.8194 },
  'Vincom Mega Mall': { latitude: 20.9975, longitude: 105.8542 }
};

// Global chat settings
const GLOBAL_CHAT_SETTINGS = {
  maxRadius: 1000, // 1km from Times City
  maxMembers: 50,
  messageRetentionMinutes: 30,
  allowedBuildings: ['Times City', 'Royal City', 'Landmark 72', 'Lotte Center', 'Vincom Mega Mall'],
  moderationRules: [
    'Không spam',
    'Không quảng cáo',
    'Không ngôn từ không phù hợp',
    'Chỉ về các hoạt động trong khu vực'
  ]
};

// Calculate distance between two coordinates (in meters)
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = lat1 * Math.PI/180;
  const φ2 = lat2 * Math.PI/180;
  const Δφ = (lat2-lat1) * Math.PI/180;
  const Δλ = (lon1-lon2) * Math.PI/180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
          Math.cos(φ1) * Math.cos(φ2) *
          Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  return R * c;
}

// Check if user has access to global chat
function checkGlobalChatAccess(userBuilding: string): boolean {
  if (!GLOBAL_CHAT_SETTINGS.allowedBuildings.includes(userBuilding)) {
    return false;
  }

  const timesCityCoords = BUILDING_COORDINATES['Times City'];
  const userCoords = BUILDING_COORDINATES[userBuilding as keyof typeof BUILDING_COORDINATES];
  
  if (!userCoords || !timesCityCoords) {
    return false;
  }

  const distance = calculateDistance(
    userCoords.latitude, userCoords.longitude,
    timesCityCoords.latitude, timesCityCoords.longitude
  );

  return distance <= GLOBAL_CHAT_SETTINGS.maxRadius;
}

// Read global chat messages from file
function readGlobalChatMessages() {
  try {
    if (fs.existsSync(GLOBAL_CHAT_FILE)) {
      const data = fs.readFileSync(GLOBAL_CHAT_FILE, 'utf8');
      return JSON.parse(data);
    }
    return { messages: [], activeUsers: [] };
  } catch (error) {
    console.error('Error reading global chat messages:', error);
    return { messages: [], activeUsers: [] };
  }
}

// Write global chat messages to file
function writeGlobalChatMessages(chatData: any) {
  try {
    const dir = path.dirname(GLOBAL_CHAT_FILE);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(GLOBAL_CHAT_FILE, JSON.stringify(chatData, null, 2));
  } catch (error) {
    console.error('Error writing global chat messages:', error);
    throw error;
  }
}

// Read user locations from file
function readUserLocations() {
  try {
    if (fs.existsSync(USER_LOCATIONS_FILE)) {
      const data = fs.readFileSync(USER_LOCATIONS_FILE, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error('Error reading user locations:', error);
    return [];
  }
}

// Clean up expired messages and inactive users
function cleanupGlobalChat() {
  const chatData = readGlobalChatMessages();
  const now = new Date();
  const retentionTime = GLOBAL_CHAT_SETTINGS.messageRetentionMinutes * 60 * 1000;

  // Clean up expired messages
  chatData.messages = chatData.messages.filter((message: any) => {
    const messageTime = new Date(message.timestamp);
    return (now.getTime() - messageTime.getTime()) < retentionTime && !message.isDeleted;
  });

  // Clean up inactive users (not seen in last 5 minutes)
  const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
  chatData.activeUsers = chatData.activeUsers.filter((user: any) => {
    const lastSeen = new Date(user.lastSeen);
    return lastSeen > fiveMinutesAgo;
  });

  return chatData;
}

// GET - Fetch global chat messages and active users
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const userBuilding = searchParams.get('building');
    const limit = parseInt(searchParams.get('limit') || '50');

    if (!userId || !userBuilding) {
      return NextResponse.json({ error: 'User ID and building are required' }, { status: 400 });
    }

    // Check access permission
    if (!checkGlobalChatAccess(userBuilding)) {
      return NextResponse.json({ 
        error: 'Access denied. Global chat is only available within 1km of Times City area.',
        hasAccess: false,
        settings: GLOBAL_CHAT_SETTINGS
      }, { status: 403 });
    }

    // Clean up expired data
    const chatData = cleanupGlobalChat();

    // Update user's active status
    const existingUserIndex = chatData.activeUsers.findIndex((user: any) => user.userId === userId);
    const userInfo = {
      userId,
      building: userBuilding,
      lastSeen: new Date().toISOString(),
      joinedAt: existingUserIndex === -1 ? new Date().toISOString() : chatData.activeUsers[existingUserIndex].joinedAt
    };

    if (existingUserIndex !== -1) {
      chatData.activeUsers[existingUserIndex] = userInfo;
    } else {
      chatData.activeUsers.push(userInfo);
    }

    // Limit messages
    const messages = chatData.messages
      .sort((a: any, b: any) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit)
      .reverse(); // Show oldest first in UI

    // Save updated data
    writeGlobalChatMessages(chatData);

    return NextResponse.json({
      hasAccess: true,
      messages,
      activeUsers: chatData.activeUsers,
      settings: GLOBAL_CHAT_SETTINGS,
      stats: {
        totalMessages: chatData.messages.length,
        activeUsersCount: chatData.activeUsers.length,
        userBuilding,
        maxCapacity: GLOBAL_CHAT_SETTINGS.maxMembers
      }
    });
  } catch (error) {
    console.error('Error fetching global chat:', error);
    return NextResponse.json({ error: 'Failed to fetch global chat' }, { status: 500 });
  }
}

// POST - Send message to global chat
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, userInfo, message, messageType = 'text' } = body;

    if (!userId || !userInfo || !message) {
      return NextResponse.json({ error: 'User ID, user info, and message are required' }, { status: 400 });
    }

    // Check access permission
    if (!checkGlobalChatAccess(userInfo.building)) {
      return NextResponse.json({ 
        error: 'Access denied. Global chat is only available within 1km of Times City area.',
        hasAccess: false
      }, { status: 403 });
    }

    // Basic message validation
    if (message.length > 500) {
      return NextResponse.json({ error: 'Message too long (max 500 characters)' }, { status: 400 });
    }

    // Simple spam detection (no more than 3 messages per minute)
    const chatData = readGlobalChatMessages();
    const oneMinuteAgo = new Date(Date.now() - 60 * 1000);
    const recentMessages = chatData.messages.filter((msg: any) => 
      msg.senderId === userId && new Date(msg.timestamp) > oneMinuteAgo
    );

    if (recentMessages.length >= 3) {
      return NextResponse.json({ error: 'Too many messages. Please wait before sending another message.' }, { status: 429 });
    }

    // Create new message
    const now = new Date().toISOString();
    const newMessage = {
      id: `global-msg-${Date.now()}`,
      senderId: userId,
      senderInfo: {
        name: userInfo.name,
        avatar: userInfo.avatar,
        apartment: userInfo.apartment,
        building: userInfo.building
      },
      message,
      messageType,
      timestamp: now,
      isDeleted: false,
      reactions: [],
      isGlobal: true
    };

    chatData.messages.push(newMessage);

    // Update user's active status
    const existingUserIndex = chatData.activeUsers.findIndex((user: any) => user.userId === userId);
    const activeUserInfo = {
      userId,
      building: userInfo.building,
      lastSeen: now,
      joinedAt: existingUserIndex === -1 ? now : chatData.activeUsers[existingUserIndex].joinedAt
    };

    if (existingUserIndex !== -1) {
      chatData.activeUsers[existingUserIndex] = activeUserInfo;
    } else {
      chatData.activeUsers.push(activeUserInfo);
    }

    // Clean up and save
    const cleanedData = cleanupGlobalChat();
    cleanedData.messages.push(newMessage);
    writeGlobalChatMessages(cleanedData);

    return NextResponse.json(newMessage, { status: 201 });
  } catch (error) {
    console.error('Error sending global chat message:', error);
    return NextResponse.json({ error: 'Failed to send message' }, { status: 500 });
  }
}

// PUT - Update message (react/edit)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { messageId, action, userId, userBuilding } = body;

    if (!messageId || !action || !userId || !userBuilding) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Check access permission
    if (!checkGlobalChatAccess(userBuilding)) {
      return NextResponse.json({ 
        error: 'Access denied',
        hasAccess: false
      }, { status: 403 });
    }

    const chatData = readGlobalChatMessages();
    const messageIndex = chatData.messages.findIndex((msg: any) => msg.id === messageId);

    if (messageIndex === -1) {
      return NextResponse.json({ error: 'Message not found' }, { status: 404 });
    }

    const message = chatData.messages[messageIndex];

    switch (action) {
      case 'react':
        if (!body.reaction) {
          return NextResponse.json({ error: 'Reaction required' }, { status: 400 });
        }

        // Initialize reactions array if not exists
        if (!message.reactions) {
          message.reactions = [];
        }

        // Check if user already reacted with this emoji
        const existingReactionIndex = message.reactions.findIndex((reaction: any) => 
          reaction.userId === userId && reaction.emoji === body.reaction
        );

        if (existingReactionIndex !== -1) {
          // Remove existing reaction
          message.reactions.splice(existingReactionIndex, 1);
        } else {
          // Add new reaction
          message.reactions.push({
            userId,
            emoji: body.reaction,
            timestamp: new Date().toISOString()
          });
        }
        break;

      case 'delete':
        // Only sender can delete their message
        if (message.senderId !== userId) {
          return NextResponse.json({ error: 'Only sender can delete message' }, { status: 403 });
        }

        message.isDeleted = true;
        message.deletedAt = new Date().toISOString();
        message.message = '[Tin nhắn đã bị xóa]';
        break;

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

    chatData.messages[messageIndex] = message;
    writeGlobalChatMessages(chatData);

    return NextResponse.json(message);
  } catch (error) {
    console.error('Error updating global chat message:', error);
    return NextResponse.json({ error: 'Failed to update message' }, { status: 500 });
  }
}

// DELETE - Leave global chat
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    const chatData = readGlobalChatMessages();
    
    // Remove user from active users
    chatData.activeUsers = chatData.activeUsers.filter((user: any) => user.userId !== userId);
    
    writeGlobalChatMessages(chatData);

    return NextResponse.json({ 
      success: true,
      message: 'Left global chat successfully' 
    });
  } catch (error) {
    console.error('Error leaving global chat:', error);
    return NextResponse.json({ error: 'Failed to leave global chat' }, { status: 500 });
  }
}
