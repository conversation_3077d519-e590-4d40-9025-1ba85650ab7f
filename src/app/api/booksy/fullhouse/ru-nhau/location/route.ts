import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const ACTIVITIES_FILE = path.join(process.cwd(), 'data/apps/booksy/fullhouse/ru-nhau/activities.json');
const USER_LOCATIONS_FILE = path.join(process.cwd(), 'data/apps/booksy/fullhouse/ru-nhau/user-locations.json');

// Building coordinates for Times City area
const BUILDING_COORDINATES = {
  'Times City': { latitude: 20.9967, longitude: 105.8686 },
  'Royal City': { latitude: 20.9845, longitude: 105.8234 },
  'Landmark 72': { latitude: 21.0285, longitude: 105.7918 },
  'Lotte Center': { latitude: 21.0227, longitude: 105.8194 },
  'Vincom Mega Mall': { latitude: 20.9975, longitude: 105.8542 }
};

// Apartment ranges for each building
const APARTMENT_RANGES = {
  'Times City': {
    towers: ['T1', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'T8', 'T9', 'T10'],
    floors: { min: 1, max: 40 },
    unitsPerFloor: 20
  },
  'Royal City': {
    towers: ['R1', 'R2', 'R3', 'R4', 'R5', 'R6'],
    floors: { min: 1, max: 50 },
    unitsPerFloor: 16
  },
  'Landmark 72': {
    towers: ['L1', 'L2'],
    floors: { min: 1, max: 72 },
    unitsPerFloor: 12
  },
  'Lotte Center': {
    towers: ['LC1'],
    floors: { min: 1, max: 65 },
    unitsPerFloor: 8
  },
  'Vincom Mega Mall': {
    towers: ['V1', 'V2', 'V3'],
    floors: { min: 1, max: 35 },
    unitsPerFloor: 24
  }
};

// Calculate distance between two coordinates (in meters)
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = lat1 * Math.PI/180;
  const φ2 = lat2 * Math.PI/180;
  const Δφ = (lat2-lat1) * Math.PI/180;
  const Δλ = (lon1-lon2) * Math.PI/180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
          Math.cos(φ1) * Math.cos(φ2) *
          Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  return R * c;
}

// Calculate apartment-to-apartment distance
function calculateApartmentDistance(apt1: string, building1: string, apt2: string, building2: string): number {
  // If different buildings, use building coordinates
  if (building1 !== building2) {
    const coord1 = BUILDING_COORDINATES[building1 as keyof typeof BUILDING_COORDINATES];
    const coord2 = BUILDING_COORDINATES[building2 as keyof typeof BUILDING_COORDINATES];
    
    if (!coord1 || !coord2) return Infinity;
    
    return calculateDistance(coord1.latitude, coord1.longitude, coord2.latitude, coord2.longitude);
  }

  // Same building - calculate internal distance
  const tower1 = apt1.split('-')[0];
  const tower2 = apt2.split('-')[0];
  const unit1 = apt1.split('-')[1];
  const unit2 = apt2.split('-')[1];

  if (!unit1 || !unit2) return 0;

  const floor1 = Math.floor(parseInt(unit1) / 100);
  const floor2 = Math.floor(parseInt(unit2) / 100);
  const room1 = parseInt(unit1) % 100;
  const room2 = parseInt(unit2) % 100;

  // Different towers in same building
  if (tower1 !== tower2) {
    return 200; // Assume 200m between towers
  }

  // Same tower - calculate floor and room distance
  const floorDistance = Math.abs(floor1 - floor2) * 3; // 3m per floor
  const roomDistance = Math.abs(room1 - room2) * 5; // 5m per room

  return Math.sqrt(floorDistance * floorDistance + roomDistance * roomDistance);
}

// Read user locations from file
function readUserLocations() {
  try {
    if (fs.existsSync(USER_LOCATIONS_FILE)) {
      const data = fs.readFileSync(USER_LOCATIONS_FILE, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error('Error reading user locations:', error);
    return [];
  }
}

// Write user locations to file
function writeUserLocations(locations: any[]) {
  try {
    const dir = path.dirname(USER_LOCATIONS_FILE);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(USER_LOCATIONS_FILE, JSON.stringify(locations, null, 2));
  } catch (error) {
    console.error('Error writing user locations:', error);
    throw error;
  }
}

// Read activities from file
function readActivities() {
  try {
    if (fs.existsSync(ACTIVITIES_FILE)) {
      const data = fs.readFileSync(ACTIVITIES_FILE, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error('Error reading activities:', error);
    return [];
  }
}

// GET - Find nearby users and activities
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const userApartment = searchParams.get('apartment');
    const userBuilding = searchParams.get('building');
    const radius = parseInt(searchParams.get('radius') || '500'); // Default 500m
    const includeActivities = searchParams.get('includeActivities') === 'true';
    const includeUsers = searchParams.get('includeUsers') === 'true';

    if (!userId || !userApartment || !userBuilding) {
      return NextResponse.json({ error: 'User ID, apartment, and building are required' }, { status: 400 });
    }

    const userLocations = readUserLocations();
    const activities = readActivities();

    // Update user's current location
    const existingLocationIndex = userLocations.findIndex((loc: any) => loc.userId === userId);
    const userLocation = {
      userId,
      apartment: userApartment,
      building: userBuilding,
      lastSeen: new Date().toISOString(),
      isActive: true
    };

    if (existingLocationIndex !== -1) {
      userLocations[existingLocationIndex] = userLocation;
    } else {
      userLocations.push(userLocation);
    }

    // Clean up old locations (older than 1 hour)
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const activeLocations = userLocations.filter((loc: any) => 
      new Date(loc.lastSeen) > oneHourAgo || loc.userId === userId
    );

    writeUserLocations(activeLocations);

    const result: any = {
      userLocation,
      nearbyUsers: [],
      nearbyActivities: [],
      globalChatAccess: false
    };

    // Find nearby users
    if (includeUsers) {
      result.nearbyUsers = activeLocations
        .filter((loc: any) => loc.userId !== userId && loc.isActive)
        .map((loc: any) => {
          const distance = calculateApartmentDistance(
            userApartment, userBuilding,
            loc.apartment, loc.building
          );
          return { ...loc, distance: Math.round(distance) };
        })
        .filter((loc: any) => loc.distance <= radius)
        .sort((a: any, b: any) => a.distance - b.distance);
    }

    // Find nearby activities
    if (includeActivities) {
      result.nearbyActivities = activities
        .filter((activity: any) => activity.status === 'active')
        .map((activity: any) => {
          let distance = Infinity;
          
          if (activity.location?.coordinates) {
            const userCoords = BUILDING_COORDINATES[userBuilding as keyof typeof BUILDING_COORDINATES];
            if (userCoords) {
              distance = calculateDistance(
                userCoords.latitude, userCoords.longitude,
                activity.location.coordinates.latitude,
                activity.location.coordinates.longitude
              );
            }
          } else if (activity.location?.building) {
            // Calculate building-to-building distance
            const userCoords = BUILDING_COORDINATES[userBuilding as keyof typeof BUILDING_COORDINATES];
            const activityCoords = BUILDING_COORDINATES[activity.location.building as keyof typeof BUILDING_COORDINATES];
            
            if (userCoords && activityCoords) {
              distance = calculateDistance(
                userCoords.latitude, userCoords.longitude,
                activityCoords.latitude, activityCoords.longitude
              );
            }
          }
          
          return { ...activity, distance: Math.round(distance) };
        })
        .filter((activity: any) => activity.distance <= radius)
        .sort((a: any, b: any) => a.distance - b.distance);
    }

    // Check global chat access (within 1km of Times City)
    const timesCityCoords = BUILDING_COORDINATES['Times City'];
    const userCoords = BUILDING_COORDINATES[userBuilding as keyof typeof BUILDING_COORDINATES];
    
    if (userCoords && timesCityCoords) {
      const distanceToTimesCity = calculateDistance(
        userCoords.latitude, userCoords.longitude,
        timesCityCoords.latitude, timesCityCoords.longitude
      );
      result.globalChatAccess = distanceToTimesCity <= 1000; // 1km radius
    }

    // Add proximity statistics
    result.stats = {
      totalNearbyUsers: result.nearbyUsers.length,
      totalNearbyActivities: result.nearbyActivities.length,
      averageDistance: result.nearbyUsers.length > 0 
        ? Math.round(result.nearbyUsers.reduce((sum: number, user: any) => sum + user.distance, 0) / result.nearbyUsers.length)
        : 0,
      buildingDistribution: result.nearbyUsers.reduce((acc: any, user: any) => {
        acc[user.building] = (acc[user.building] || 0) + 1;
        return acc;
      }, {})
    };

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error finding nearby users and activities:', error);
    return NextResponse.json({ error: 'Failed to find nearby users and activities' }, { status: 500 });
  }
}

// POST - Update user location
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, apartment, building, isActive = true } = body;

    if (!userId || !apartment || !building) {
      return NextResponse.json({ error: 'User ID, apartment, and building are required' }, { status: 400 });
    }

    // Validate building exists
    if (!BUILDING_COORDINATES[building as keyof typeof BUILDING_COORDINATES]) {
      return NextResponse.json({ error: 'Invalid building' }, { status: 400 });
    }

    // Validate apartment format
    const apartmentRegex = /^[A-Z]+\d+-\d{4}$/;
    if (!apartmentRegex.test(apartment)) {
      return NextResponse.json({ error: 'Invalid apartment format (e.g., T1-1205)' }, { status: 400 });
    }

    const userLocations = readUserLocations();
    const existingLocationIndex = userLocations.findIndex((loc: any) => loc.userId === userId);

    const userLocation = {
      userId,
      apartment,
      building,
      lastSeen: new Date().toISOString(),
      isActive
    };

    if (existingLocationIndex !== -1) {
      userLocations[existingLocationIndex] = userLocation;
    } else {
      userLocations.push(userLocation);
    }

    writeUserLocations(userLocations);

    return NextResponse.json({
      success: true,
      userLocation,
      message: 'Location updated successfully'
    });
  } catch (error) {
    console.error('Error updating user location:', error);
    return NextResponse.json({ error: 'Failed to update user location' }, { status: 500 });
  }
}

// PUT - Update user activity status
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, isActive } = body;

    if (!userId || typeof isActive !== 'boolean') {
      return NextResponse.json({ error: 'User ID and isActive status are required' }, { status: 400 });
    }

    const userLocations = readUserLocations();
    const existingLocationIndex = userLocations.findIndex((loc: any) => loc.userId === userId);

    if (existingLocationIndex === -1) {
      return NextResponse.json({ error: 'User location not found' }, { status: 404 });
    }

    userLocations[existingLocationIndex].isActive = isActive;
    userLocations[existingLocationIndex].lastSeen = new Date().toISOString();

    writeUserLocations(userLocations);

    return NextResponse.json({
      success: true,
      userLocation: userLocations[existingLocationIndex],
      message: 'Activity status updated successfully'
    });
  } catch (error) {
    console.error('Error updating activity status:', error);
    return NextResponse.json({ error: 'Failed to update activity status' }, { status: 500 });
  }
}

// DELETE - Remove user from location tracking
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }

    const userLocations = readUserLocations();
    const filteredLocations = userLocations.filter((loc: any) => loc.userId !== userId);

    writeUserLocations(filteredLocations);

    return NextResponse.json({
      success: true,
      message: 'User removed from location tracking'
    });
  } catch (error) {
    console.error('Error removing user location:', error);
    return NextResponse.json({ error: 'Failed to remove user location' }, { status: 500 });
  }
}
