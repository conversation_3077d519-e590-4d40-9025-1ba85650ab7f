import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const ACTIVITIES_FILE = path.join(process.cwd(), 'data/apps/booksy/fullhouse/ru-nhau/activities.json');
const CHAT_MESSAGES_FILE = path.join(process.cwd(), 'data/apps/booksy/fullhouse/ru-nhau/chat-messages.json');

// Read activities from file
function readActivities() {
  try {
    if (fs.existsSync(ACTIVITIES_FILE)) {
      const data = fs.readFileSync(ACTIVITIES_FILE, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error('Error reading activities:', error);
    return [];
  }
}

// Write activities to file
function writeActivities(activities: any[]) {
  try {
    fs.writeFileSync(ACTIVITIES_FILE, JSON.stringify(activities, null, 2));
  } catch (error) {
    console.error('Error writing activities:', error);
    throw error;
  }
}

// Read chat messages from file
function readChatMessages() {
  try {
    if (fs.existsSync(CHAT_MESSAGES_FILE)) {
      const data = fs.readFileSync(CHAT_MESSAGES_FILE, 'utf8');
      return JSON.parse(data);
    }
    return [];
  } catch (error) {
    console.error('Error reading chat messages:', error);
    return [];
  }
}

// Write chat messages to file
function writeChatMessages(messages: any[]) {
  try {
    fs.writeFileSync(CHAT_MESSAGES_FILE, JSON.stringify(messages, null, 2));
  } catch (error) {
    console.error('Error writing chat messages:', error);
    throw error;
  }
}

// Perform comprehensive cleanup
function performCleanup() {
  const now = new Date();
  const cleanupResults = {
    expiredActivities: 0,
    emptyGroups: 0,
    inactiveGroups: 0,
    expiredMessages: 0,
    totalActivitiesBefore: 0,
    totalActivitiesAfter: 0,
    totalMessagesBefore: 0,
    totalMessagesAfter: 0
  };

  // Clean up activities
  const activities = readActivities();
  cleanupResults.totalActivitiesBefore = activities.length;

  const activeActivities = activities.filter((activity: any) => {
    const autoDeleteTime = new Date(activity.autoDeleteAt);
    const lastActivityTime = new Date(activity.lastActivity);
    
    // Check if activity has expired
    if (now > autoDeleteTime) {
      cleanupResults.expiredActivities++;
      return false;
    }
    
    // Check if group is empty (only creator left or no members)
    if (activity.currentMembers <= 1) {
      const timeSinceCreation = now.getTime() - new Date(activity.createdAt).getTime();
      const thirtyMinutes = 30 * 60 * 1000;
      
      if (timeSinceCreation > thirtyMinutes) {
        cleanupResults.emptyGroups++;
        return false;
      }
    }
    
    // Check if group is inactive (no activity for 30 minutes)
    const timeSinceLastActivity = now.getTime() - lastActivityTime.getTime();
    const thirtyMinutes = 30 * 60 * 1000;
    
    if (timeSinceLastActivity > thirtyMinutes && activity.currentMembers > 1) {
      cleanupResults.inactiveGroups++;
      return false;
    }
    
    return true;
  });

  cleanupResults.totalActivitiesAfter = activeActivities.length;
  
  if (activeActivities.length !== activities.length) {
    writeActivities(activeActivities);
  }

  // Clean up chat messages
  const messages = readChatMessages();
  cleanupResults.totalMessagesBefore = messages.length;

  const activeMessages = messages.filter((message: any) => {
    const autoDeleteTime = new Date(message.autoDeleteAt);
    
    if (now > autoDeleteTime || message.isDeleted) {
      cleanupResults.expiredMessages++;
      return false;
    }
    
    // Also remove messages from deleted activities
    const activityExists = activeActivities.some((activity: any) => 
      activity.id === message.activityId
    );
    
    if (message.activityId && !activityExists) {
      cleanupResults.expiredMessages++;
      return false;
    }
    
    return true;
  });

  cleanupResults.totalMessagesAfter = activeMessages.length;
  
  if (activeMessages.length !== messages.length) {
    writeChatMessages(activeMessages);
  }

  return cleanupResults;
}

// GET - Get cleanup status and statistics
export async function GET(request: NextRequest) {
  try {
    const activities = readActivities();
    const messages = readChatMessages();
    const now = new Date();

    const stats = {
      totalActivities: activities.length,
      activeActivities: activities.filter((a: any) => a.status === 'active').length,
      totalMessages: messages.length,
      activeMessages: messages.filter((m: any) => !m.isDeleted).length,
      activitiesNearExpiry: activities.filter((a: any) => {
        const autoDeleteTime = new Date(a.autoDeleteAt);
        const timeUntilExpiry = autoDeleteTime.getTime() - now.getTime();
        return timeUntilExpiry > 0 && timeUntilExpiry < 5 * 60 * 1000; // Less than 5 minutes
      }).length,
      messagesNearExpiry: messages.filter((m: any) => {
        const autoDeleteTime = new Date(m.autoDeleteAt);
        const timeUntilExpiry = autoDeleteTime.getTime() - now.getTime();
        return timeUntilExpiry > 0 && timeUntilExpiry < 5 * 60 * 1000; // Less than 5 minutes
      }).length,
      emptyGroups: activities.filter((a: any) => a.currentMembers <= 1).length,
      inactiveGroups: activities.filter((a: any) => {
        const lastActivityTime = new Date(a.lastActivity);
        const timeSinceLastActivity = now.getTime() - lastActivityTime.getTime();
        return timeSinceLastActivity > 30 * 60 * 1000; // More than 30 minutes
      }).length
    };

    return NextResponse.json({
      success: true,
      timestamp: now.toISOString(),
      stats
    });
  } catch (error) {
    console.error('Error getting cleanup status:', error);
    return NextResponse.json({ error: 'Failed to get cleanup status' }, { status: 500 });
  }
}

// POST - Trigger manual cleanup
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { force = false } = body;

    // Perform cleanup
    const cleanupResults = performCleanup();

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      message: 'Cleanup completed successfully',
      results: cleanupResults
    });
  } catch (error) {
    console.error('Error performing cleanup:', error);
    return NextResponse.json({ error: 'Failed to perform cleanup' }, { status: 500 });
  }
}

// PUT - Update cleanup settings
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    
    // In a real implementation, you would store these settings in a database
    // For now, we'll just return the settings
    const settings = {
      activityRetentionMinutes: body.activityRetentionMinutes || 30,
      chatRetentionMinutes: body.chatRetentionMinutes || 30,
      emptyGroupCleanupMinutes: body.emptyGroupCleanupMinutes || 30,
      inactiveGroupCleanupMinutes: body.inactiveGroupCleanupMinutes || 60,
      cleanupIntervalMinutes: body.cleanupIntervalMinutes || 5,
      autoCleanupEnabled: body.autoCleanupEnabled !== false
    };

    return NextResponse.json({
      success: true,
      message: 'Cleanup settings updated',
      settings
    });
  } catch (error) {
    console.error('Error updating cleanup settings:', error);
    return NextResponse.json({ error: 'Failed to update cleanup settings' }, { status: 500 });
  }
}

// DELETE - Force delete all expired data
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const confirmDelete = searchParams.get('confirm') === 'true';

    if (!confirmDelete) {
      return NextResponse.json({ 
        error: 'Confirmation required. Add ?confirm=true to force delete all expired data' 
      }, { status: 400 });
    }

    // Force cleanup all expired data
    const cleanupResults = performCleanup();

    // Also clean up any remaining inactive data
    const activities = readActivities();
    const messages = readChatMessages();

    // Remove all inactive activities
    const activeActivities = activities.filter((activity: any) => 
      activity.status === 'active' && activity.currentMembers > 1
    );

    // Remove all deleted messages
    const activeMessages = messages.filter((message: any) => 
      !message.isDeleted
    );

    writeActivities(activeActivities);
    writeChatMessages(activeMessages);

    const finalResults = {
      ...cleanupResults,
      forcedCleanup: true,
      finalActivitiesCount: activeActivities.length,
      finalMessagesCount: activeMessages.length
    };

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      message: 'Force cleanup completed',
      results: finalResults
    });
  } catch (error) {
    console.error('Error performing force cleanup:', error);
    return NextResponse.json({ error: 'Failed to perform force cleanup' }, { status: 500 });
  }
}
