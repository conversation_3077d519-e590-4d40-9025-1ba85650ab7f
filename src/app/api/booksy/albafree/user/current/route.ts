import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const USERS_FILE = path.join(process.cwd(), 'data/apps/booksy/albafree/users.json');

function readUsersFile() {
  try {
    const fileContents = fs.readFileSync(USERS_FILE, 'utf8');
    return JSON.parse(fileContents);
  } catch (error) {
    console.error('Error reading users file:', error);
    return [];
  }
}

// GET /api/booksy/albafree/user/current - Get current user (mock implementation)
export async function GET(request: NextRequest) {
  try {
    const users = readUsersFile();
    
    // For demo purposes, return the first user as current user
    // In a real app, this would be based on authentication
    const currentUser = users.find((user: any) => user.id === 'user-001') || users[0];
    
    if (!currentUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(currentUser);
  } catch (error) {
    console.error('Error fetching current user:', error);
    return NextResponse.json(
      { error: 'Failed to fetch current user' },
      { status: 500 }
    );
  }
}
