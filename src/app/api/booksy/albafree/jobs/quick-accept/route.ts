import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const JOBS_FILE = path.join(process.cwd(), 'data/apps/booksy/albafree/jobs.json');
const MATCHES_FILE = path.join(process.cwd(), 'data/apps/booksy/albafree/matches.json');
const NOTIFICATIONS_FILE = path.join(process.cwd(), 'data/apps/booksy/albafree/notifications.json');

function readJobsFile() {
  try {
    const fileContents = fs.readFileSync(JOBS_FILE, 'utf8');
    return JSON.parse(fileContents);
  } catch (error) {
    console.error('Error reading jobs file:', error);
    return [];
  }
}

function writeJobsFile(jobs: any[]) {
  try {
    fs.writeFileSync(JOBS_FILE, JSON.stringify(jobs, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing jobs file:', error);
    return false;
  }
}

function readMatchesFile() {
  try {
    const fileContents = fs.readFileSync(MATCHES_FILE, 'utf8');
    return JSON.parse(fileContents);
  } catch (error) {
    console.error('Error reading matches file:', error);
    return [];
  }
}

function writeMatchesFile(matches: any[]) {
  try {
    fs.writeFileSync(MATCHES_FILE, JSON.stringify(matches, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing matches file:', error);
    return false;
  }
}

function readNotificationsFile() {
  try {
    const fileContents = fs.readFileSync(NOTIFICATIONS_FILE, 'utf8');
    return JSON.parse(fileContents);
  } catch (error) {
    console.error('Error reading notifications file:', error);
    return [];
  }
}

function writeNotificationsFile(notifications: any[]) {
  try {
    fs.writeFileSync(NOTIFICATIONS_FILE, JSON.stringify(notifications, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing notifications file:', error);
    return false;
  }
}

// POST /api/booksy/albafree/jobs/quick-accept - Quick accept a job (for emergency jobs)
export async function POST(request: NextRequest) {
  try {
    const { jobId, workerId } = await request.json();
    
    if (!jobId || !workerId) {
      return NextResponse.json(
        { error: 'jobId and workerId are required' },
        { status: 400 }
      );
    }
    
    const jobs = readJobsFile();
    const matches = readMatchesFile();
    const notifications = readNotificationsFile();
    
    // Find the job
    const jobIndex = jobs.findIndex((job: any) => job.id === jobId);
    if (jobIndex === -1) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      );
    }
    
    const job = jobs[jobIndex];
    
    // Check if job is still open
    if (job.status !== 'open') {
      return NextResponse.json(
        { error: 'Job is no longer available' },
        { status: 400 }
      );
    }
    
    // Update job status and assign to worker
    jobs[jobIndex].status = 'assigned';
    jobs[jobIndex].assignedTo = workerId;
    if (!jobs[jobIndex].applicants.includes(workerId)) {
      jobs[jobIndex].applicants.push(workerId);
    }
    
    // Create match record
    const matchId = `match-${String(matches.length + 1).padStart(3, '0')}`;
    const newMatch = {
      id: matchId,
      jobId,
      workerId,
      employerId: job.postedBy,
      status: 'accepted',
      createdAt: new Date().toISOString(),
      respondedAt: new Date().toISOString(),
      acceptedAt: new Date().toISOString(),
      completedAt: null,
      type: 'quick_accept',
      bbeonjjeokCall: job.bbeonjjeokCall || false,
      workerResponse: 'quick_accepted',
      employerResponse: 'auto_approved',
      rating: null,
      review: null
    };
    
    matches.push(newMatch);
    
    // Create notification for employer
    const notificationId = `notif-${String(notifications.length + 1).padStart(3, '0')}`;
    const newNotification = {
      id: notificationId,
      userId: job.postedBy,
      type: 'job_accepted',
      title: '일자리 확정!',
      message: `'${job.title}' 공고가 확정되었습니다. 근로자가 곧 도착할 예정입니다.`,
      data: {
        jobId,
        workerId,
        matchId
      },
      isRead: false,
      createdAt: new Date().toISOString(),
      expiresAt: null,
      priority: 'high'
    };
    
    notifications.push(newNotification);
    
    // Save all files
    if (writeJobsFile(jobs) && writeMatchesFile(matches) && writeNotificationsFile(notifications)) {
      return NextResponse.json({
        success: true,
        match: newMatch,
        job: jobs[jobIndex],
        message: 'Job accepted successfully'
      });
    } else {
      return NextResponse.json(
        { error: 'Failed to accept job' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error accepting job:', error);
    return NextResponse.json(
      { error: 'Failed to accept job' },
      { status: 500 }
    );
  }
}
