import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const JOBS_FILE = path.join(process.cwd(), 'data/apps/booksy/albafree/jobs.json');

function readJobsFile() {
  try {
    const fileContents = fs.readFileSync(JOBS_FILE, 'utf8');
    return JSON.parse(fileContents);
  } catch (error) {
    console.error('Error reading jobs file:', error);
    return [];
  }
}

function writeJobsFile(jobs: any[]) {
  try {
    fs.writeFileSync(JOBS_FILE, JSON.stringify(jobs, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing jobs file:', error);
    return false;
  }
}

// GET /api/booksy/albafree/jobs - Get jobs with filters
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const category = searchParams.get('category');
    const urgency = searchParams.get('urgency');
    const emergency = searchParams.get('emergency') === 'true';
    const lat = searchParams.get('lat');
    const lng = searchParams.get('lng');
    const radius = parseFloat(searchParams.get('radius') || '10');
    const limit = parseInt(searchParams.get('limit') || '20');
    
    let jobs = readJobsFile();
    
    // Filter by status
    if (status) {
      jobs = jobs.filter((job: any) => job.status === status);
    }
    
    // Filter by category
    if (category && category !== 'all') {
      jobs = jobs.filter((job: any) => job.category === category);
    }
    
    // Filter by urgency
    if (urgency) {
      jobs = jobs.filter((job: any) => job.urgency === urgency);
    }
    
    // Filter emergency jobs
    if (emergency) {
      jobs = jobs.filter((job: any) => job.isEmergency === true);
    }
    
    // Filter by location if coordinates provided
    if (lat && lng) {
      const userLat = parseFloat(lat);
      const userLng = parseFloat(lng);
      
      jobs = jobs.map((job: any) => {
        const distance = calculateDistance(
          userLat, userLng,
          job.location.lat, job.location.lng
        );
        return { ...job, distance };
      }).filter((job: any) => job.distance <= radius)
        .sort((a: any, b: any) => {
          // Sort emergency jobs first, then by distance
          if (a.isEmergency !== b.isEmergency) {
            return b.isEmergency ? 1 : -1;
          }
          return a.distance - b.distance;
        });
    } else {
      // Sort by urgency and creation time
      jobs.sort((a: any, b: any) => {
        const urgencyOrder = { emergency: 4, high: 3, normal: 2, low: 1 };
        const aUrgency = urgencyOrder[a.urgency as keyof typeof urgencyOrder] || 0;
        const bUrgency = urgencyOrder[b.urgency as keyof typeof urgencyOrder] || 0;
        
        if (aUrgency !== bUrgency) {
          return bUrgency - aUrgency;
        }
        
        return new Date(b.postedAt).getTime() - new Date(a.postedAt).getTime();
      });
    }
    
    return NextResponse.json(jobs.slice(0, limit));
  } catch (error) {
    console.error('Error fetching jobs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch jobs' },
      { status: 500 }
    );
  }
}

// POST /api/booksy/albafree/jobs - Create new job
export async function POST(request: NextRequest) {
  try {
    const jobData = await request.json();
    const jobs = readJobsFile();
    
    // Generate new job ID
    const newId = `job-${String(jobs.length + 1).padStart(3, '0')}`;
    
    const newJob = {
      id: newId,
      ...jobData,
      postedAt: new Date().toISOString(),
      status: 'open',
      applicants: [],
      isEmergency: jobData.urgency === 'emergency' || jobData.isEmergency || false,
      bbeonjjeokCall: jobData.bbeonjjeokCall || false
    };
    
    jobs.push(newJob);
    
    if (writeJobsFile(jobs)) {
      return NextResponse.json(newJob, { status: 201 });
    } else {
      return NextResponse.json(
        { error: 'Failed to save job' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error creating job:', error);
    return NextResponse.json(
      { error: 'Failed to create job' },
      { status: 500 }
    );
  }
}

// Helper function to calculate distance between two coordinates
function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}
