import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const JOBS_FILE = path.join(process.cwd(), 'data/apps/booksy/albafree/jobs.json');
const MATCHES_FILE = path.join(process.cwd(), 'data/apps/booksy/albafree/matches.json');
const NOTIFICATIONS_FILE = path.join(process.cwd(), 'data/apps/booksy/albafree/notifications.json');

function readJobsFile() {
  try {
    const fileContents = fs.readFileSync(JOBS_FILE, 'utf8');
    return JSON.parse(fileContents);
  } catch (error) {
    console.error('Error reading jobs file:', error);
    return [];
  }
}

function writeJobsFile(jobs: any[]) {
  try {
    fs.writeFileSync(JOBS_FILE, JSON.stringify(jobs, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing jobs file:', error);
    return false;
  }
}

function readMatchesFile() {
  try {
    const fileContents = fs.readFileSync(MATCHES_FILE, 'utf8');
    return JSON.parse(fileContents);
  } catch (error) {
    console.error('Error reading matches file:', error);
    return [];
  }
}

function writeMatchesFile(matches: any[]) {
  try {
    fs.writeFileSync(MATCHES_FILE, JSON.stringify(matches, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing matches file:', error);
    return false;
  }
}

function readNotificationsFile() {
  try {
    const fileContents = fs.readFileSync(NOTIFICATIONS_FILE, 'utf8');
    return JSON.parse(fileContents);
  } catch (error) {
    console.error('Error reading notifications file:', error);
    return [];
  }
}

function writeNotificationsFile(notifications: any[]) {
  try {
    fs.writeFileSync(NOTIFICATIONS_FILE, JSON.stringify(notifications, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing notifications file:', error);
    return false;
  }
}

// POST /api/booksy/albafree/jobs/apply - Apply to a job
export async function POST(request: NextRequest) {
  try {
    const { jobId, workerId, message } = await request.json();
    
    if (!jobId || !workerId) {
      return NextResponse.json(
        { error: 'jobId and workerId are required' },
        { status: 400 }
      );
    }
    
    const jobs = readJobsFile();
    const matches = readMatchesFile();
    const notifications = readNotificationsFile();
    
    // Find the job
    const jobIndex = jobs.findIndex((job: any) => job.id === jobId);
    if (jobIndex === -1) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      );
    }
    
    const job = jobs[jobIndex];
    
    // Check if job is still open
    if (job.status !== 'open') {
      return NextResponse.json(
        { error: 'Job is no longer available' },
        { status: 400 }
      );
    }
    
    // Check if worker has already applied
    if (job.applicants.includes(workerId)) {
      return NextResponse.json(
        { error: 'Already applied to this job' },
        { status: 400 }
      );
    }
    
    // Add worker to applicants
    jobs[jobIndex].applicants.push(workerId);
    
    // Create match record
    const matchId = `match-${String(matches.length + 1).padStart(3, '0')}`;
    const newMatch = {
      id: matchId,
      jobId,
      workerId,
      employerId: job.postedBy,
      status: 'pending',
      createdAt: new Date().toISOString(),
      respondedAt: null,
      acceptedAt: null,
      completedAt: null,
      type: 'application',
      bbeonjjeokCall: false,
      workerResponse: 'applied',
      employerResponse: null,
      rating: null,
      review: null,
      message: message || null
    };
    
    matches.push(newMatch);
    
    // Create notification for employer
    const notificationId = `notif-${String(notifications.length + 1).padStart(3, '0')}`;
    const newNotification = {
      id: notificationId,
      userId: job.postedBy,
      type: 'new_application',
      title: '새로운 지원자',
      message: `'${job.title}' 공고에 새로운 지원자가 있습니다.`,
      data: {
        jobId,
        workerId,
        matchId
      },
      isRead: false,
      createdAt: new Date().toISOString(),
      expiresAt: null,
      priority: 'normal'
    };
    
    notifications.push(newNotification);
    
    // Save all files
    if (writeJobsFile(jobs) && writeMatchesFile(matches) && writeNotificationsFile(notifications)) {
      return NextResponse.json({
        success: true,
        match: newMatch,
        message: 'Application submitted successfully'
      });
    } else {
      return NextResponse.json(
        { error: 'Failed to save application' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error applying to job:', error);
    return NextResponse.json(
      { error: 'Failed to apply to job' },
      { status: 500 }
    );
  }
}
