import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const JOBS_FILE = path.join(process.cwd(), 'data/apps/booksy/albafree/jobs.json');

function readJobsFile() {
  try {
    const fileContents = fs.readFileSync(JOBS_FILE, 'utf8');
    return JSON.parse(fileContents);
  } catch (error) {
    console.error('Error reading jobs file:', error);
    return [];
  }
}

function writeJobsFile(jobs: any[]) {
  try {
    fs.writeFileSync(JOBS_FILE, JSON.stringify(jobs, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing jobs file:', error);
    return false;
  }
}

// GET /api/booksy/albafree/jobs/[id] - Get job by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const jobs = readJobsFile();
    const job = jobs.find((j: any) => j.id === resolvedParams.id);
    
    if (!job) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(job);
  } catch (error) {
    console.error('Error fetching job:', error);
    return NextResponse.json(
      { error: 'Failed to fetch job' },
      { status: 500 }
    );
  }
}

// PUT /api/booksy/albafree/jobs/[id] - Update job
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const updateData = await request.json();
    const jobs = readJobsFile();
    const jobIndex = jobs.findIndex((j: any) => j.id === resolvedParams.id);
    
    if (jobIndex === -1) {
      return NextResponse.json(
        { error: 'Job not found' },
        { status: 404 }
      );
    }
    
    // Update job data
    jobs[jobIndex] = { ...jobs[jobIndex], ...updateData };
    
    if (writeJobsFile(jobs)) {
      return NextResponse.json(jobs[jobIndex]);
    } else {
      return NextResponse.json(
        { error: 'Failed to update job' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error updating job:', error);
    return NextResponse.json(
      { error: 'Failed to update job' },
      { status: 500 }
    );
  }
}
