import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const USERS_FILE = path.join(process.cwd(), 'data/apps/booksy/albafree/users.json');

function readUsersFile() {
  try {
    const fileContents = fs.readFileSync(USERS_FILE, 'utf8');
    return JSON.parse(fileContents);
  } catch (error) {
    console.error('Error reading users file:', error);
    return [];
  }
}

function writeUsersFile(users: any[]) {
  try {
    fs.writeFileSync(USERS_FILE, JSON.stringify(users, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing users file:', error);
    return false;
  }
}

// GET /api/booksy/albafree/users - Get all users or filter by role
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const role = searchParams.get('role');
    const lat = searchParams.get('lat');
    const lng = searchParams.get('lng');
    const radius = parseFloat(searchParams.get('radius') || '5');
    
    const users = readUsersFile();
    
    let filteredUsers = users;
    
    // Filter by role if specified
    if (role && ['owner', 'worker'].includes(role)) {
      filteredUsers = filteredUsers.filter((user: any) => user.role === role);
    }
    
    // Calculate distance if coordinates provided
    if (lat && lng) {
      const userLat = parseFloat(lat);
      const userLng = parseFloat(lng);
      
      filteredUsers = filteredUsers.map((user: any) => {
        const distance = calculateDistance(
          userLat, userLng,
          user.location.lat, user.location.lng
        );
        return { ...user, distance };
      }).filter((user: any) => user.distance <= radius)
        .sort((a: any, b: any) => a.distance - b.distance);
    }
    
    return NextResponse.json(filteredUsers);
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

// POST /api/booksy/albafree/users - Create new user
export async function POST(request: NextRequest) {
  try {
    const userData = await request.json();
    const users = readUsersFile();
    
    // Generate new user ID
    const newId = `user-${String(users.length + 1).padStart(3, '0')}`;
    
    const newUser = {
      id: newId,
      ...userData,
      rating: 0,
      totalRatings: 0,
      isVerified: false,
      isOnline: true,
      lastSeen: new Date().toISOString(),
      stats: {
        completedJobs: 0,
        responseTime: "신규",
        reliability: 100,
        noShowRate: 0
      },
      verification: {
        idVerified: false,
        phoneVerified: false,
        emailVerified: false,
        backgroundCheck: false
      },
      workHistory: []
    };
    
    users.push(newUser);
    
    if (writeUsersFile(users)) {
      return NextResponse.json(newUser, { status: 201 });
    } else {
      return NextResponse.json(
        { error: 'Failed to save user' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    );
  }
}

// Helper function to calculate distance between two coordinates
function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}
