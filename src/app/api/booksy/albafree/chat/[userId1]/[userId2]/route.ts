import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const CHATS_FILE = path.join(process.cwd(), 'data/apps/booksy/albafree/chats.json');

function readChatsFile() {
  try {
    const fileContents = fs.readFileSync(CHATS_FILE, 'utf8');
    return JSON.parse(fileContents);
  } catch (error) {
    console.error('Error reading chats file:', error);
    return [];
  }
}

function writeChatsFile(chats: any[]) {
  try {
    // Ensure directory exists
    const dir = path.dirname(CHATS_FILE);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    fs.writeFileSync(CHATS_FILE, JSON.stringify(chats, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing chats file:', error);
    return false;
  }
}

// GET /api/booksy/albafree/chat/[userId1]/[userId2] - Get chat messages between two users
export async function GET(
  request: NextRequest,
  { params }: { params: { userId1: string; userId2: string } }
) {
  try {
    const { userId1, userId2 } = params;
    
    if (!userId1 || !userId2) {
      return NextResponse.json(
        { error: 'Both user IDs are required' },
        { status: 400 }
      );
    }
    
    const chats = readChatsFile();
    
    // Find messages between the two users
    const messages = chats.filter((message: any) => 
      (message.senderId === userId1 && message.receiverId === userId2) ||
      (message.senderId === userId2 && message.receiverId === userId1)
    ).sort((a: any, b: any) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
    
    // Mark messages as read for the requesting user (userId1)
    const updatedChats = chats.map((message: any) => {
      if (message.senderId === userId2 && message.receiverId === userId1 && !message.isRead) {
        return { ...message, isRead: true };
      }
      return message;
    });
    
    // Save updated read status
    writeChatsFile(updatedChats);
    
    return NextResponse.json(messages);
  } catch (error) {
    console.error('Error fetching chat messages:', error);
    return NextResponse.json(
      { error: 'Failed to fetch chat messages' },
      { status: 500 }
    );
  }
}
