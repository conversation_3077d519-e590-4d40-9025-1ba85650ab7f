import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const CHATS_FILE = path.join(process.cwd(), 'data/apps/booksy/albafree/chats.json');

function readChatsFile() {
  try {
    const fileContents = fs.readFileSync(CHATS_FILE, 'utf8');
    return JSON.parse(fileContents);
  } catch (error) {
    console.error('Error reading chats file:', error);
    return [];
  }
}

function writeChatsFile(chats: any[]) {
  try {
    // Ensure directory exists
    const dir = path.dirname(CHATS_FILE);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    fs.writeFileSync(CHATS_FILE, JSON.stringify(chats, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing chats file:', error);
    return false;
  }
}

// POST /api/booksy/albafree/chat/send - Send a message
export async function POST(request: NextRequest) {
  try {
    const { receiverId, content, type = 'text', jobOffer } = await request.json();
    
    if (!receiverId || !content) {
      return NextResponse.json(
        { error: 'receiverId and content are required' },
        { status: 400 }
      );
    }
    
    // Mock current user ID - in real app, get from authentication
    const senderId = 'user-001';
    
    const chats = readChatsFile();
    
    // Generate new message ID
    const messageId = `msg-${String(chats.length + 1).padStart(6, '0')}`;
    
    const newMessage = {
      id: messageId,
      senderId,
      receiverId,
      content,
      timestamp: new Date().toISOString(),
      type,
      isRead: false,
      ...(jobOffer && { jobOffer })
    };
    
    chats.push(newMessage);
    
    if (writeChatsFile(chats)) {
      return NextResponse.json(newMessage, { status: 201 });
    } else {
      return NextResponse.json(
        { error: 'Failed to save message' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error sending message:', error);
    return NextResponse.json(
      { error: 'Failed to send message' },
      { status: 500 }
    );
  }
}
