import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const NOTIFICATIONS_FILE = path.join(process.cwd(), 'data/apps/booksy/albafree/notifications.json');
const MATCHES_FILE = path.join(process.cwd(), 'data/apps/booksy/albafree/matches.json');

function readNotificationsFile() {
  try {
    const fileContents = fs.readFileSync(NOTIFICATIONS_FILE, 'utf8');
    return JSON.parse(fileContents);
  } catch (error) {
    console.error('Error reading notifications file:', error);
    return [];
  }
}

function writeNotificationsFile(notifications: any[]) {
  try {
    fs.writeFileSync(NOTIFICATIONS_FILE, JSON.stringify(notifications, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing notifications file:', error);
    return false;
  }
}

function readMatchesFile() {
  try {
    const fileContents = fs.readFileSync(MATCHES_FILE, 'utf8');
    return JSON.parse(fileContents);
  } catch (error) {
    console.error('Error reading matches file:', error);
    return [];
  }
}

function writeMatchesFile(matches: any[]) {
  try {
    fs.writeFileSync(MATCHES_FILE, JSON.stringify(matches, null, 2));
    return true;
  } catch (error) {
    console.error('Error writing matches file:', error);
    return false;
  }
}

// POST /api/booksy/albafree/bbeonjjeok-call - Send Bbeonjjeok Call
export async function POST(request: NextRequest) {
  try {
    const { targetUserId, senderId, jobId, message } = await request.json();
    
    if (!targetUserId || !senderId) {
      return NextResponse.json(
        { error: 'targetUserId and senderId are required' },
        { status: 400 }
      );
    }
    
    const notifications = readNotificationsFile();
    const matches = readMatchesFile();
    
    // Create notification
    const notificationId = `notif-${String(notifications.length + 1).padStart(3, '0')}`;
    const newNotification = {
      id: notificationId,
      userId: targetUserId,
      type: 'bbeonjjeok_call',
      title: '번쩍콜이 도착했습니다!',
      message: message || '즉시 근무 가능한 직원을 찾고 있습니다.',
      data: {
        jobId: jobId || null,
        employerId: senderId,
        urgency: 'emergency'
      },
      isRead: false,
      createdAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 30 * 60 * 1000).toISOString(), // 30 minutes
      priority: 'high'
    };
    
    notifications.push(newNotification);
    
    // Create match if jobId is provided
    if (jobId) {
      const matchId = `match-${String(matches.length + 1).padStart(3, '0')}`;
      const newMatch = {
        id: matchId,
        jobId,
        workerId: targetUserId,
        employerId: senderId,
        status: 'pending',
        createdAt: new Date().toISOString(),
        respondedAt: null,
        acceptedAt: null,
        completedAt: null,
        type: 'bbeonjjeok_call',
        bbeonjjeokCall: true,
        workerResponse: null,
        employerResponse: null,
        rating: null,
        review: null
      };
      
      matches.push(newMatch);
      writeMatchesFile(matches);
    }
    
    if (writeNotificationsFile(notifications)) {
      return NextResponse.json({
        success: true,
        notification: newNotification,
        message: 'Bbeonjjeok Call sent successfully'
      });
    } else {
      return NextResponse.json(
        { error: 'Failed to send Bbeonjjeok Call' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error sending Bbeonjjeok Call:', error);
    return NextResponse.json(
      { error: 'Failed to send Bbeonjjeok Call' },
      { status: 500 }
    );
  }
}
