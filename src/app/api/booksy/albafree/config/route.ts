import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const CONFIG_FILE = path.join(process.cwd(), 'data/apps/booksy/albafree/config.json');

function readConfigFile() {
  try {
    const fileContents = fs.readFileSync(CONFIG_FILE, 'utf8');
    return JSON.parse(fileContents);
  } catch (error) {
    console.error('Error reading config file:', error);
    return {};
  }
}

// GET /api/booksy/albafree/config - Get app configuration
export async function GET(request: NextRequest) {
  try {
    const config = readConfigFile();
    return NextResponse.json(config);
  } catch (error) {
    console.error('Error fetching config:', error);
    return NextResponse.json(
      { error: 'Failed to fetch config' },
      { status: 500 }
    );
  }
}
