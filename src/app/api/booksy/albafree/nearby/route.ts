import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

const USERS_FILE = path.join(process.cwd(), 'data/apps/booksy/albafree/users.json');

function readUsersFile() {
  try {
    const fileContents = fs.readFileSync(USERS_FILE, 'utf8');
    return JSON.parse(fileContents);
  } catch (error) {
    console.error('Error reading users file:', error);
    return [];
  }
}

// Helper function to calculate distance between two coordinates
function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

// GET /api/booksy/albafree/nearby - Get nearby users based on role and location
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const role = searchParams.get('role'); // 'owner' or 'worker'
    const lat = searchParams.get('lat') || '21.0285'; // Default to Hanoi
    const lng = searchParams.get('lng') || '105.8542';
    const radius = parseFloat(searchParams.get('radius') || '5');
    const limit = parseInt(searchParams.get('limit') || '20');
    const refresh = searchParams.get('refresh') === 'true';
    
    if (!role || !['owner', 'worker'].includes(role)) {
      return NextResponse.json(
        { error: 'Valid role parameter required (owner or worker)' },
        { status: 400 }
      );
    }
    
    const users = readUsersFile();
    const userLat = parseFloat(lat);
    const userLng = parseFloat(lng);
    
    // Filter users by role and calculate distances
    let nearbyUsers = users
      .filter((user: any) => user.role === role)
      .map((user: any) => {
        const distance = calculateDistance(
          userLat, userLng,
          user.location.lat, user.location.lng
        );
        return { ...user, distance };
      })
      .filter((user: any) => user.distance <= radius);

    // If refresh is requested, simulate some users going online/offline
    if (refresh) {
      nearbyUsers = nearbyUsers.map((user: any) => ({
        ...user,
        isOnline: Math.random() > 0.3, // 70% chance of being online
        lastSeen: user.isOnline ? new Date().toISOString() : user.lastSeen
      }));
    }

    nearbyUsers = nearbyUsers
      .sort((a: any, b: any) => {
        // Sort by online status first, then by distance
        if (a.isOnline !== b.isOnline) {
          return b.isOnline ? 1 : -1;
        }
        return a.distance - b.distance;
      })
      .slice(0, limit);
    
    return NextResponse.json(nearbyUsers);
  } catch (error) {
    console.error('Error fetching nearby users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch nearby users' },
      { status: 500 }
    );
  }
}
