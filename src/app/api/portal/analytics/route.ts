import { NextRequest, NextResponse } from 'next/server';
import { getAnalytics, trackAppLaunch, trackSearch } from '@/app/portal/utils/dataManager';

export async function GET() {
  try {
    const analytics = await getAnalytics();
    return NextResponse.json({ analytics });
  } catch (error) {
    console.error('Error fetching analytics:', error);
    return NextResponse.json({ error: 'Failed to fetch analytics' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check if request has content
    const contentLength = request.headers.get('content-length');
    if (!contentLength || contentLength === '0') {
      return NextResponse.json({ error: 'Empty request body' }, { status: 400 });
    }

    const body = await request.json();
    const { action, data } = body || {};

    if (!action) {
      return NextResponse.json({ error: 'Missing action parameter' }, { status: 400 });
    }

    let success = false;

    switch (action) {
      case 'trackAppLaunch':
        success = await trackAppLaunch(data?.appId, data?.userId);
        break;
      case 'trackSearch':
        success = await trackSearch(data?.query);
        break;
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }

    if (success) {
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json({ error: 'Failed to track action' }, { status: 500 });
    }
  } catch (error) {
    console.error('Error tracking analytics:', error);
    return NextResponse.json({ error: 'Failed to track analytics' }, { status: 500 });
  }
}
