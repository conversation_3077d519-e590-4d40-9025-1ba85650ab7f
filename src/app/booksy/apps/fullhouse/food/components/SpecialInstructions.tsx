'use client';

import { useState } from 'react';
import { 
  MessageSquare, 
  Plus, 
  X, 
  Clock, 
  Utensils, 
  Truck, 
  AlertCircle,
  Check
} from 'lucide-react';

interface SpecialInstructionsProps {
  onInstructionsChange: (instructions: string[], customNote: string) => void;
  initialInstructions?: string[];
  initialCustomNote?: string;
}

const SpecialInstructions = ({ 
  onInstructionsChange, 
  initialInstructions = [], 
  initialCustomNote = '' 
}: SpecialInstructionsProps) => {
  const [selectedInstructions, setSelectedInstructions] = useState<string[]>(initialInstructions);
  const [customNote, setCustomNote] = useState(initialCustomNote);
  const [showCustomInput, setShowCustomInput] = useState(false);

  const predefinedInstructions = [
    {
      category: 'Chuẩn bị món ăn',
      icon: Utensils,
      color: 'text-orange-500',
      items: [
        'Ít muối',
        'Không đường',
        '<PERSON><PERSON> nhẹ',
        '<PERSON><PERSON> vừa',
        '<PERSON><PERSON>t cay',
        '<PERSON>hông hành',
        '<PERSON>hông tỏi',
        '<PERSON>hông ngò',
        'Thêm rau',
        '<PERSON>t dầu',
        'Nướng kỹ',
        'Tái hơn',
        'Chín kỹ'
      ]
    },
    {
      category: 'Đóng gói',
      icon: Package,
      color: 'text-blue-500',
      items: [
        'Đóng gói riêng từng món',
        'Không cần đũa',
        'Không cần thìa',
        'Gói riêng nước mắm',
        'Gói riêng tương ớt',
        'Đá riêng',
        'Nước dùng riêng',
        'Cắt nhỏ',
        'Không cắt'
      ]
    },
    {
      category: 'Giao hàng',
      icon: Truck,
      color: 'text-green-500',
      items: [
        'Gọi điện trước khi đến',
        'Để ở bàn bảo vệ',
        'Giao tận tay',
        'Không gọi chuông',
        'Giao hàng im lặng',
        'Liên hệ qua tin nhắn',
        'Giao hàng không tiếp xúc'
      ]
    },
    {
      category: 'Thời gian',
      icon: Clock,
      color: 'text-purple-500',
      items: [
        'Giao sớm nhất có thể',
        'Không vội',
        'Giao đúng giờ hẹn',
        'Giao trong giờ nghỉ trưa',
        'Giao sau 18:00'
      ]
    }
  ];

  const handleInstructionToggle = (instruction: string) => {
    const newInstructions = selectedInstructions.includes(instruction)
      ? selectedInstructions.filter(item => item !== instruction)
      : [...selectedInstructions, instruction];
    
    setSelectedInstructions(newInstructions);
    onInstructionsChange(newInstructions, customNote);
  };

  const handleCustomNoteChange = (note: string) => {
    setCustomNote(note);
    onInstructionsChange(selectedInstructions, note);
  };

  const clearAllInstructions = () => {
    setSelectedInstructions([]);
    setCustomNote('');
    setShowCustomInput(false);
    onInstructionsChange([], '');
  };

  const totalInstructions = selectedInstructions.length + (customNote.trim() ? 1 : 0);

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
          <MessageSquare className="w-5 h-5 text-orange-500" />
          <span>Yêu Cầu Đặc Biệt</span>
          {totalInstructions > 0 && (
            <span className="bg-orange-100 text-orange-800 text-sm px-2 py-1 rounded-full">
              {totalInstructions}
            </span>
          )}
        </h3>
        {totalInstructions > 0 && (
          <button
            onClick={clearAllInstructions}
            className="text-sm text-gray-500 hover:text-gray-700"
          >
            Xóa tất cả
          </button>
        )}
      </div>

      <div className="space-y-6">
        {predefinedInstructions.map((category) => {
          const IconComponent = category.icon;
          return (
            <div key={category.category}>
              <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center space-x-2">
                <IconComponent className={`w-4 h-4 ${category.color}`} />
                <span>{category.category}</span>
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {category.items.map((instruction) => {
                  const isSelected = selectedInstructions.includes(instruction);
                  return (
                    <button
                      key={instruction}
                      onClick={() => handleInstructionToggle(instruction)}
                      className={`flex items-center justify-between px-3 py-2 rounded-lg text-sm transition-all ${
                        isSelected
                          ? 'bg-orange-100 text-orange-800 border border-orange-300'
                          : 'bg-gray-50 text-gray-700 hover:bg-gray-100 border border-transparent'
                      }`}
                    >
                      <span>{instruction}</span>
                      {isSelected && (
                        <Check className="w-4 h-4 ml-2 flex-shrink-0" />
                      )}
                    </button>
                  );
                })}
              </div>
            </div>
          );
        })}

        {/* Custom Instructions */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium text-gray-900">Ghi Chú Khác</h4>
            {!showCustomInput && (
              <button
                onClick={() => setShowCustomInput(true)}
                className="flex items-center space-x-1 text-sm text-orange-600 hover:text-orange-800"
              >
                <Plus className="w-4 h-4" />
                <span>Thêm ghi chú</span>
              </button>
            )}
          </div>

          {showCustomInput && (
            <div className="space-y-3">
              <textarea
                value={customNote}
                onChange={(e) => handleCustomNoteChange(e.target.value)}
                placeholder="Nhập yêu cầu đặc biệt của bạn..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 resize-none"
                maxLength={200}
              />
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-500">
                  {customNote.length}/200 ký tự
                </span>
                <div className="flex space-x-2">
                  <button
                    onClick={() => {
                      setShowCustomInput(false);
                      handleCustomNoteChange('');
                    }}
                    className="text-sm text-gray-500 hover:text-gray-700"
                  >
                    Hủy
                  </button>
                  <button
                    onClick={() => setShowCustomInput(false)}
                    className="text-sm text-orange-600 hover:text-orange-800"
                  >
                    Xong
                  </button>
                </div>
              </div>
            </div>
          )}

          {customNote && !showCustomInput && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
              <div className="flex items-start justify-between">
                <p className="text-sm text-gray-700 flex-1">{customNote}</p>
                <button
                  onClick={() => setShowCustomInput(true)}
                  className="text-gray-400 hover:text-gray-600 ml-2"
                >
                  <Edit className="w-4 h-4" />
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Summary */}
      {totalInstructions > 0 && (
        <div className="mt-6 pt-4 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Tóm Tắt Yêu Cầu</h4>
          <div className="space-y-2">
            {selectedInstructions.map((instruction, index) => (
              <div key={index} className="flex items-center space-x-2 text-sm">
                <Check className="w-4 h-4 text-green-500 flex-shrink-0" />
                <span className="text-gray-700">{instruction}</span>
                <button
                  onClick={() => handleInstructionToggle(instruction)}
                  className="text-gray-400 hover:text-red-500 ml-auto"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            ))}
            {customNote && (
              <div className="flex items-start space-x-2 text-sm">
                <MessageSquare className="w-4 h-4 text-blue-500 flex-shrink-0 mt-0.5" />
                <span className="text-gray-700 flex-1">{customNote}</span>
                <button
                  onClick={() => handleCustomNoteChange('')}
                  className="text-gray-400 hover:text-red-500"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Important Notes */}
      <div className="mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div className="flex items-start space-x-2">
          <AlertCircle className="w-4 h-4 text-yellow-600 flex-shrink-0 mt-0.5" />
          <div className="text-sm text-yellow-800">
            <p className="font-medium mb-1">Lưu ý quan trọng:</p>
            <ul className="space-y-1 text-xs">
              <li>• Một số yêu cầu có thể không thực hiện được tùy theo tình hình nhà hàng</li>
              <li>• Yêu cầu đặc biệt có thể ảnh hưởng đến thời gian chuẩn bị</li>
              <li>• Nhà hàng sẽ liên hệ nếu không thể đáp ứng yêu cầu</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

// Add missing import
import { Package, Edit } from 'lucide-react';

export default SpecialInstructions;
