'use client';

import { useState, useEffect } from 'react';
import { 
  MapPin, 
  Clock, 
  DollarSign, 
  Check, 
  AlertCircle,
  Truck,
  Navigation,
  Home
} from 'lucide-react';

interface DeliveryZone {
  name: string;
  fee: number;
  estimatedTime: { min: number; max: number };
  isAvailable: boolean;
  description?: string;
}

interface DeliveryZonesProps {
  zones: DeliveryZone[];
  selectedZone?: string;
  onZoneSelect: (zoneName: string) => void;
  freeDeliveryThreshold?: number;
  currentOrderValue?: number;
}

const DeliveryZones = ({ 
  zones, 
  selectedZone, 
  onZoneSelect, 
  freeDeliveryThreshold = 200000,
  currentOrderValue = 0 
}: DeliveryZonesProps) => {
  const [userLocation, setUserLocation] = useState<string>('');
  const [isDetectingLocation, setIsDetectingLocation] = useState(false);

  const detectUserLocation = async () => {
    setIsDetectingLocation(true);
    try {
      // Mock location detection - in real app would use geolocation API
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock detected location
      const mockLocations = ['Times City', 'Royal City', 'Landmark 72', 'Lotte Center'];
      const detectedLocation = mockLocations[Math.floor(Math.random() * mockLocations.length)];
      setUserLocation(detectedLocation);
      
      // Auto-select the detected zone
      const matchingZone = zones.find(zone => 
        zone.name.toLowerCase().includes(detectedLocation.toLowerCase())
      );
      if (matchingZone) {
        onZoneSelect(matchingZone.name);
      }
    } catch (error) {
      console.error('Failed to detect location:', error);
    } finally {
      setIsDetectingLocation(false);
    }
  };

  const getDeliveryFee = (zone: DeliveryZone) => {
    if (currentOrderValue >= freeDeliveryThreshold) {
      return 0;
    }
    return zone.fee;
  };

  const isFreeDelivery = (zone: DeliveryZone) => {
    return currentOrderValue >= freeDeliveryThreshold || zone.fee === 0;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
          <Truck className="w-5 h-5 text-orange-500" />
          <span>Khu Vực Giao Hàng</span>
        </h3>
        <button
          onClick={detectUserLocation}
          disabled={isDetectingLocation}
          className="flex items-center space-x-2 px-3 py-2 bg-orange-100 text-orange-700 rounded-lg hover:bg-orange-200 disabled:opacity-50"
        >
          {isDetectingLocation ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-500"></div>
              <span>Đang xác định...</span>
            </>
          ) : (
            <>
              <Navigation className="w-4 h-4" />
              <span>Tự động xác định</span>
            </>
          )}
        </button>
      </div>

      {userLocation && (
        <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <Home className="w-4 h-4 text-green-600" />
            <span className="text-sm text-green-800">
              Vị trí được xác định: <strong>{userLocation}</strong>
            </span>
          </div>
        </div>
      )}

      {/* Free Delivery Threshold Info */}
      {freeDeliveryThreshold > 0 && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <DollarSign className="w-4 h-4 text-blue-600" />
            <span className="text-sm text-blue-800">
              Miễn phí giao hàng cho đơn từ {freeDeliveryThreshold.toLocaleString('vi-VN')}đ
              {currentOrderValue > 0 && (
                <span className="ml-2">
                  (Còn thiếu: {Math.max(0, freeDeliveryThreshold - currentOrderValue).toLocaleString('vi-VN')}đ)
                </span>
              )}
            </span>
          </div>
        </div>
      )}

      <div className="space-y-3">
        {zones.map((zone) => {
          const deliveryFee = getDeliveryFee(zone);
          const isSelected = selectedZone === zone.name;
          const isAvailable = zone.isAvailable;
          
          return (
            <button
              key={zone.name}
              onClick={() => isAvailable && onZoneSelect(zone.name)}
              disabled={!isAvailable}
              className={`w-full p-4 border rounded-lg text-left transition-all ${
                isSelected
                  ? 'border-orange-500 bg-orange-50'
                  : isAvailable
                  ? 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  : 'border-gray-100 bg-gray-50 opacity-50 cursor-not-allowed'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <MapPin className={`w-5 h-5 ${isSelected ? 'text-orange-500' : 'text-gray-400'}`} />
                    <h4 className={`font-medium ${isSelected ? 'text-orange-900' : 'text-gray-900'}`}>
                      {zone.name}
                    </h4>
                    {isSelected && (
                      <Check className="w-5 h-5 text-orange-500" />
                    )}
                    {!isAvailable && (
                      <AlertCircle className="w-5 h-5 text-red-500" />
                    )}
                  </div>
                  
                  {zone.description && (
                    <p className="text-sm text-gray-600 mb-2">{zone.description}</p>
                  )}
                  
                  <div className="flex items-center space-x-4 text-sm">
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-600">
                        {zone.estimatedTime.min}-{zone.estimatedTime.max} phút
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <DollarSign className="w-4 h-4 text-gray-400" />
                      <span className={`${isFreeDelivery(zone) ? 'text-green-600 font-medium' : 'text-gray-600'}`}>
                        {isFreeDelivery(zone) ? 'Miễn phí' : `${deliveryFee.toLocaleString('vi-VN')}đ`}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              
              {!isAvailable && (
                <div className="mt-2 text-sm text-red-600">
                  Hiện tại không giao hàng đến khu vực này
                </div>
              )}
            </button>
          );
        })}
      </div>

      {zones.length === 0 && (
        <div className="text-center py-8">
          <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Chưa có khu vực giao hàng</h3>
          <p className="text-gray-500">Nhà hàng chưa thiết lập khu vực giao hàng</p>
        </div>
      )}

      {/* Additional Delivery Info */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <h4 className="text-sm font-medium text-gray-900 mb-3">Lưu Ý Giao Hàng</h4>
        <div className="space-y-2 text-sm text-gray-600">
          <div className="flex items-start space-x-2">
            <div className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
            <span>Thời gian giao hàng có thể thay đổi tùy theo tình hình giao thông</span>
          </div>
          <div className="flex items-start space-x-2">
            <div className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
            <span>Phí giao hàng được tính dựa trên khoảng cách và khu vực</span>
          </div>
          <div className="flex items-start space-x-2">
            <div className="w-1.5 h-1.5 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
            <span>Đơn hàng từ {freeDeliveryThreshold.toLocaleString('vi-VN')}đ được miễn phí giao hàng</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeliveryZones;
