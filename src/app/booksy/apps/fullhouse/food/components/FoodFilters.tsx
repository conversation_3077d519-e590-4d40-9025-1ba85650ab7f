'use client';

import { useState, useEffect } from 'react';
import { 
  Filter, 
  X, 
  Check,
  Star,
  Clock,
  DollarSign,
  Leaf,
  AlertTriangle,
  ChefHat
} from 'lucide-react';

interface FilterProps {
  onFiltersChange: (filters: any) => void;
  initialFilters?: any;
}

interface FilterOption {
  id: string;
  name: string;
  icon?: string;
  color?: string;
  description?: string;
}

const FoodFilters = ({ onFiltersChange, initialFilters = {} }: FilterProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [filters, setFilters] = useState({
    cuisineTypes: [],
    dietaryRestrictions: [],
    priceRange: '',
    rating: 0,
    deliveryTime: 0,
    spicyLevel: 0,
    allergens: [],
    features: [],
    ...initialFilters
  });

  const cuisineTypes = [
    { id: 'vietnamese', name: '<PERSON><PERSON> V<PERSON>ệ<PERSON>', icon: '🇻🇳' },
    { id: 'italian', name: '<PERSON><PERSON> Ý', icon: '🇮🇹' },
    { id: 'chinese', name: '<PERSON><PERSON>', icon: '🇨🇳' },
    { id: 'japanese', name: '<PERSON><PERSON>', icon: '🇯🇵' },
    { id: 'korean', name: '<PERSON>ón Hàn', icon: '🇰🇷' },
    { id: 'thai', name: 'Món Thái', icon: '🇹🇭' },
    { id: 'western', name: 'Món Âu', icon: '🍽️' },
    { id: 'indian', name: 'Món Ấn', icon: '🇮🇳' }
  ];

  const dietaryRestrictions = [
    { id: 'vegetarian', name: 'Chay', icon: '🥬', color: 'bg-green-100 text-green-800' },
    { id: 'vegan', name: 'Thuần Chay', icon: '🌱', color: 'bg-green-100 text-green-800' },
    { id: 'gluten_free', name: 'Không Gluten', icon: '🌾', color: 'bg-yellow-100 text-yellow-800' },
    { id: 'dairy_free', name: 'Không Sữa', icon: '🥛', color: 'bg-blue-100 text-blue-800' },
    { id: 'nut_free', name: 'Không Hạt', icon: '🥜', color: 'bg-orange-100 text-orange-800' },
    { id: 'halal', name: 'Halal', icon: '☪️', color: 'bg-emerald-100 text-emerald-800' },
    { id: 'low_carb', name: 'Ít Carb', icon: '🥩', color: 'bg-red-100 text-red-800' },
    { id: 'keto', name: 'Keto', icon: '🥑', color: 'bg-purple-100 text-purple-800' }
  ];

  const priceRanges = [
    { id: 'budget', name: 'Bình Dân', description: 'Dưới 100.000đ', icon: '💰' },
    { id: 'mid_range', name: 'Trung Bình', description: '100.000đ - 300.000đ', icon: '💰💰' },
    { id: 'premium', name: 'Cao Cấp', description: '300.000đ - 500.000đ', icon: '💰💰💰' },
    { id: 'luxury', name: 'Sang Trọng', description: 'Trên 500.000đ', icon: '💎' }
  ];

  const spicyLevels = [
    { id: 0, name: 'Không Cay', icon: '😊', color: 'bg-gray-100 text-gray-800' },
    { id: 1, name: 'Nhẹ', icon: '🌶️', color: 'bg-green-100 text-green-800' },
    { id: 2, name: 'Vừa', icon: '🌶️🌶️', color: 'bg-yellow-100 text-yellow-800' },
    { id: 3, name: 'Cay', icon: '🌶️🌶️🌶️', color: 'bg-orange-100 text-orange-800' },
    { id: 4, name: 'Rất Cay', icon: '🌶️🌶️🌶️🌶️', color: 'bg-red-100 text-red-800' },
    { id: 5, name: 'Siêu Cay', icon: '🔥🔥🔥', color: 'bg-red-200 text-red-900' }
  ];

  const features = [
    { id: 'fast_delivery', name: 'Giao Hàng Nhanh', icon: '⚡' },
    { id: 'free_delivery', name: 'Miễn Phí Ship', icon: '🚚' },
    { id: 'verified', name: 'Đã Xác Minh', icon: '✅' },
    { id: 'popular', name: 'Phổ Biến', icon: '🔥' },
    { id: 'new', name: 'Mới', icon: '🆕' },
    { id: 'signature', name: 'Đặc Sản', icon: '⭐' }
  ];

  const allergens = [
    { id: 'gluten', name: 'Gluten' },
    { id: 'dairy', name: 'Sữa' },
    { id: 'eggs', name: 'Trứng' },
    { id: 'nuts', name: 'Hạt' },
    { id: 'peanuts', name: 'Đậu Phộng' },
    { id: 'soy', name: 'Đậu Nành' },
    { id: 'fish', name: 'Cá' },
    { id: 'shellfish', name: 'Tôm Cua' }
  ];

  const handleFilterChange = (filterType: string, value: any) => {
    const newFilters = { ...filters };
    
    if (Array.isArray(newFilters[filterType])) {
      const currentArray = newFilters[filterType];
      if (currentArray.includes(value)) {
        newFilters[filterType] = currentArray.filter((item: any) => item !== value);
      } else {
        newFilters[filterType] = [...currentArray, value];
      }
    } else {
      newFilters[filterType] = value;
    }
    
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const clearAllFilters = () => {
    const clearedFilters = {
      cuisineTypes: [],
      dietaryRestrictions: [],
      priceRange: '',
      rating: 0,
      deliveryTime: 0,
      spicyLevel: 0,
      allergens: [],
      features: []
    };
    setFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.cuisineTypes.length > 0) count += filters.cuisineTypes.length;
    if (filters.dietaryRestrictions.length > 0) count += filters.dietaryRestrictions.length;
    if (filters.priceRange) count += 1;
    if (filters.rating > 0) count += 1;
    if (filters.deliveryTime > 0) count += 1;
    if (filters.spicyLevel > 0) count += 1;
    if (filters.allergens.length > 0) count += filters.allergens.length;
    if (filters.features.length > 0) count += filters.features.length;
    return count;
  };

  const activeCount = getActiveFiltersCount();

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
      >
        <Filter className="w-4 h-4" />
        <span>Bộ Lọc</span>
        {activeCount > 0 && (
          <span className="bg-orange-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
            {activeCount}
          </span>
        )}
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-2 w-96 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Bộ Lọc Nâng Cao</h3>
              <div className="flex items-center space-x-2">
                {activeCount > 0 && (
                  <button
                    onClick={clearAllFilters}
                    className="text-sm text-orange-600 hover:text-orange-800"
                  >
                    Xóa tất cả
                  </button>
                )}
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div className="space-y-6">
              {/* Cuisine Types */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">Loại Ẩm Thực</h4>
                <div className="grid grid-cols-2 gap-2">
                  {cuisineTypes.map((cuisine) => (
                    <button
                      key={cuisine.id}
                      onClick={() => handleFilterChange('cuisineTypes', cuisine.id)}
                      className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm ${
                        filters.cuisineTypes.includes(cuisine.id)
                          ? 'bg-orange-100 text-orange-800 border border-orange-300'
                          : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <span>{cuisine.icon}</span>
                      <span>{cuisine.name}</span>
                      {filters.cuisineTypes.includes(cuisine.id) && (
                        <Check className="w-4 h-4 ml-auto" />
                      )}
                    </button>
                  ))}
                </div>
              </div>

              {/* Dietary Restrictions */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">Chế Độ Ăn</h4>
                <div className="grid grid-cols-2 gap-2">
                  {dietaryRestrictions.map((diet) => (
                    <button
                      key={diet.id}
                      onClick={() => handleFilterChange('dietaryRestrictions', diet.id)}
                      className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm ${
                        filters.dietaryRestrictions.includes(diet.id)
                          ? 'bg-orange-100 text-orange-800 border border-orange-300'
                          : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <span>{diet.icon}</span>
                      <span>{diet.name}</span>
                      {filters.dietaryRestrictions.includes(diet.id) && (
                        <Check className="w-4 h-4 ml-auto" />
                      )}
                    </button>
                  ))}
                </div>
              </div>

              {/* Price Range */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">Khoảng Giá</h4>
                <div className="space-y-2">
                  {priceRanges.map((range) => (
                    <button
                      key={range.id}
                      onClick={() => handleFilterChange('priceRange', range.id)}
                      className={`w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm ${
                        filters.priceRange === range.id
                          ? 'bg-orange-100 text-orange-800 border border-orange-300'
                          : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <div className="flex items-center space-x-2">
                        <span>{range.icon}</span>
                        <div className="text-left">
                          <div className="font-medium">{range.name}</div>
                          <div className="text-xs text-gray-500">{range.description}</div>
                        </div>
                      </div>
                      {filters.priceRange === range.id && (
                        <Check className="w-4 h-4" />
                      )}
                    </button>
                  ))}
                </div>
              </div>

              {/* Rating */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">Đánh Giá Tối Thiểu</h4>
                <div className="flex space-x-2">
                  {[0, 3, 4, 4.5].map((rating) => (
                    <button
                      key={rating}
                      onClick={() => handleFilterChange('rating', rating)}
                      className={`flex items-center space-x-1 px-3 py-2 rounded-lg text-sm ${
                        filters.rating === rating
                          ? 'bg-orange-100 text-orange-800 border border-orange-300'
                          : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span>{rating === 0 ? 'Tất cả' : `${rating}+`}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Delivery Time */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">Thời Gian Giao Hàng</h4>
                <div className="flex space-x-2">
                  {[0, 15, 30, 45].map((time) => (
                    <button
                      key={time}
                      onClick={() => handleFilterChange('deliveryTime', time)}
                      className={`flex items-center space-x-1 px-3 py-2 rounded-lg text-sm ${
                        filters.deliveryTime === time
                          ? 'bg-orange-100 text-orange-800 border border-orange-300'
                          : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <Clock className="w-4 h-4" />
                      <span>{time === 0 ? 'Tất cả' : `≤ ${time} phút`}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Spicy Level */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">Mức Độ Cay</h4>
                <div className="grid grid-cols-3 gap-2">
                  {spicyLevels.map((level) => (
                    <button
                      key={level.id}
                      onClick={() => handleFilterChange('spicyLevel', level.id)}
                      className={`flex items-center space-x-1 px-2 py-2 rounded-lg text-xs ${
                        filters.spicyLevel === level.id
                          ? 'bg-orange-100 text-orange-800 border border-orange-300'
                          : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <span>{level.icon}</span>
                      <span>{level.name}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Features */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3">Tính Năng</h4>
                <div className="grid grid-cols-2 gap-2">
                  {features.map((feature) => (
                    <button
                      key={feature.id}
                      onClick={() => handleFilterChange('features', feature.id)}
                      className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm ${
                        filters.features.includes(feature.id)
                          ? 'bg-orange-100 text-orange-800 border border-orange-300'
                          : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <span>{feature.icon}</span>
                      <span>{feature.name}</span>
                      {filters.features.includes(feature.id) && (
                        <Check className="w-4 h-4 ml-auto" />
                      )}
                    </button>
                  ))}
                </div>
              </div>

              {/* Allergens to Avoid */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center space-x-2">
                  <AlertTriangle className="w-4 h-4 text-yellow-500" />
                  <span>Tránh Chất Gây Dị Ứng</span>
                </h4>
                <div className="grid grid-cols-2 gap-2">
                  {allergens.map((allergen) => (
                    <button
                      key={allergen.id}
                      onClick={() => handleFilterChange('allergens', allergen.id)}
                      className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm ${
                        filters.allergens.includes(allergen.id)
                          ? 'bg-red-100 text-red-800 border border-red-300'
                          : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <span>{allergen.name}</span>
                      {filters.allergens.includes(allergen.id) && (
                        <X className="w-4 h-4 ml-auto text-red-600" />
                      )}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FoodFilters;
