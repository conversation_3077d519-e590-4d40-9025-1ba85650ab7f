'use client';

import { useState, useEffect } from 'react';
import { 
  ArrowLeft, 
  Star, 
  Clock, 
  MapPin, 
  Phone, 
  Heart, 
  Share2,
  ShoppingCart,
  Plus,
  Minus,
  Filter,
  Search,
  ChefHat,
  Store,
  Info,
  CheckCircle,
  Truck
} from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';

interface Restaurant {
  id: string;
  name: string;
  description: string;
  cuisineType: string[];
  category: string;
  rating: number;
  totalReviews: number;
  priceRange: string;
  operatingHours: Record<string, { open: string; close: string; isOpen: boolean }>;
  deliveryInfo: {
    isDeliveryAvailable: boolean;
    deliveryFee: number;
    freeDeliveryThreshold: number;
    estimatedDeliveryTime: { min: number; max: number };
  };
  contact: {
    phone: string;
    address: { fullAddress: string };
  };
  specialFeatures: string[];
  isVerified: boolean;
  isFeatured: boolean;
}

interface MenuItem {
  id: string;
  name: string;
  description: string;
  category: string;
  subcategory: string;
  price: { basePrice: number; currency: string };
  variants: Array<{ id: string; name: string; price: number }>;
  images: Array<{ url: string; alt: string; isPrimary: boolean }>;
  preparationTime: { min: number; max: number };
  rating: number;
  totalReviews: number;
  isSignature: boolean;
  isPopular: boolean;
  tags: string[];
  availability: { isAvailable: boolean };
}

const RestaurantDetailPage = () => {
  const params = useParams();
  const restaurantId = params.id as string;
  
  const [restaurant, setRestaurant] = useState<Restaurant | null>(null);
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [cartItems, setCartItems] = useState<any[]>([]);
  const [isFavorite, setIsFavorite] = useState(false);

  const menuCategories = [
    { id: 'all', name: 'Tất Cả' },
    { id: 'main_course', name: 'Món Chính' },
    { id: 'appetizer', name: 'Khai Vị' },
    { id: 'soup', name: 'Canh & Súp' },
    { id: 'beverage', name: 'Đồ Uống' },
    { id: 'dessert', name: 'Tráng Miệng' }
  ];

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Mock data for development
        setRestaurant({
          id: restaurantId,
          name: 'Phở Hà Nội Truyền Thống',
          description: 'Phở Hà Nội chính hiệu với nước dùng niêu trong suốt 24 giờ. Được thành lập từ năm 1995, chúng tôi tự hào mang đến hương vị phở truyền thống đậm đà nhất.',
          cuisineType: ['vietnamese', 'noodles'],
          category: 'restaurant',
          rating: 4.8,
          totalReviews: 324,
          priceRange: 'budget',
          operatingHours: {
            monday: { open: '06:00', close: '22:00', isOpen: true },
            tuesday: { open: '06:00', close: '22:00', isOpen: true },
            wednesday: { open: '06:00', close: '22:00', isOpen: true },
            thursday: { open: '06:00', close: '22:00', isOpen: true },
            friday: { open: '06:00', close: '22:00', isOpen: true },
            saturday: { open: '06:00', close: '23:00', isOpen: true },
            sunday: { open: '06:00', close: '23:00', isOpen: true }
          },
          deliveryInfo: {
            isDeliveryAvailable: true,
            deliveryFee: 15000,
            freeDeliveryThreshold: 200000,
            estimatedDeliveryTime: { min: 20, max: 35 }
          },
          contact: {
            phone: '+84 24 3825 1234',
            address: { fullAddress: '123 Phố Hàng Bông, Hàng Bông, Hoàn Kiếm, Hà Nội' }
          },
          specialFeatures: ['halal_certified', 'vegetarian_options', 'late_night_delivery'],
          isVerified: true,
          isFeatured: true
        });

        setMenuItems([
          {
            id: 'menu-item-001',
            name: 'Phở Bò Tái',
            description: 'Phở bò với thịt bò tái, nước dùng trong suốt niêu 24 giờ, bánh phở tươi',
            category: 'main_course',
            subcategory: 'noodles',
            price: { basePrice: 65000, currency: 'VND' },
            variants: [
              { id: 'variant-001', name: 'Size Nhỏ', price: 65000 },
              { id: 'variant-002', name: 'Size Lớn', price: 85000 }
            ],
            images: [
              { url: '/images/menu/pho-bo-tai-1.jpg', alt: 'Phở Bò Tái', isPrimary: true }
            ],
            preparationTime: { min: 8, max: 12 },
            rating: 4.9,
            totalReviews: 156,
            isSignature: true,
            isPopular: true,
            tags: ['signature', 'traditional', 'halal', 'popular'],
            availability: { isAvailable: true }
          },
          {
            id: 'menu-item-002',
            name: 'Phở Gà',
            description: 'Phở gà với thịt gà luộc, nước dùng ngọt thanh',
            category: 'main_course',
            subcategory: 'noodles',
            price: { basePrice: 60000, currency: 'VND' },
            variants: [
              { id: 'variant-003', name: 'Size Nhỏ', price: 60000 },
              { id: 'variant-004', name: 'Size Lớn', price: 80000 }
            ],
            images: [
              { url: '/images/menu/pho-ga-1.jpg', alt: 'Phở Gà', isPrimary: true }
            ],
            preparationTime: { min: 8, max: 12 },
            rating: 4.7,
            totalReviews: 89,
            isSignature: false,
            isPopular: true,
            tags: ['traditional', 'halal', 'popular'],
            availability: { isAvailable: true }
          },
          {
            id: 'menu-item-003',
            name: 'Cà Phê Đen',
            description: 'Cà phê phin truyền thống, đậm đà hương vị',
            category: 'beverage',
            subcategory: 'coffee',
            price: { basePrice: 25000, currency: 'VND' },
            variants: [
              { id: 'variant-005', name: 'Nóng', price: 25000 },
              { id: 'variant-006', name: 'Đá', price: 25000 }
            ],
            images: [
              { url: '/images/menu/ca-phe-den-1.jpg', alt: 'Cà Phê Đen', isPrimary: true }
            ],
            preparationTime: { min: 3, max: 5 },
            rating: 4.5,
            totalReviews: 45,
            isSignature: false,
            isPopular: false,
            tags: ['traditional', 'coffee'],
            availability: { isAvailable: true }
          }
        ]);
      } catch (error) {
        console.error('Failed to fetch restaurant data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [restaurantId]);

  const addToCart = (item: MenuItem, variantId?: string) => {
    const selectedVariant = variantId ? item.variants.find(v => v.id === variantId) : item.variants[0];
    const cartItem = {
      ...item,
      selectedVariant,
      cartId: `${item.id}-${variantId || item.variants[0]?.id}`,
      quantity: 1
    };

    setCartItems(prev => {
      const existingItem = prev.find(cartItem => cartItem.cartId === cartItem.cartId);
      if (existingItem) {
        return prev.map(cartItem =>
          cartItem.cartId === cartItem.cartId
            ? { ...cartItem, quantity: cartItem.quantity + 1 }
            : cartItem
        );
      } else {
        return [...prev, cartItem];
      }
    });
  };

  const filteredMenuItems = menuItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    
    return matchesSearch && matchesCategory && item.availability.isAvailable;
  });

  const getCurrentStatus = () => {
    const now = new Date();
    const currentDay = now.toLocaleLowerCase().slice(0, 3) + (now.getDay() === 0 ? 'sunday' : 
                      now.getDay() === 1 ? 'monday' : 
                      now.getDay() === 2 ? 'tuesday' : 
                      now.getDay() === 3 ? 'wednesday' : 
                      now.getDay() === 4 ? 'thursday' : 
                      now.getDay() === 5 ? 'friday' : 'saturday');
    
    const todayHours = restaurant?.operatingHours[currentDay];
    if (!todayHours || !todayHours.isOpen) return { isOpen: false, text: 'Đóng cửa' };
    
    const currentTime = now.getHours() * 100 + now.getMinutes();
    const openTime = parseInt(todayHours.open.replace(':', ''));
    const closeTime = parseInt(todayHours.close.replace(':', ''));
    
    if (currentTime >= openTime && currentTime <= closeTime) {
      return { isOpen: true, text: `Mở cửa đến ${todayHours.close}` };
    } else {
      return { isOpen: false, text: `Mở cửa lúc ${todayHours.open}` };
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải dữ liệu...</p>
        </div>
      </div>
    );
  }

  if (!restaurant) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Store className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Không tìm thấy nhà hàng</h3>
          <p className="text-gray-500 mb-4">Nhà hàng bạn tìm kiếm không tồn tại hoặc đã bị xóa</p>
          <Link
            href="/booksy/apps/fullhouse/food"
            className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600"
          >
            Quay lại danh sách
          </Link>
        </div>
      </div>
    );
  }

  const status = getCurrentStatus();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/booksy/apps/fullhouse/food" className="text-gray-500 hover:text-gray-700">
                <ArrowLeft className="w-5 h-5" />
              </Link>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">{restaurant.name}</h1>
                <p className="text-sm text-gray-500">{restaurant.cuisineType.join(', ')}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setIsFavorite(!isFavorite)}
                className={`p-2 rounded-lg ${isFavorite ? 'text-red-500' : 'text-gray-400 hover:text-red-500'}`}
              >
                <Heart className={`w-5 h-5 ${isFavorite ? 'fill-current' : ''}`} />
              </button>
              <button className="p-2 rounded-lg text-gray-400 hover:text-gray-600">
                <Share2 className="w-5 h-5" />
              </button>
              <Link
                href="/booksy/apps/fullhouse/food/cart"
                className="bg-orange-500 text-white px-3 py-2 rounded-lg hover:bg-orange-600 flex items-center space-x-2 relative"
              >
                <ShoppingCart className="w-4 h-4" />
                <span>Giỏ hàng</span>
                {cartItems.length > 0 && (
                  <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {cartItems.reduce((sum, item) => sum + item.quantity, 0)}
                  </span>
                )}
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Restaurant Info */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <h1 className="text-2xl font-bold text-gray-900">{restaurant.name}</h1>
                {restaurant.isVerified && (
                  <span className="bg-green-100 text-green-800 text-sm px-2 py-1 rounded-full flex items-center space-x-1">
                    <CheckCircle className="w-4 h-4" />
                    <span>Đã xác minh</span>
                  </span>
                )}
              </div>
              <p className="text-gray-600 mb-4">{restaurant.description}</p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="flex items-center space-x-2">
                  <Star className="w-5 h-5 text-yellow-400 fill-current" />
                  <span className="font-medium">{restaurant.rating}</span>
                  <span className="text-gray-500">({restaurant.totalReviews} đánh giá)</span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Clock className="w-5 h-5 text-gray-400" />
                  <span className={`text-sm ${status.isOpen ? 'text-green-600' : 'text-red-600'}`}>
                    {status.text}
                  </span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Truck className="w-5 h-5 text-gray-400" />
                  <span className="text-sm text-gray-600">
                    {restaurant.deliveryInfo.estimatedDeliveryTime.min}-{restaurant.deliveryInfo.estimatedDeliveryTime.max} phút
                  </span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <MapPin className="w-5 h-5 text-gray-400" />
                  <span className="text-sm text-gray-600">
                    Phí ship: {restaurant.deliveryInfo.deliveryFee.toLocaleString()}đ
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Delivery Info */}
          <div className="bg-orange-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Truck className="w-5 h-5 text-orange-500" />
              <span className="font-medium text-orange-900">Thông tin giao hàng</span>
            </div>
            <div className="text-sm text-orange-800">
              <p>Phí giao hàng: {restaurant.deliveryInfo.deliveryFee.toLocaleString()}đ</p>
              <p>Miễn phí giao hàng cho đơn từ {restaurant.deliveryInfo.freeDeliveryThreshold.toLocaleString()}đ</p>
              <p>Thời gian giao hàng: {restaurant.deliveryInfo.estimatedDeliveryTime.min}-{restaurant.deliveryInfo.estimatedDeliveryTime.max} phút</p>
            </div>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div className="relative flex-1 md:mr-4">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Tìm kiếm món ăn..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              />
            </div>
            
            <div className="flex space-x-2 overflow-x-auto">
              {menuCategories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`px-4 py-2 rounded-lg whitespace-nowrap ${
                    selectedCategory === category.id
                      ? 'bg-orange-500 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {category.name}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Menu Items */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">
              Thực Đơn ({filteredMenuItems.length} món)
            </h2>
          </div>
          
          <div className="p-6">
            {filteredMenuItems.length === 0 ? (
              <div className="text-center py-12">
                <ChefHat className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Không tìm thấy món ăn</h3>
                <p className="text-gray-500">Thử thay đổi từ khóa tìm kiếm hoặc danh mục</p>
              </div>
            ) : (
              <div className="space-y-6">
                {filteredMenuItems.map((item) => (
                  <div key={item.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-start space-x-4">
                      <div className="w-24 h-24 bg-gradient-to-r from-orange-100 to-orange-200 rounded-lg flex items-center justify-center flex-shrink-0">
                        <ChefHat className="w-8 h-8 text-orange-400" />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <h3 className="font-semibold text-gray-900 flex items-center space-x-2">
                              <span>{item.name}</span>
                              {item.isSignature && (
                                <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded">
                                  Đặc sản
                                </span>
                              )}
                              {item.isPopular && (
                                <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded">
                                  Phổ biến
                                </span>
                              )}
                            </h3>
                            <p className="text-sm text-gray-600 mt-1 line-clamp-2">{item.description}</p>
                          </div>
                          <button className="text-gray-400 hover:text-red-500 ml-4">
                            <Heart className="w-5 h-5" />
                          </button>
                        </div>
                        
                        <div className="flex items-center space-x-4 mb-3 text-sm text-gray-500">
                          <div className="flex items-center space-x-1">
                            <Star className="w-4 h-4 text-yellow-400 fill-current" />
                            <span>{item.rating}</span>
                            <span>({item.totalReviews})</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Clock className="w-4 h-4" />
                            <span>{item.preparationTime.min}-{item.preparationTime.max} phút</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="text-lg font-bold text-orange-600">
                              {item.price.basePrice.toLocaleString('vi-VN')}đ
                            </div>
                            {item.variants.length > 1 && (
                              <div className="text-sm text-gray-500">
                                {item.variants.length} lựa chọn size
                              </div>
                            )}
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            {item.variants.length === 1 ? (
                              <button
                                onClick={() => addToCart(item, item.variants[0].id)}
                                className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 flex items-center space-x-1"
                              >
                                <Plus className="w-4 h-4" />
                                <span>Thêm</span>
                              </button>
                            ) : (
                              <select
                                onChange={(e) => {
                                  if (e.target.value) {
                                    addToCart(item, e.target.value);
                                    e.target.value = '';
                                  }
                                }}
                                className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 cursor-pointer"
                                defaultValue=""
                              >
                                <option value="" disabled>Chọn size</option>
                                {item.variants.map((variant) => (
                                  <option key={variant.id} value={variant.id} className="text-black">
                                    {variant.name} - {variant.price.toLocaleString()}đ
                                  </option>
                                ))}
                              </select>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RestaurantDetailPage;
