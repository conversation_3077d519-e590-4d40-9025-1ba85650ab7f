'use client';

import { useState, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  Star, 
  Clock, 
  MapPin, 
  ShoppingCart, 
  Plus, 
  Minus,
  Heart,
  ChefHat,
  Utensils,
  Coffee,
  Cake,
  Leaf,
  Zap,
  Store,
  ArrowLeft,
  SlidersHorizontal
} from 'lucide-react';
import Link from 'next/link';

interface Restaurant {
  id: string;
  name: string;
  description: string;
  cuisineType: string[];
  category: string;
  rating: number;
  totalReviews: number;
  priceRange: string;
  averageOrderValue: number;
  deliveryInfo: {
    deliveryFee: number;
    estimatedDeliveryTime: { min: number; max: number };
  };
  images: Array<{ url: string; alt: string; isPrimary: boolean }>;
  isVerified: boolean;
  isFeatured: boolean;
  tags: string[];
}

interface MenuItem {
  id: string;
  restaurantId: string;
  name: string;
  description: string;
  price: { basePrice: number; currency: string };
  images: Array<{ url: string; alt: string; isPrimary: boolean }>;
  rating: number;
  totalReviews: number;
  preparationTime: { min: number; max: number };
  isSignature: boolean;
  isPopular: boolean;
  tags: string[];
}

const FoodOrderingPage = () => {
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [featuredItems, setFeaturedItems] = useState<MenuItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedCuisine, setSelectedCuisine] = useState('all');
  const [priceRange, setPriceRange] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  const [cartItems, setCartItems] = useState<any[]>([]);

  const categories = [
    { id: 'all', name: 'Tất Cả', icon: Utensils },
    { id: 'restaurant', name: 'Nhà Hàng', icon: Store },
    { id: 'fast_food', name: 'Đồ Ăn Nhanh', icon: Zap },
    { id: 'coffee', name: 'Cà Phê', icon: Coffee },
    { id: 'dessert', name: 'Bánh Ngọt', icon: Cake },
    { id: 'healthy', name: 'Healthy', icon: Leaf }
  ];

  const cuisineTypes = [
    { id: 'all', name: 'Tất Cả' },
    { id: 'vietnamese', name: 'Món Việt' },
    { id: 'italian', name: 'Món Ý' },
    { id: 'chinese', name: 'Món Trung Hoa' },
    { id: 'japanese', name: 'Món Nhật' },
    { id: 'korean', name: 'Món Hàn' },
    { id: 'western', name: 'Món Âu' }
  ];

  const priceRanges = [
    { id: 'all', name: 'Tất Cả' },
    { id: 'budget', name: 'Bình Dân (< 100k)' },
    { id: 'mid_range', name: 'Trung Bình (100k - 300k)' },
    { id: 'premium', name: 'Cao Cấp (> 300k)' }
  ];

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Mock data for development
        setRestaurants([
          {
            id: 'restaurant-001',
            name: 'Phở Hà Nội Truyền Thống',
            description: 'Phở Hà Nội chính hiệu với nước dùng niêu trong suốt 24 giờ',
            cuisineType: ['vietnamese'],
            category: 'restaurant',
            rating: 4.8,
            totalReviews: 324,
            priceRange: 'budget',
            averageOrderValue: 85000,
            deliveryInfo: {
              deliveryFee: 15000,
              estimatedDeliveryTime: { min: 20, max: 35 }
            },
            images: [
              { url: '/images/restaurants/pho-ha-noi/storefront.jpg', alt: 'Phở Hà Nội', isPrimary: true }
            ],
            isVerified: true,
            isFeatured: true,
            tags: ['signature', 'traditional', 'halal', 'popular']
          },
          {
            id: 'restaurant-002',
            name: 'Pizza Italia Express',
            description: 'Pizza Ý chính hiệu với đế bánh mỏng giòn',
            cuisineType: ['italian'],
            category: 'fast_food',
            rating: 4.6,
            totalReviews: 189,
            priceRange: 'mid_range',
            averageOrderValue: 245000,
            deliveryInfo: {
              deliveryFee: 20000,
              estimatedDeliveryTime: { min: 25, max: 40 }
            },
            images: [
              { url: '/images/restaurants/pizza-italia/storefront.jpg', alt: 'Pizza Italia', isPrimary: true }
            ],
            isVerified: true,
            isFeatured: false,
            tags: ['pizza', 'italian', 'fast_food', 'vegetarian']
          }
        ]);

        setFeaturedItems([
          {
            id: 'menu-item-001',
            restaurantId: 'restaurant-001',
            name: 'Phở Bò Tái',
            description: 'Phở bò với thịt bò tái, nước dùng trong suốt niêu 24 giờ',
            price: { basePrice: 65000, currency: 'VND' },
            images: [
              { url: '/images/menu/pho-bo-tai-1.jpg', alt: 'Phở Bò Tái', isPrimary: true }
            ],
            rating: 4.9,
            totalReviews: 156,
            preparationTime: { min: 8, max: 12 },
            isSignature: true,
            isPopular: true,
            tags: ['signature', 'traditional', 'halal', 'popular']
          },
          {
            id: 'menu-item-002',
            restaurantId: 'restaurant-002',
            name: 'Pizza Margherita',
            description: 'Pizza cổ điển với sốt cà chua, phô mai mozzarella tươi',
            price: { basePrice: 189000, currency: 'VND' },
            images: [
              { url: '/images/menu/pizza-margherita-1.jpg', alt: 'Pizza Margherita', isPrimary: true }
            ],
            rating: 4.7,
            totalReviews: 89,
            preparationTime: { min: 12, max: 18 },
            isSignature: false,
            isPopular: true,
            tags: ['classic', 'vegetarian', 'popular', 'italian']
          }
        ]);
      } catch (error) {
        console.error('Failed to fetch food data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const addToCart = (item: MenuItem) => {
    setCartItems(prev => {
      const existingItem = prev.find(cartItem => cartItem.id === item.id);
      if (existingItem) {
        return prev.map(cartItem =>
          cartItem.id === item.id
            ? { ...cartItem, quantity: cartItem.quantity + 1 }
            : cartItem
        );
      } else {
        return [...prev, { ...item, quantity: 1 }];
      }
    });
  };

  const getPriceRangeIcon = (range: string) => {
    switch (range) {
      case 'budget':
        return '💰';
      case 'mid_range':
        return '💰💰';
      case 'premium':
        return '💰💰💰';
      default:
        return '💰';
    }
  };

  const filteredRestaurants = restaurants.filter(restaurant => {
    const matchesSearch = restaurant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         restaurant.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || restaurant.category === selectedCategory;
    const matchesCuisine = selectedCuisine === 'all' || restaurant.cuisineType.includes(selectedCuisine);
    const matchesPrice = priceRange === 'all' || restaurant.priceRange === priceRange;
    
    return matchesSearch && matchesCategory && matchesCuisine && matchesPrice;
  });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải dữ liệu...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/booksy/apps/fullhouse" className="text-gray-500 hover:text-gray-700">
                <ArrowLeft className="w-5 h-5" />
              </Link>
              <ChefHat className="w-6 h-6 text-orange-500" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">Đặt Đồ Ăn</h1>
                <p className="text-sm text-gray-500">Khám phá ẩm thực Times City</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="bg-gray-100 text-gray-700 px-3 py-2 rounded-lg hover:bg-gray-200 flex items-center space-x-2"
              >
                <SlidersHorizontal className="w-4 h-4" />
                <span>Lọc</span>
              </button>
              <Link
                href="/booksy/apps/fullhouse/food/cart"
                className="bg-orange-500 text-white px-3 py-2 rounded-lg hover:bg-orange-600 flex items-center space-x-2 relative"
              >
                <ShoppingCart className="w-4 h-4" />
                <span>Giỏ hàng</span>
                {cartItems.length > 0 && (
                  <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {cartItems.reduce((sum, item) => sum + item.quantity, 0)}
                  </span>
                )}
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Bar */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Tìm kiếm nhà hàng, món ăn..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />
          </div>
        </div>

        {/* Category Filters */}
        <div className="mb-6">
          <div className="flex space-x-2 overflow-x-auto pb-2">
            {categories.map((category) => {
              const IconComponent = category.icon;
              return (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-full whitespace-nowrap ${
                    selectedCategory === category.id
                      ? 'bg-orange-500 text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <IconComponent className="w-4 h-4" />
                  <span>{category.name}</span>
                </button>
              );
            })}
          </div>
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Bộ Lọc Nâng Cao</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Loại ẩm thực
                </label>
                <select
                  value={selectedCuisine}
                  onChange={(e) => setSelectedCuisine(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                >
                  {cuisineTypes.map(cuisine => (
                    <option key={cuisine.id} value={cuisine.id}>{cuisine.name}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Khoảng giá
                </label>
                <select
                  value={priceRange}
                  onChange={(e) => setPriceRange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                >
                  {priceRanges.map(range => (
                    <option key={range.id} value={range.id}>{range.name}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        )}

        {/* Featured Items */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Món Nổi Bật</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredItems.map((item) => (
              <div key={item.id} className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                <div className="aspect-w-16 aspect-h-9 bg-gray-200">
                  <div className="w-full h-48 bg-gradient-to-r from-orange-100 to-orange-200 flex items-center justify-center">
                    <ChefHat className="w-12 h-12 text-orange-400" />
                  </div>
                </div>
                <div className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold text-gray-900">{item.name}</h3>
                    <button className="text-gray-400 hover:text-red-500">
                      <Heart className="w-5 h-5" />
                    </button>
                  </div>
                  <p className="text-sm text-gray-600 mb-3 line-clamp-2">{item.description}</p>
                  
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="text-sm font-medium">{item.rating}</span>
                      <span className="text-sm text-gray-500">({item.totalReviews})</span>
                    </div>
                    <div className="flex items-center space-x-1 text-sm text-gray-500">
                      <Clock className="w-4 h-4" />
                      <span>{item.preparationTime.min}-{item.preparationTime.max} phút</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="text-lg font-bold text-orange-600">
                      {item.price.basePrice.toLocaleString('vi-VN')}đ
                    </div>
                    <button
                      onClick={() => addToCart(item)}
                      className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 flex items-center space-x-1"
                    >
                      <Plus className="w-4 h-4" />
                      <span>Thêm</span>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Restaurant List */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Nhà Hàng ({filteredRestaurants.length})
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredRestaurants.map((restaurant) => (
              <Link
                key={restaurant.id}
                href={`/booksy/apps/fullhouse/food/restaurant/${restaurant.id}`}
                className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow"
              >
                <div className="aspect-w-16 aspect-h-9 bg-gray-200">
                  <div className="w-full h-48 bg-gradient-to-r from-blue-100 to-blue-200 flex items-center justify-center">
                    <Store className="w-12 h-12 text-blue-400" />
                  </div>
                </div>
                <div className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold text-gray-900">{restaurant.name}</h3>
                    {restaurant.isVerified && (
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                        Đã xác minh
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mb-3 line-clamp-2">{restaurant.description}</p>
                  
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="text-sm font-medium">{restaurant.rating}</span>
                      <span className="text-sm text-gray-500">({restaurant.totalReviews})</span>
                    </div>
                    <div className="text-sm text-gray-500">
                      {getPriceRangeIcon(restaurant.priceRange)}
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>{restaurant.deliveryInfo.estimatedDeliveryTime.min}-{restaurant.deliveryInfo.estimatedDeliveryTime.max} phút</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <MapPin className="w-4 h-4" />
                      <span>Phí ship: {restaurant.deliveryInfo.deliveryFee.toLocaleString()}đ</span>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-1 mt-3">
                    {restaurant.tags.slice(0, 3).map((tag) => (
                      <span key={tag} className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FoodOrderingPage;
