# Rủ nhau - Group Activities Feature

## Overview

"Rủ nhau" is a dynamic, location-based social feature for the Full House app that allows residents to organize spontaneous activities and connect with neighbors. The system is designed for house-to-house proximity matching with automatic cleanup to maintain privacy and relevance.

## Key Features

### 🏠 **House-to-House Proximity**
- Location-based matching using apartment numbers and building coordinates
- Distance calculation between apartments within the same building
- Cross-building distance calculation for nearby complexes
- Radius-based activity discovery (default 500m-1km)

### ⚡ **Dynamic Auto-Cleanup**
- All activities auto-delete after 30 minutes of inactivity
- Chat messages auto-delete after 30 minutes
- Groups automatically dissolve if only creator remains for 30+ minutes
- Background cleanup processes run every 5 minutes

### 🎯 **Activity Categories**
- **Thể <PERSON>hao**: Pickleball, Tennis, Ch<PERSON>y <PERSON>, <PERSON><PERSON><PERSON>, Bơi Lộ<PERSON>
- **Mua Sắm**: <PERSON><PERSON>, <PERSON><PERSON>, Chợ Truyền Thống
- **Làm Đẹp**: G<PERSON><PERSON>/<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>óc Da
- **Giao <PERSON>**: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Board Games
- **Ăn Uống**: <PERSON><PERSON> Ăn, Ăn Vặt, <PERSON><PERSON><PERSON> Ăn
- **Giải Trí**: <PERSON>em <PERSON>m, <PERSON>ơ<PERSON> Game, <PERSON><PERSON>c

### 💬 **Dual Chat System**
- **Group Chat**: Private chat within each activity group
- **Global Chat**: Area-wide chat for residents within 1km of Times City
- Real-time messaging with reactions and replies
- Auto-delete messages after 30 minutes

### 🌍 **Global Chat Access**
- Available to residents within 1km radius of Times City
- Supports multiple buildings: Times City, Royal City, Landmark 72, Lotte Center, Vincom Mega Mall
- Moderation rules and spam protection
- Building-based user identification

## Technical Architecture

### API Endpoints

#### Activities Management
- `GET /api/booksy/fullhouse/ru-nhau/activities` - Fetch nearby activities
- `POST /api/booksy/fullhouse/ru-nhau/activities` - Create new activity
- `PUT /api/booksy/fullhouse/ru-nhau/activities` - Join/leave/update activity
- `DELETE /api/booksy/fullhouse/ru-nhau/activities` - Delete activity

#### Chat System
- `GET /api/booksy/fullhouse/ru-nhau/chat` - Fetch chat messages
- `POST /api/booksy/fullhouse/ru-nhau/chat` - Send message
- `PUT /api/booksy/fullhouse/ru-nhau/chat` - React/edit/delete message

#### Location Services
- `GET /api/booksy/fullhouse/ru-nhau/location` - Find nearby users/activities
- `POST /api/booksy/fullhouse/ru-nhau/location` - Update user location
- `PUT /api/booksy/fullhouse/ru-nhau/location` - Update activity status
- `DELETE /api/booksy/fullhouse/ru-nhau/location` - Remove from tracking

#### Global Chat
- `GET /api/booksy/fullhouse/ru-nhau/global-chat` - Access global chat
- `POST /api/booksy/fullhouse/ru-nhau/global-chat` - Send global message
- `PUT /api/booksy/fullhouse/ru-nhau/global-chat` - React to global message
- `DELETE /api/booksy/fullhouse/ru-nhau/global-chat` - Leave global chat

#### Auto-Cleanup
- `GET /api/booksy/fullhouse/ru-nhau/cleanup` - Get cleanup status
- `POST /api/booksy/fullhouse/ru-nhau/cleanup` - Trigger manual cleanup
- `PUT /api/booksy/fullhouse/ru-nhau/cleanup` - Update cleanup settings
- `DELETE /api/booksy/fullhouse/ru-nhau/cleanup` - Force delete expired data

### Data Models

#### Activity
```typescript
interface Activity {
  id: string;
  title: string;
  description: string;
  category: string;
  activityType: string;
  creatorId: string;
  creatorInfo: UserInfo;
  location: LocationInfo;
  maxMembers: number;
  currentMembers: number;
  members: Member[];
  scheduledTime: string;
  duration: number;
  status: 'active' | 'inactive';
  tags: string[];
  requirements: string[];
  chatId: string;
  lastActivity: string;
  autoDeleteAt: string;
  createdAt: string;
  updatedAt: string;
}
```

#### Chat Message
```typescript
interface ChatMessage {
  id: string;
  chatId: string;
  activityId?: string;
  senderId: string;
  senderInfo: UserInfo;
  message: string;
  messageType: 'text' | 'emoji';
  timestamp: string;
  autoDeleteAt: string;
  isDeleted: boolean;
  reactions?: Reaction[];
  replyTo?: string;
}
```

#### User Location
```typescript
interface UserLocation {
  userId: string;
  apartment: string;
  building: string;
  lastSeen: string;
  isActive: boolean;
}
```

### Components

#### Main Interface
- `page.tsx` - Main "Rủ nhau" interface with tabs and activity management
- `ActivityCategories.tsx` - Category selection and activity templates
- `CreateActivityForm.tsx` - Form for creating new activities

#### Chat Components
- `GroupChat.tsx` - Private group chat within activities
- `GlobalChat.tsx` - Area-wide global chat system

### Data Storage

All data is stored in JSON files under `/data/apps/booksy/fullhouse/ru-nhau/`:
- `activities.json` - Active activities and groups
- `chat-messages.json` - Chat messages for all groups
- `activity-categories.json` - Activity types and templates
- `user-locations.json` - User location tracking
- `global-chat.json` - Global chat messages and active users

## Usage Examples

### Creating an Activity
1. Navigate to "Rủ nhau" from Full House quick actions
2. Select "Tạo mới" tab
3. Choose activity category (e.g., "Thể Thao")
4. Select activity type (e.g., "Pickleball")
5. Use template or create custom activity
6. Fill in details: time, location, max members, requirements
7. Submit to create the activity

### Joining an Activity
1. Browse "Gần đây" tab for nearby activities
2. View activity details and member count
3. Click "Tham gia" if space available
4. Access group chat to coordinate with members

### Global Chat Access
1. Click "Chat Toàn Khu" button
2. System checks location proximity (within 1km of Times City)
3. If approved, join area-wide chat with other residents
4. Messages auto-delete after 30 minutes

## Privacy & Safety

### Auto-Cleanup System
- **Activities**: Auto-delete after 30 minutes or when empty
- **Messages**: Auto-delete after 30 minutes
- **Location Data**: Clean up inactive users (1+ hour offline)
- **Background Process**: Runs every 5 minutes

### Location Privacy
- Only apartment number and building shared
- No precise GPS coordinates stored
- Distance calculations use building-level coordinates
- Users can go offline to stop location sharing

### Moderation
- Global chat has moderation rules
- Spam protection (max 3 messages per minute)
- Message length limits (500 characters)
- User reporting capabilities

## Integration

### Full House Integration
- Added as quick action in Full House main page
- Accessible via orange "Rủ nhau" button
- Integrated with existing user authentication
- Uses consistent UI/UX patterns

### Navigation
- Main route: `/booksy/apps/fullhouse/ru-nhau`
- Quick action ID: `ru-nhau`
- Icon: Zap (⚡) representing dynamic activities

## Future Enhancements

### Planned Features
- Push notifications for activity invites
- Activity history and statistics
- User reputation system
- Photo sharing in group chats
- Integration with calendar apps
- Weather-based activity suggestions

### Technical Improvements
- Real-time WebSocket connections
- Advanced location services
- Machine learning for activity recommendations
- Enhanced moderation tools
- Analytics dashboard

## Development Notes

### Testing
- Use mock data for development
- Test auto-cleanup functionality
- Verify location-based filtering
- Check chat message persistence
- Validate privacy controls

### Deployment
- Ensure data directories exist
- Configure cleanup intervals
- Set up monitoring for auto-cleanup
- Test cross-building distance calculations
- Verify global chat access controls

---

**Note**: This feature is designed specifically for residential communities and emphasizes privacy, spontaneity, and neighbor connections while maintaining appropriate boundaries through automatic cleanup and location-based access controls.
