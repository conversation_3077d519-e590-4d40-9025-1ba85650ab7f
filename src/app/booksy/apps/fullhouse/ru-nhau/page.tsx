'use client';

import { useState, useEffect } from 'react';
import { 
  Users, 
  Plus, 
  Globe, 
  MapPin, 
  Clock, 
  MessageCircle,
  Filter,
  Search,
  RefreshCw,
  ArrowLeft,
  Zap,
  Heart,
  Star
} from 'lucide-react';
import Link from 'next/link';
import ActivityCategories from './components/ActivityCategories';
import CreateActivityForm from './components/CreateActivityForm';
import GroupChat from './components/GroupChat';
import GlobalChat from './components/GlobalChat';

interface Activity {
  id: string;
  title: string;
  description: string;
  category: string;
  activityType: string;
  creatorInfo: {
    name: string;
    avatar: string;
    apartment: string;
    building: string;
  };
  location: {
    building: string;
    area: string;
  };
  maxMembers: number;
  currentMembers: number;
  members: any[];
  scheduledTime: string;
  duration: number;
  status: string;
  tags: string[];
  distance?: number;
  chatId: string;
  lastActivity: string;
  autoDeleteAt: string;
}

const RuNhauPage = () => {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [filteredActivities, setFilteredActivities] = useState<Activity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'nearby' | 'categories' | 'my-activities'>('nearby');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showGlobalChat, setShowGlobalChat] = useState(false);
  const [selectedActivity, setSelectedActivity] = useState<Activity | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [prefilledData, setPrefilledData] = useState<any>(null);

  // Mock current user data
  const currentUser = {
    id: 'user-current',
    name: 'Người dùng hiện tại',
    avatar: '/images/users/current-user.jpg',
    apartment: 'T1-1205',
    building: 'Times City'
  };

  useEffect(() => {
    fetchActivities();
  }, []);

  useEffect(() => {
    filterActivities();
  }, [activities, searchQuery, categoryFilter]);

  const fetchActivities = async () => {
    try {
      setIsLoading(true);
      
      // Mock location for Times City
      const userLat = 20.9967;
      const userLon = 105.8686;
      const radius = 1000; // 1km
      
      const response = await fetch(
        `/api/booksy/fullhouse/ru-nhau/activities?lat=${userLat}&lon=${userLon}&radius=${radius}&userId=${currentUser.id}`
      );
      
      if (response.ok) {
        const data = await response.json();
        setActivities(data);
      }
    } catch (error) {
      console.error('Failed to fetch activities:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterActivities = () => {
    let filtered = activities;

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(activity =>
        activity.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        activity.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        activity.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Filter by category
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(activity => activity.category === categoryFilter);
    }

    setFilteredActivities(filtered);
  };

  const handleCreateActivity = (category: string, activityType: string, template?: string) => {
    setPrefilledData({ category, activityType, template });
    setShowCreateForm(true);
  };

  const handleSubmitActivity = async (activityData: any) => {
    try {
      const response = await fetch('/api/booksy/fullhouse/ru-nhau/activities', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(activityData),
      });

      if (response.ok) {
        await fetchActivities();
        setShowCreateForm(false);
        setPrefilledData(null);
      }
    } catch (error) {
      console.error('Failed to create activity:', error);
    }
  };

  const handleJoinActivity = async (activityId: string) => {
    try {
      const response = await fetch('/api/booksy/fullhouse/ru-nhau/activities', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          activityId,
          action: 'join',
          userId: currentUser.id,
          userInfo: {
            name: currentUser.name,
            avatar: currentUser.avatar,
            apartment: currentUser.apartment
          }
        }),
      });

      if (response.ok) {
        await fetchActivities();
      }
    } catch (error) {
      console.error('Failed to join activity:', error);
    }
  };

  const handleLeaveActivity = async (activityId: string) => {
    try {
      const response = await fetch('/api/booksy/fullhouse/ru-nhau/activities', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          activityId,
          action: 'leave',
          userId: currentUser.id,
          userInfo: {
            name: currentUser.name,
            avatar: currentUser.avatar,
            apartment: currentUser.apartment
          }
        }),
      });

      if (response.ok) {
        await fetchActivities();
      }
    } catch (error) {
      console.error('Failed to leave activity:', error);
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('vi-VN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Hôm nay';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Ngày mai';
    } else {
      return date.toLocaleDateString('vi-VN');
    }
  };

  const getTimeUntilDelete = (autoDeleteAt: string) => {
    const deleteTime = new Date(autoDeleteAt);
    const now = new Date();
    const diff = deleteTime.getTime() - now.getTime();
    
    if (diff <= 0) return 'Đã hết hạn';
    
    const minutes = Math.floor(diff / (1000 * 60));
    return `${minutes} phút`;
  };

  const isUserJoined = (activity: Activity) => {
    return activity.members.some(member => member.userId === currentUser.id);
  };

  const categories = [
    { id: 'all', name: 'Tất cả', icon: '🌟' },
    { id: 'sports', name: 'Thể Thao', icon: '🏃‍♂️' },
    { id: 'shopping', name: 'Mua Sắm', icon: '🛒' },
    { id: 'beauty', name: 'Làm Đẹp', icon: '💅' },
    { id: 'social', name: 'Giao Lưu', icon: '💬' },
    { id: 'food', name: 'Ăn Uống', icon: '🍽️' },
    { id: 'entertainment', name: 'Giải Trí', icon: '🎮' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/booksy/apps/fullhouse" className="text-gray-500 hover:text-gray-700">
                <ArrowLeft className="w-5 h-5" />
              </Link>
              <div className="flex items-center space-x-2">
                <Zap className="w-6 h-6 text-orange-500" />
                <div>
                  <h1 className="text-xl font-semibold text-gray-900">Rủ nhau</h1>
                  <p className="text-sm text-gray-500">Tìm bạn cùng hoạt động</p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setShowGlobalChat(true)}
                className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 flex items-center space-x-2"
              >
                <Globe className="w-4 h-4" />
                <span>Chat Toàn Khu</span>
              </button>
              <button
                onClick={fetchActivities}
                className="bg-gray-100 text-gray-700 p-2 rounded-lg hover:bg-gray-200"
              >
                <RefreshCw className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tabs */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6">
          <button
            onClick={() => setActiveTab('nearby')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'nearby'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Gần đây
          </button>
          <button
            onClick={() => setActiveTab('categories')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'categories'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Tạo mới
          </button>
          <button
            onClick={() => setActiveTab('my-activities')}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'my-activities'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Của tôi
          </button>
        </div>

        {/* Content */}
        {activeTab === 'nearby' && (
          <div className="space-y-6">
            {/* Search and Filters */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Tìm kiếm hoạt động..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                />
              </div>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                {categories.map(cat => (
                  <option key={cat.id} value={cat.id}>
                    {cat.icon} {cat.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Activities List */}
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
              </div>
            ) : filteredActivities.length === 0 ? (
              <div className="text-center py-12">
                <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Chưa có hoạt động nào</h3>
                <p className="text-gray-500 mb-4">Hãy tạo hoạt động đầu tiên để mọi người cùng tham gia!</p>
                <button
                  onClick={() => setActiveTab('categories')}
                  className="bg-orange-500 text-white px-6 py-3 rounded-lg hover:bg-orange-600 flex items-center space-x-2 mx-auto"
                >
                  <Plus className="w-5 h-5" />
                  <span>Tạo hoạt động</span>
                </button>
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-6">
                {filteredActivities.map((activity) => {
                  const joined = isUserJoined(activity);
                  
                  return (
                    <div key={activity.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900">{activity.title}</h3>
                            <span className="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                              {categories.find(c => c.id === activity.category)?.name}
                            </span>
                          </div>
                          <p className="text-gray-600 mb-3">{activity.description}</p>
                          
                          <div className="flex flex-wrap gap-4 text-sm text-gray-500 mb-4">
                            <div className="flex items-center space-x-1">
                              <MapPin className="w-4 h-4" />
                              <span>{activity.location.building} - {activity.location.area}</span>
                              {activity.distance && (
                                <span className="text-orange-600">({activity.distance}m)</span>
                              )}
                            </div>
                            <div className="flex items-center space-x-1">
                              <Clock className="w-4 h-4" />
                              <span>{formatDate(activity.scheduledTime)} {formatTime(activity.scheduledTime)}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Users className="w-4 h-4" />
                              <span>{activity.currentMembers}/{activity.maxMembers} người</span>
                            </div>
                          </div>

                          <div className="flex items-center space-x-2 mb-4">
                            <img
                              src={activity.creatorInfo.avatar}
                              alt={activity.creatorInfo.name}
                              className="w-8 h-8 rounded-full"
                            />
                            <div>
                              <p className="text-sm font-medium text-gray-900">{activity.creatorInfo.name}</p>
                              <p className="text-xs text-gray-500">{activity.creatorInfo.apartment}</p>
                            </div>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="text-xs text-gray-500">
                              Tự xóa sau: {getTimeUntilDelete(activity.autoDeleteAt)}
                            </div>
                            <div className="flex items-center space-x-2">
                              <button
                                onClick={() => setSelectedActivity(activity)}
                                className="bg-gray-100 text-gray-700 px-3 py-1 rounded hover:bg-gray-200 flex items-center space-x-1"
                              >
                                <MessageCircle className="w-4 h-4" />
                                <span>Chat</span>
                              </button>
                              
                              {joined ? (
                                <button
                                  onClick={() => handleLeaveActivity(activity.id)}
                                  className="bg-red-500 text-white px-4 py-1 rounded hover:bg-red-600"
                                >
                                  Rời khỏi
                                </button>
                              ) : activity.currentMembers < activity.maxMembers ? (
                                <button
                                  onClick={() => handleJoinActivity(activity.id)}
                                  className="bg-orange-500 text-white px-4 py-1 rounded hover:bg-orange-600"
                                >
                                  Tham gia
                                </button>
                              ) : (
                                <span className="text-gray-500 text-sm">Đã đầy</span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        )}

        {activeTab === 'categories' && (
          <ActivityCategories
            onSelectActivity={handleCreateActivity}
            onCreateCustom={() => setShowCreateForm(true)}
          />
        )}

        {activeTab === 'my-activities' && (
          <div className="space-y-6">
            <div className="text-center py-12">
              <Heart className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Hoạt động của bạn</h3>
              <p className="text-gray-500">Các hoạt động bạn đã tạo hoặc tham gia sẽ hiển thị ở đây</p>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      <CreateActivityForm
        isOpen={showCreateForm}
        onClose={() => {
          setShowCreateForm(false);
          setPrefilledData(null);
        }}
        onSubmit={handleSubmitActivity}
        prefilledData={prefilledData}
      />

      <GlobalChat
        currentUserId={currentUser.id}
        currentUserInfo={currentUser}
        isOpen={showGlobalChat}
        onClose={() => setShowGlobalChat(false)}
      />

      {selectedActivity && (
        <GroupChat
          activityId={selectedActivity.id}
          chatId={selectedActivity.chatId}
          currentUserId={currentUser.id}
          currentUserInfo={{
            name: currentUser.name,
            avatar: currentUser.avatar,
            apartment: currentUser.apartment
          }}
          isOpen={!!selectedActivity}
          onClose={() => setSelectedActivity(null)}
        />
      )}
    </div>
  );
};

export default RuNhauPage;
