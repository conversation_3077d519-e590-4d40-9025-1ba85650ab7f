'use client';

import { useState, useEffect } from 'react';
import { 
  Plus, 
  Users, 
  Clock, 
  MapPin,
  ChevronRight,
  Search,
  Filter
} from 'lucide-react';

interface ActivityType {
  id: string;
  name: string;
  description: string;
  icon: string;
  suggestedDuration: number;
  maxMembers: number;
  requirements: string[];
  templates: string[];
}

interface Category {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  activities: ActivityType[];
}

interface ActivityCategoriesProps {
  onSelectActivity: (category: string, activityType: string, template?: string) => void;
  onCreateCustom: () => void;
}

const ActivityCategories = ({ onSelectActivity, onCreateCustom }: ActivityCategoriesProps) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        // Mock data for development - in real app would fetch from API
        const mockCategories: Category[] = [
          {
            id: 'sports',
            name: 'Thể Thao',
            description: 'Các hoạt động thể thao và vận động',
            icon: '🏃‍♂️',
            color: 'bg-blue-100 text-blue-800',
            activities: [
              {
                id: 'pickleball',
                name: 'Pickleball',
                description: 'Chơi pickleball cùng nhau',
                icon: '🏓',
                suggestedDuration: 120,
                maxMembers: 4,
                requirements: ['Vợt pickleball', 'Giày thể thao', 'Nước uống'],
                templates: [
                  'Ai đi pickleball không?',
                  'Tìm người chơi pickleball',
                  'Pickleball buổi chiều không?'
                ]
              },
              {
                id: 'tennis',
                name: 'Tennis',
                description: 'Chơi tennis cùng nhau',
                icon: '🎾',
                suggestedDuration: 90,
                maxMembers: 4,
                requirements: ['Vợt tennis', 'Giày tennis', 'Khăn lau mồ hôi'],
                templates: [
                  'Tennis không?',
                  'Ai chơi tennis cùng?',
                  'Đi đánh tennis không?'
                ]
              },
              {
                id: 'jogging',
                name: 'Chạy Bộ',
                description: 'Chạy bộ cùng nhau',
                icon: '🏃',
                suggestedDuration: 60,
                maxMembers: 8,
                requirements: ['Giày chạy bộ', 'Quần áo thể thao', 'Nước uống'],
                templates: [
                  'Đi chạy bộ không?',
                  'Chạy bộ buổi sáng không?',
                  'Ai chạy bộ cùng?'
                ]
              },
              {
                id: 'badminton',
                name: 'Cầu Lông',
                description: 'Chơi cầu lông cùng nhau',
                icon: '🏸',
                suggestedDuration: 90,
                maxMembers: 4,
                requirements: ['Vợt cầu lông', 'Giày thể thao', 'Khăn'],
                templates: [
                  'Cầu lông không?',
                  'Ai đánh cầu lông?',
                  'Đi chơi cầu lông không?'
                ]
              }
            ]
          },
          {
            id: 'shopping',
            name: 'Mua Sắm',
            description: 'Đi mua sắm cùng nhau',
            icon: '🛒',
            color: 'bg-green-100 text-green-800',
            activities: [
              {
                id: 'grocery_shopping',
                name: 'Đi Chợ',
                description: 'Đi chợ mua thực phẩm',
                icon: '🥬',
                suggestedDuration: 90,
                maxMembers: 4,
                requirements: ['Túi đựng đồ', 'Tiền mặt/thẻ'],
                templates: [
                  'Đi chợ không?',
                  'Ai đi chợ cùng?',
                  'Đi mua thực phẩm không?'
                ]
              },
              {
                id: 'mall_shopping',
                name: 'Đi Mall',
                description: 'Đi shopping mall',
                icon: '🏬',
                suggestedDuration: 180,
                maxMembers: 5,
                requirements: ['Túi xách', 'Thẻ/tiền'],
                templates: [
                  'Đi mall không?',
                  'Shopping cùng nhau không?',
                  'Ai đi Vincom?'
                ]
              }
            ]
          },
          {
            id: 'beauty',
            name: 'Làm Đẹp',
            description: 'Các hoạt động làm đẹp và chăm sóc',
            icon: '💅',
            color: 'bg-pink-100 text-pink-800',
            activities: [
              {
                id: 'hair_salon',
                name: 'Gội Đầu/Spa',
                description: 'Đi spa gội đầu massage',
                icon: '💆‍♀️',
                suggestedDuration: 180,
                maxMembers: 4,
                requirements: ['Khăn riêng', 'Tiền đặt cọc'],
                templates: [
                  'Đi gội đầu không?',
                  'Spa massage không?',
                  'Ai đi làm tóc cùng?'
                ]
              },
              {
                id: 'nail_salon',
                name: 'Làm Nail',
                description: 'Đi làm nail cùng nhau',
                icon: '💅',
                suggestedDuration: 120,
                maxMembers: 3,
                requirements: ['Book trước', 'Mẫu nail'],
                templates: [
                  'Làm nail không?',
                  'Ai đi làm nail?',
                  'Nail salon cùng nhau?'
                ]
              }
            ]
          },
          {
            id: 'social',
            name: 'Giao Lưu',
            description: 'Các hoạt động giao lưu xã hội',
            icon: '💬',
            color: 'bg-yellow-100 text-yellow-800',
            activities: [
              {
                id: 'chatting',
                name: 'Tán Phét',
                description: 'Ngồi cafe tán phét',
                icon: '☕',
                suggestedDuration: 120,
                maxMembers: 6,
                requirements: ['Tự trả tiền đồ uống'],
                templates: [
                  'Tán phét không?',
                  'Cafe tán phét không?',
                  'Ai rảnh ngồi cafe?'
                ]
              },
              {
                id: 'drinking',
                name: 'Nhậu',
                description: 'Đi nhậu cùng nhau',
                icon: '🍺',
                suggestedDuration: 180,
                maxMembers: 6,
                requirements: ['Trên 18 tuổi', 'Có người lái xe'],
                templates: [
                  'Nhậu không?',
                  'Ai đi nhậu cùng?',
                  'Bia hơi cuối tuần không?'
                ]
              },
              {
                id: 'karaoke',
                name: 'Karaoke',
                description: 'Đi hát karaoke',
                icon: '🎤',
                suggestedDuration: 150,
                maxMembers: 8,
                requirements: ['Chia tiền phòng', 'Nước uống'],
                templates: [
                  'Karaoke không?',
                  'Ai đi hát?',
                  'Hát karaoke cuối tuần?'
                ]
              }
            ]
          },
          {
            id: 'food',
            name: 'Ăn Uống',
            description: 'Các hoạt động ăn uống',
            icon: '🍽️',
            color: 'bg-orange-100 text-orange-800',
            activities: [
              {
                id: 'dining',
                name: 'Đi Ăn',
                description: 'Đi ăn cùng nhau',
                icon: '🍜',
                suggestedDuration: 90,
                maxMembers: 6,
                requirements: ['Tự trả tiền ăn'],
                templates: [
                  'Đi ăn không?',
                  'Ai đi ăn cùng?',
                  'Ăn trưa/tối không?'
                ]
              },
              {
                id: 'street_food',
                name: 'Ăn Vặt',
                description: 'Đi ăn vặt đường phố',
                icon: '🌮',
                suggestedDuration: 120,
                maxMembers: 4,
                requirements: ['Tiền mặt', 'Dạ dày khỏe'],
                templates: [
                  'Ăn vặt không?',
                  'Đi ăn đường phố?',
                  'Ai thích ăn vặt?'
                ]
              }
            ]
          }
        ];

        setCategories(mockCategories);
      } catch (error) {
        console.error('Failed to fetch categories:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const filteredCategories = categories.filter(category => {
    if (!searchQuery) return true;
    
    const searchLower = searchQuery.toLowerCase();
    return (
      category.name.toLowerCase().includes(searchLower) ||
      category.description.toLowerCase().includes(searchLower) ||
      category.activities.some(activity => 
        activity.name.toLowerCase().includes(searchLower) ||
        activity.description.toLowerCase().includes(searchLower)
      )
    );
  });

  const handleActivitySelect = (category: Category, activity: ActivityType, template?: string) => {
    onSelectActivity(category.id, activity.id, template);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
        <input
          type="text"
          placeholder="Tìm kiếm hoạt động..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
        />
      </div>

      {/* Categories */}
      <div className="space-y-4">
        {filteredCategories.map((category) => (
          <div key={category.id} className="bg-white rounded-lg shadow-sm border border-gray-200">
            <button
              onClick={() => setSelectedCategory(selectedCategory === category.id ? '' : category.id)}
              className="w-full p-4 flex items-center justify-between hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <span className="text-2xl">{category.icon}</span>
                <div className="text-left">
                  <h3 className="font-semibold text-gray-900">{category.name}</h3>
                  <p className="text-sm text-gray-500">{category.description}</p>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${category.color}`}>
                  {category.activities.length} hoạt động
                </span>
              </div>
              <ChevronRight 
                className={`w-5 h-5 text-gray-400 transition-transform ${
                  selectedCategory === category.id ? 'rotate-90' : ''
                }`} 
              />
            </button>

            {selectedCategory === category.id && (
              <div className="border-t border-gray-200 p-4 space-y-3">
                {category.activities.map((activity) => (
                  <div key={activity.id} className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <span className="text-xl">{activity.icon}</span>
                        <div>
                          <h4 className="font-medium text-gray-900">{activity.name}</h4>
                          <p className="text-sm text-gray-600">{activity.description}</p>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                      <div className="flex items-center space-x-1">
                        <Users className="w-4 h-4" />
                        <span>Tối đa {activity.maxMembers} người</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="w-4 h-4" />
                        <span>{activity.suggestedDuration} phút</span>
                      </div>
                    </div>

                    {activity.requirements.length > 0 && (
                      <div className="mb-3">
                        <p className="text-sm font-medium text-gray-700 mb-1">Cần chuẩn bị:</p>
                        <div className="flex flex-wrap gap-1">
                          {activity.requirements.map((req, index) => (
                            <span key={index} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                              {req}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="space-y-2">
                      <p className="text-sm font-medium text-gray-700">Mẫu tin nhắn:</p>
                      <div className="grid grid-cols-1 gap-2">
                        {activity.templates.map((template, index) => (
                          <button
                            key={index}
                            onClick={() => handleActivitySelect(category, activity, template)}
                            className="text-left p-2 bg-white border border-gray-200 rounded hover:border-orange-300 hover:bg-orange-50 transition-colors"
                          >
                            <span className="text-sm text-gray-700">"{template}"</span>
                          </button>
                        ))}
                      </div>
                      
                      <button
                        onClick={() => handleActivitySelect(category, activity)}
                        className="w-full mt-2 bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors flex items-center justify-center space-x-2"
                      >
                        <Plus className="w-4 h-4" />
                        <span>Tạo {activity.name}</span>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Custom Activity */}
      <div className="bg-gradient-to-r from-purple-100 to-pink-100 rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold text-gray-900 mb-2">Tạo Hoạt Động Tùy Chỉnh</h3>
            <p className="text-sm text-gray-600">Không tìm thấy hoạt động phù hợp? Tạo hoạt động riêng của bạn!</p>
          </div>
          <button
            onClick={onCreateCustom}
            className="bg-purple-500 text-white px-6 py-3 rounded-lg hover:bg-purple-600 transition-colors flex items-center space-x-2"
          >
            <Plus className="w-5 h-5" />
            <span>Tạo Mới</span>
          </button>
        </div>
      </div>

      {filteredCategories.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Search className="w-12 h-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Không tìm thấy hoạt động</h3>
          <p className="text-gray-500">Thử thay đổi từ khóa tìm kiếm hoặc tạo hoạt động mới</p>
        </div>
      )}
    </div>
  );
};

export default ActivityCategories;
