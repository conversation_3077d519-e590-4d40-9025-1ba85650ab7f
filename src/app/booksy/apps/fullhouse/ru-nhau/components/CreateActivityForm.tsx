'use client';

import { useState } from 'react';
import { 
  X, 
  MapPin, 
  Clock, 
  Users, 
  Calendar,
  Plus,
  Minus,
  AlertCircle,
  CheckCircle
} from 'lucide-react';

interface CreateActivityFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (activityData: any) => void;
  prefilledData?: {
    category?: string;
    activityType?: string;
    template?: string;
  };
}

const CreateActivityForm = ({ isOpen, onClose, onSubmit, prefilledData }: CreateActivityFormProps) => {
  const [formData, setFormData] = useState({
    title: prefilledData?.template || '',
    description: '',
    category: prefilledData?.category || 'social',
    activityType: prefilledData?.activityType || 'chatting',
    maxMembers: 4,
    scheduledTime: '',
    duration: 120,
    location: {
      building: 'Times City',
      area: '',
      coordinates: {
        latitude: 20.9967,
        longitude: 105.8686
      }
    },
    requirements: [''],
    tags: []
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<any>({});

  const categories = [
    { id: 'sports', name: '<PERSON>h<PERSON>', icon: '🏃‍♂️' },
    { id: 'shopping', name: 'Mua Sắm', icon: '🛒' },
    { id: 'beauty', name: 'Làm Đẹp', icon: '💅' },
    { id: 'social', name: 'Giao Lưu', icon: '💬' },
    { id: 'food', name: 'Ăn Uống', icon: '🍽️' },
    { id: 'entertainment', name: 'Giải Trí', icon: '🎮' }
  ];

  const activityTypes = {
    sports: [
      { id: 'pickleball', name: 'Pickleball', icon: '🏓' },
      { id: 'tennis', name: 'Tennis', icon: '🎾' },
      { id: 'jogging', name: 'Chạy Bộ', icon: '🏃' },
      { id: 'badminton', name: 'Cầu Lông', icon: '🏸' },
      { id: 'swimming', name: 'Bơi Lội', icon: '🏊' }
    ],
    shopping: [
      { id: 'grocery_shopping', name: 'Đi Chợ', icon: '🥬' },
      { id: 'mall_shopping', name: 'Đi Mall', icon: '🏬' },
      { id: 'market_shopping', name: 'Chợ Truyền Thống', icon: '🏪' }
    ],
    beauty: [
      { id: 'hair_salon', name: 'Gội Đầu/Spa', icon: '💆‍♀️' },
      { id: 'nail_salon', name: 'Làm Nail', icon: '💅' },
      { id: 'facial', name: 'Chăm Sóc Da', icon: '🧴' }
    ],
    social: [
      { id: 'chatting', name: 'Tán Phét', icon: '☕' },
      { id: 'drinking', name: 'Nhậu', icon: '🍺' },
      { id: 'karaoke', name: 'Karaoke', icon: '🎤' },
      { id: 'board_games', name: 'Board Games', icon: '🎲' }
    ],
    food: [
      { id: 'dining', name: 'Đi Ăn', icon: '🍜' },
      { id: 'street_food', name: 'Ăn Vặt', icon: '🌮' },
      { id: 'cooking', name: 'Nấu Ăn', icon: '👨‍🍳' }
    ],
    entertainment: [
      { id: 'movies', name: 'Xem Phim', icon: '🎬' },
      { id: 'gaming', name: 'Chơi Game', icon: '🎮' },
      { id: 'music', name: 'Nghe Nhạc', icon: '🎵' }
    ]
  };

  const buildings = [
    'Times City',
    'Royal City', 
    'Landmark 72',
    'Lotte Center',
    'Vincom Mega Mall'
  ];

  const validateForm = () => {
    const newErrors: any = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Vui lòng nhập tiêu đề';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Vui lòng nhập mô tả';
    }

    if (formData.maxMembers < 2 || formData.maxMembers > 20) {
      newErrors.maxMembers = 'Số thành viên phải từ 2 đến 20';
    }

    if (!formData.scheduledTime) {
      newErrors.scheduledTime = 'Vui lòng chọn thời gian';
    } else {
      const scheduledDate = new Date(formData.scheduledTime);
      const now = new Date();
      if (scheduledDate <= now) {
        newErrors.scheduledTime = 'Thời gian phải trong tương lai';
      }
    }

    if (formData.duration < 30 || formData.duration > 480) {
      newErrors.duration = 'Thời gian từ 30 phút đến 8 giờ';
    }

    if (!formData.location.area.trim()) {
      newErrors.locationArea = 'Vui lòng nhập địa điểm cụ thể';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Filter out empty requirements
      const cleanedRequirements = formData.requirements.filter(req => req.trim() !== '');
      
      const activityData = {
        ...formData,
        requirements: cleanedRequirements,
        creatorId: 'user-current', // Mock current user ID
        creatorInfo: {
          name: 'Người dùng hiện tại',
          avatar: '/images/users/current-user.jpg',
          apartment: 'T1-1205',
          building: formData.location.building
        }
      };

      await onSubmit(activityData);
      onClose();
    } catch (error) {
      console.error('Failed to create activity:', error);
      setErrors({ submit: 'Không thể tạo hoạt động. Vui lòng thử lại.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const addRequirement = () => {
    setFormData(prev => ({
      ...prev,
      requirements: [...prev.requirements, '']
    }));
  };

  const removeRequirement = (index: number) => {
    setFormData(prev => ({
      ...prev,
      requirements: prev.requirements.filter((_, i) => i !== index)
    }));
  };

  const updateRequirement = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      requirements: prev.requirements.map((req, i) => i === index ? value : req)
    }));
  };

  // Set default scheduled time to 1 hour from now
  const getDefaultScheduledTime = () => {
    const now = new Date();
    now.setHours(now.getHours() + 1);
    now.setMinutes(0);
    return now.toISOString().slice(0, 16);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Tạo Hoạt Động Mới</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tiêu đề hoạt động *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                errors.title ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Ví dụ: Ai đi pickleball không?"
            />
            {errors.title && (
              <p className="mt-1 text-sm text-red-600 flex items-center space-x-1">
                <AlertCircle className="w-4 h-4" />
                <span>{errors.title}</span>
              </p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Mô tả chi tiết *
            </label>
            <textarea
              rows={3}
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                errors.description ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Mô tả chi tiết về hoạt động..."
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-600 flex items-center space-x-1">
                <AlertCircle className="w-4 h-4" />
                <span>{errors.description}</span>
              </p>
            )}
          </div>

          {/* Category & Activity Type */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Danh mục
              </label>
              <select
                value={formData.category}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  category: e.target.value,
                  activityType: activityTypes[e.target.value as keyof typeof activityTypes]?.[0]?.id || ''
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                {categories.map(cat => (
                  <option key={cat.id} value={cat.id}>
                    {cat.icon} {cat.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Loại hoạt động
              </label>
              <select
                value={formData.activityType}
                onChange={(e) => setFormData(prev => ({ ...prev, activityType: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                {(activityTypes[formData.category as keyof typeof activityTypes] || []).map(type => (
                  <option key={type.id} value={type.id}>
                    {type.icon} {type.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Max Members & Duration */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Số thành viên tối đa *
              </label>
              <div className="flex items-center space-x-2">
                <Users className="w-5 h-5 text-gray-400" />
                <input
                  type="number"
                  min="2"
                  max="20"
                  value={formData.maxMembers}
                  onChange={(e) => setFormData(prev => ({ ...prev, maxMembers: parseInt(e.target.value) || 2 }))}
                  className={`flex-1 px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                    errors.maxMembers ? 'border-red-300' : 'border-gray-300'
                  }`}
                />
              </div>
              {errors.maxMembers && (
                <p className="mt-1 text-sm text-red-600">{errors.maxMembers}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Thời gian dự kiến (phút) *
              </label>
              <div className="flex items-center space-x-2">
                <Clock className="w-5 h-5 text-gray-400" />
                <input
                  type="number"
                  min="30"
                  max="480"
                  step="30"
                  value={formData.duration}
                  onChange={(e) => setFormData(prev => ({ ...prev, duration: parseInt(e.target.value) || 120 }))}
                  className={`flex-1 px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                    errors.duration ? 'border-red-300' : 'border-gray-300'
                  }`}
                />
              </div>
              {errors.duration && (
                <p className="mt-1 text-sm text-red-600">{errors.duration}</p>
              )}
            </div>
          </div>

          {/* Scheduled Time */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Thời gian bắt đầu *
            </label>
            <div className="flex items-center space-x-2">
              <Calendar className="w-5 h-5 text-gray-400" />
              <input
                type="datetime-local"
                value={formData.scheduledTime || getDefaultScheduledTime()}
                onChange={(e) => setFormData(prev => ({ ...prev, scheduledTime: e.target.value }))}
                className={`flex-1 px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                  errors.scheduledTime ? 'border-red-300' : 'border-gray-300'
                }`}
              />
            </div>
            {errors.scheduledTime && (
              <p className="mt-1 text-sm text-red-600">{errors.scheduledTime}</p>
            )}
          </div>

          {/* Location */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Địa điểm *
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <select
                  value={formData.location.building}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    location: { ...prev.location, building: e.target.value }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                >
                  {buildings.map(building => (
                    <option key={building} value={building}>{building}</option>
                  ))}
                </select>
              </div>
              <div>
                <div className="flex items-center space-x-2">
                  <MapPin className="w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    value={formData.location.area}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      location: { ...prev.location, area: e.target.value }
                    }))}
                    className={`flex-1 px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                      errors.locationArea ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Ví dụ: Sân pickleball tầng 5"
                  />
                </div>
                {errors.locationArea && (
                  <p className="mt-1 text-sm text-red-600">{errors.locationArea}</p>
                )}
              </div>
            </div>
          </div>

          {/* Requirements */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Yêu cầu chuẩn bị
            </label>
            <div className="space-y-2">
              {formData.requirements.map((requirement, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={requirement}
                    onChange={(e) => updateRequirement(index, e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    placeholder="Ví dụ: Mang vợt riêng"
                  />
                  {formData.requirements.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeRequirement(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <Minus className="w-5 h-5" />
                    </button>
                  )}
                </div>
              ))}
              <button
                type="button"
                onClick={addRequirement}
                className="flex items-center space-x-2 text-orange-600 hover:text-orange-800"
              >
                <Plus className="w-4 h-4" />
                <span>Thêm yêu cầu</span>
              </button>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              Hủy
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="bg-orange-500 text-white px-6 py-2 rounded-lg hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Đang tạo...</span>
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4" />
                  <span>Tạo Hoạt Động</span>
                </>
              )}
            </button>
          </div>

          {errors.submit && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600 flex items-center space-x-2">
                <AlertCircle className="w-4 h-4" />
                <span>{errors.submit}</span>
              </p>
            </div>
          )}
        </form>
      </div>
    </div>
  );
};

export default CreateActivityForm;
