'use client';

import { useState, useEffect, useRef } from 'react';
import { 
  Send, 
  Smile, 
  Globe,
  Users,
  MapPin,
  Clock,
  X,
  AlertCircle,
  Shield
} from 'lucide-react';

interface GlobalMessage {
  id: string;
  senderId: string;
  senderInfo: {
    name: string;
    avatar: string;
    apartment: string;
    building: string;
  };
  message: string;
  messageType: string;
  timestamp: string;
  isDeleted: boolean;
  reactions?: Array<{
    userId: string;
    emoji: string;
    timestamp: string;
  }>;
  isGlobal: boolean;
}

interface ActiveUser {
  userId: string;
  building: string;
  lastSeen: string;
  joinedAt: string;
}

interface GlobalChatProps {
  currentUserId: string;
  currentUserInfo: {
    name: string;
    avatar: string;
    apartment: string;
    building: string;
  };
  isOpen: boolean;
  onClose: () => void;
}

const GlobalChat = ({ 
  currentUserId, 
  currentUserInfo, 
  isOpen, 
  onClose 
}: GlobalChatProps) => {
  const [messages, setMessages] = useState<GlobalMessage[]>([]);
  const [activeUsers, setActiveUsers] = useState<ActiveUser[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [hasAccess, setHasAccess] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState<string | null>(null);
  const [settings, setSettings] = useState<any>({});
  const [stats, setStats] = useState<any>({});
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const emojis = ['❤️', '👍', '😂', '😮', '😢', '😡', '👏', '🔥', '💯', '🎉'];

  useEffect(() => {
    if (isOpen) {
      fetchGlobalChat();
      const interval = setInterval(fetchGlobalChat, 5000); // Poll every 5 seconds
      return () => clearInterval(interval);
    }
  }, [isOpen]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchGlobalChat = async () => {
    try {
      const response = await fetch(
        `/api/booksy/fullhouse/ru-nhau/global-chat?userId=${currentUserId}&building=${currentUserInfo.building}`
      );
      
      if (response.ok) {
        const data = await response.json();
        setMessages(data.messages || []);
        setActiveUsers(data.activeUsers || []);
        setHasAccess(data.hasAccess);
        setSettings(data.settings || {});
        setStats(data.stats || {});
      } else {
        const errorData = await response.json();
        setHasAccess(errorData.hasAccess || false);
        setSettings(errorData.settings || {});
      }
    } catch (error) {
      console.error('Failed to fetch global chat:', error);
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || isLoading || !hasAccess) return;

    setIsLoading(true);
    try {
      const response = await fetch('/api/booksy/fullhouse/ru-nhau/global-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: currentUserId,
          userInfo: currentUserInfo,
          message: newMessage.trim(),
          messageType: 'text'
        }),
      });

      if (response.ok) {
        setNewMessage('');
        await fetchGlobalChat();
      } else {
        const errorData = await response.json();
        alert(errorData.error || 'Không thể gửi tin nhắn');
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      alert('Không thể gửi tin nhắn');
    } finally {
      setIsLoading(false);
    }
  };

  const reactToMessage = async (messageId: string, emoji: string) => {
    try {
      await fetch('/api/booksy/fullhouse/ru-nhau/global-chat', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messageId,
          action: 'react',
          userId: currentUserId,
          userBuilding: currentUserInfo.building,
          reaction: emoji
        }),
      });

      await fetchGlobalChat();
      setShowEmojiPicker(null);
    } catch (error) {
      console.error('Failed to react to message:', error);
    }
  };

  const deleteMessage = async (messageId: string) => {
    try {
      await fetch('/api/booksy/fullhouse/ru-nhau/global-chat', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messageId,
          action: 'delete',
          userId: currentUserId,
          userBuilding: currentUserInfo.building
        }),
      });

      await fetchGlobalChat();
    } catch (error) {
      console.error('Failed to delete message:', error);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('vi-VN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const getReactionCount = (reactions: any[], emoji: string) => {
    return reactions?.filter(r => r.emoji === emoji).length || 0;
  };

  const hasUserReacted = (reactions: any[], emoji: string) => {
    return reactions?.some(r => r.emoji === emoji && r.userId === currentUserId) || false;
  };

  const getBuildingColor = (building: string) => {
    const colors: { [key: string]: string } = {
      'Times City': 'bg-blue-100 text-blue-800',
      'Royal City': 'bg-purple-100 text-purple-800',
      'Landmark 72': 'bg-green-100 text-green-800',
      'Lotte Center': 'bg-red-100 text-red-800',
      'Vincom Mega Mall': 'bg-yellow-100 text-yellow-800'
    };
    return colors[building] || 'bg-gray-100 text-gray-800';
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-2xl h-[700px] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <Globe className="w-6 h-6 text-blue-500" />
            <div>
              <h3 className="font-semibold text-gray-900">Chat Toàn Khu Vực</h3>
              <p className="text-sm text-gray-500">
                {stats.activeUsersCount || 0} người đang online
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {!hasAccess ? (
          /* Access Denied */
          <div className="flex-1 flex items-center justify-center p-8">
            <div className="text-center">
              <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Không có quyền truy cập
              </h3>
              <p className="text-gray-600 mb-4">
                Chat toàn khu vực chỉ dành cho cư dân trong bán kính 1km từ Times City
              </p>
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Khu vực được phép:</h4>
                <div className="space-y-1">
                  {settings.allowedBuildings?.map((building: string) => (
                    <div key={building} className="flex items-center space-x-2">
                      <MapPin className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-600">{building}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* Active Users */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center space-x-2 mb-2">
                <Users className="w-4 h-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">
                  Đang online ({activeUsers.length})
                </span>
              </div>
              <div className="flex flex-wrap gap-2">
                {activeUsers.slice(0, 10).map((user) => (
                  <span
                    key={user.userId}
                    className={`px-2 py-1 rounded-full text-xs font-medium ${getBuildingColor(user.building)}`}
                  >
                    {user.building}
                  </span>
                ))}
                {activeUsers.length > 10 && (
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                    +{activeUsers.length - 10} khác
                  </span>
                )}
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.map((message) => {
                const isOwnMessage = message.senderId === currentUserId;
                
                return (
                  <div key={message.id} className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[80%] ${isOwnMessage ? 'order-2' : 'order-1'}`}>
                      {/* Sender info for others' messages */}
                      {!isOwnMessage && (
                        <div className="flex items-center space-x-2 mb-1">
                          <img
                            src={message.senderInfo.avatar}
                            alt={message.senderInfo.name}
                            className="w-6 h-6 rounded-full"
                          />
                          <span className="text-xs text-gray-600">
                            {message.senderInfo.name}
                          </span>
                          <span className={`px-2 py-0.5 rounded-full text-xs ${getBuildingColor(message.senderInfo.building)}`}>
                            {message.senderInfo.building}
                          </span>
                        </div>
                      )}

                      {/* Message bubble */}
                      <div
                        className={`relative rounded-lg px-3 py-2 ${
                          isOwnMessage
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-100 text-gray-900'
                        } ${message.isDeleted ? 'opacity-50' : ''}`}
                      >
                        <p className="text-sm">{message.message}</p>
                        
                        {/* Message actions */}
                        <div className="flex items-center justify-between mt-1">
                          <span className="text-xs opacity-70">
                            {formatTime(message.timestamp)}
                          </span>
                          
                          {!message.isDeleted && (
                            <div className="flex items-center space-x-1">
                              <button
                                onClick={() => setShowEmojiPicker(showEmojiPicker === message.id ? null : message.id)}
                                className="opacity-70 hover:opacity-100"
                              >
                                <Smile className="w-3 h-3" />
                              </button>
                              
                              {isOwnMessage && (
                                <button
                                  onClick={() => deleteMessage(message.id)}
                                  className="opacity-70 hover:opacity-100"
                                >
                                  <X className="w-3 h-3" />
                                </button>
                              )}
                            </div>
                          )}
                        </div>

                        {/* Emoji picker */}
                        {showEmojiPicker === message.id && (
                          <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg p-2 flex space-x-1 z-10">
                            {emojis.map((emoji) => (
                              <button
                                key={emoji}
                                onClick={() => reactToMessage(message.id, emoji)}
                                className="hover:bg-gray-100 p-1 rounded"
                              >
                                {emoji}
                              </button>
                            ))}
                          </div>
                        )}
                      </div>

                      {/* Reactions */}
                      {message.reactions && message.reactions.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-1">
                          {Object.entries(
                            message.reactions.reduce((acc: any, reaction) => {
                              acc[reaction.emoji] = (acc[reaction.emoji] || 0) + 1;
                              return acc;
                            }, {})
                          ).map(([emoji, count]) => (
                            <button
                              key={emoji}
                              onClick={() => reactToMessage(message.id, emoji)}
                              className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${
                                hasUserReacted(message.reactions!, emoji)
                                  ? 'bg-blue-100 text-blue-800'
                                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                              }`}
                            >
                              <span>{emoji}</span>
                              <span>{count as number}</span>
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
              <div ref={messagesEndRef} />
            </div>

            {/* Rules */}
            <div className="px-4 py-2 bg-yellow-50 border-t border-yellow-200">
              <div className="flex items-start space-x-2">
                <Shield className="w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                <div className="text-xs text-yellow-800">
                  <strong>Quy tắc chat:</strong> {settings.moderationRules?.join(' • ')}
                </div>
              </div>
            </div>

            {/* Input */}
            <div className="p-4 border-t border-gray-200">
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Nhập tin nhắn cho toàn khu vực..."
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  disabled={isLoading}
                  maxLength={500}
                />
                <button
                  onClick={sendMessage}
                  disabled={!newMessage.trim() || isLoading}
                  className="bg-blue-500 text-white p-2 rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  ) : (
                    <Send className="w-5 h-5" />
                  )}
                </button>
              </div>
              
              <div className="mt-2 text-xs text-gray-500 flex items-center justify-between">
                <div className="flex items-center space-x-1">
                  <Clock className="w-3 h-3" />
                  <span>Tin nhắn tự xóa sau 30 phút</span>
                </div>
                <span>{newMessage.length}/500</span>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default GlobalChat;
