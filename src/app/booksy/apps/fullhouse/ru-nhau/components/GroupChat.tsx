'use client';

import { useState, useEffect, useRef } from 'react';
import { 
  Send, 
  Smile, 
  MoreH<PERSON>zontal, 
  <PERSON><PERSON>,
  Heart,
  ThumbsUp,
  Laugh,
  Angry,
  Sad,
  Clock,
  Users,
  X
} from 'lucide-react';

interface Message {
  id: string;
  senderId: string;
  senderInfo: {
    name: string;
    avatar: string;
    apartment: string;
  };
  message: string;
  messageType: string;
  timestamp: string;
  autoDeleteAt: string;
  isDeleted: boolean;
  reactions?: Array<{
    userId: string;
    emoji: string;
    timestamp: string;
  }>;
  replyTo?: string;
}

interface GroupChatProps {
  activityId: string;
  chatId: string;
  currentUserId: string;
  currentUserInfo: {
    name: string;
    avatar: string;
    apartment: string;
  };
  isOpen: boolean;
  onClose: () => void;
}

const GroupChat = ({ 
  activityId, 
  chatId, 
  currentUserId, 
  currentUserInfo, 
  isOpen, 
  onClose 
}: GroupChatProps) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState<string | null>(null);
  const [replyingTo, setReplyingTo] = useState<Message | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const emojis = ['❤️', '👍', '😂', '😮', '😢', '😡', '👏', '🔥', '💯', '🎉'];

  useEffect(() => {
    if (isOpen) {
      fetchMessages();
      const interval = setInterval(fetchMessages, 3000); // Poll every 3 seconds
      return () => clearInterval(interval);
    }
  }, [isOpen, chatId]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchMessages = async () => {
    try {
      const response = await fetch(`/api/booksy/fullhouse/ru-nhau/chat?chatId=${chatId}&activityId=${activityId}&userId=${currentUserId}`);
      if (response.ok) {
        const data = await response.json();
        setMessages(data);
      }
    } catch (error) {
      console.error('Failed to fetch messages:', error);
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || isLoading) return;

    setIsLoading(true);
    try {
      const response = await fetch('/api/booksy/fullhouse/ru-nhau/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chatId,
          activityId,
          senderId: currentUserId,
          senderInfo: currentUserInfo,
          message: newMessage.trim(),
          messageType: 'text',
          replyTo: replyingTo?.id || null
        }),
      });

      if (response.ok) {
        setNewMessage('');
        setReplyingTo(null);
        await fetchMessages();
      }
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const reactToMessage = async (messageId: string, emoji: string) => {
    try {
      await fetch('/api/booksy/fullhouse/ru-nhau/chat', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          messageId,
          action: 'react',
          userId: currentUserId,
          reaction: emoji
        }),
      });

      await fetchMessages();
      setShowEmojiPicker(null);
    } catch (error) {
      console.error('Failed to react to message:', error);
    }
  };

  const deleteMessage = async (messageId: string) => {
    try {
      await fetch(`/api/booksy/fullhouse/ru-nhau/chat?messageId=${messageId}&userId=${currentUserId}`, {
        method: 'DELETE',
      });

      await fetchMessages();
    } catch (error) {
      console.error('Failed to delete message:', error);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('vi-VN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getTimeUntilDelete = (autoDeleteAt: string) => {
    const deleteTime = new Date(autoDeleteAt);
    const now = new Date();
    const diff = deleteTime.getTime() - now.getTime();
    
    if (diff <= 0) return 'Đã hết hạn';
    
    const minutes = Math.floor(diff / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);
    
    if (minutes > 0) {
      return `${minutes}p ${seconds}s`;
    }
    return `${seconds}s`;
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const getReactionCount = (reactions: any[], emoji: string) => {
    return reactions?.filter(r => r.emoji === emoji).length || 0;
  };

  const hasUserReacted = (reactions: any[], emoji: string) => {
    return reactions?.some(r => r.emoji === emoji && r.userId === currentUserId) || false;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-md h-[600px] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <Users className="w-5 h-5 text-orange-500" />
            <h3 className="font-semibold text-gray-900">Chat Nhóm</h3>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => {
            const isOwnMessage = message.senderId === currentUserId;
            const timeUntilDelete = getTimeUntilDelete(message.autoDeleteAt);
            
            return (
              <div key={message.id} className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
                <div className={`max-w-[80%] ${isOwnMessage ? 'order-2' : 'order-1'}`}>
                  {/* Sender info for others' messages */}
                  {!isOwnMessage && (
                    <div className="flex items-center space-x-2 mb-1">
                      <img
                        src={message.senderInfo.avatar}
                        alt={message.senderInfo.name}
                        className="w-6 h-6 rounded-full"
                      />
                      <span className="text-xs text-gray-600">
                        {message.senderInfo.name} • {message.senderInfo.apartment}
                      </span>
                    </div>
                  )}

                  {/* Reply indicator */}
                  {message.replyTo && (
                    <div className="text-xs text-gray-500 mb-1 italic">
                      Trả lời tin nhắn
                    </div>
                  )}

                  {/* Message bubble */}
                  <div
                    className={`relative rounded-lg px-3 py-2 ${
                      isOwnMessage
                        ? 'bg-orange-500 text-white'
                        : 'bg-gray-100 text-gray-900'
                    } ${message.isDeleted ? 'opacity-50' : ''}`}
                  >
                    <p className="text-sm">{message.message}</p>
                    
                    {/* Message actions */}
                    <div className="flex items-center justify-between mt-1">
                      <div className="flex items-center space-x-1">
                        <span className="text-xs opacity-70">
                          {formatTime(message.timestamp)}
                        </span>
                        <div className="flex items-center space-x-1 text-xs opacity-70">
                          <Clock className="w-3 h-3" />
                          <span>{timeUntilDelete}</span>
                        </div>
                      </div>
                      
                      {!message.isDeleted && (
                        <div className="flex items-center space-x-1">
                          <button
                            onClick={() => setShowEmojiPicker(showEmojiPicker === message.id ? null : message.id)}
                            className="opacity-70 hover:opacity-100"
                          >
                            <Smile className="w-3 h-3" />
                          </button>
                          
                          {!isOwnMessage && (
                            <button
                              onClick={() => setReplyingTo(message)}
                              className="opacity-70 hover:opacity-100"
                            >
                              <Reply className="w-3 h-3" />
                            </button>
                          )}
                          
                          {isOwnMessage && (
                            <button
                              onClick={() => deleteMessage(message.id)}
                              className="opacity-70 hover:opacity-100"
                            >
                              <X className="w-3 h-3" />
                            </button>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Emoji picker */}
                    {showEmojiPicker === message.id && (
                      <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg p-2 flex space-x-1 z-10">
                        {emojis.map((emoji) => (
                          <button
                            key={emoji}
                            onClick={() => reactToMessage(message.id, emoji)}
                            className="hover:bg-gray-100 p-1 rounded"
                          >
                            {emoji}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Reactions */}
                  {message.reactions && message.reactions.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-1">
                      {Object.entries(
                        message.reactions.reduce((acc: any, reaction) => {
                          acc[reaction.emoji] = (acc[reaction.emoji] || 0) + 1;
                          return acc;
                        }, {})
                      ).map(([emoji, count]) => (
                        <button
                          key={emoji}
                          onClick={() => reactToMessage(message.id, emoji)}
                          className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${
                            hasUserReacted(message.reactions!, emoji)
                              ? 'bg-orange-100 text-orange-800'
                              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                          }`}
                        >
                          <span>{emoji}</span>
                          <span>{count as number}</span>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
          <div ref={messagesEndRef} />
        </div>

        {/* Reply indicator */}
        {replyingTo && (
          <div className="px-4 py-2 bg-gray-50 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Trả lời: {replyingTo.senderInfo.name}
              </div>
              <button
                onClick={() => setReplyingTo(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
            <div className="text-sm text-gray-500 truncate">
              {replyingTo.message}
            </div>
          </div>
        )}

        {/* Input */}
        <div className="p-4 border-t border-gray-200">
          <div className="flex items-center space-x-2">
            <input
              ref={inputRef}
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Nhập tin nhắn..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              disabled={isLoading}
            />
            <button
              onClick={sendMessage}
              disabled={!newMessage.trim() || isLoading}
              className="bg-orange-500 text-white p-2 rounded-lg hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              ) : (
                <Send className="w-5 h-5" />
              )}
            </button>
          </div>
          
          <div className="mt-2 text-xs text-gray-500 flex items-center space-x-1">
            <Clock className="w-3 h-3" />
            <span>Tin nhắn tự xóa sau 30 phút</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GroupChat;
