"use client";

import { useState, useEffect } from "react";
import {
  User,
  Calendar,
  Users,
  Star,
  Settings,
  Clock,
  TrendingUp,
  MessageSquare,
  DollarSign,
  Package,
  CheckCircle,
  AlertTriangle,
  Eye,
  ArrowRight,
  Phone,
  MapPin,
  Wrench,
  Home,
  Store,
  ShoppingBag,
  Globe,
  Monitor,
  Plus,
  ExternalLink,
  UtensilsCrossed
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import WorkingMode from "./components/WorkingMode";

interface ProviderStats {
  totalBookings: number;
  completedBookings: number;
  pendingOrders: number;
  totalEarnings: number;
  thisMonthEarnings: number;
  rating: number;
  totalCustomers: number;
  upcomingBookings: number;
  responseTime: string;
}

interface Order {
  id: string;
  customerName: string;
  serviceName: string;
  address: string;
  scheduledTime: string;
  status: string;
  price: number;
  isEmergency: boolean;
}

interface ProviderShop {
  kioskShop: {
    enabled: boolean;
    storeId: string | null;
    storeName: string | null;
    status: 'active' | 'draft' | 'disabled';
    storeDetails?: any;
  };
  ecommerceShop: {
    enabled: boolean;
    storeId: string | null;
    storeName: string | null;
    status: 'active' | 'draft' | 'disabled';
    domain?: string;
    storeDetails?: any;
  };
}

export default function ProviderDashboard() {
  const router = useRouter();
  const [stats, setStats] = useState<ProviderStats>({
    totalBookings: 156,
    completedBookings: 148,
    pendingOrders: 3,
    totalEarnings: 78000000,
    thisMonthEarnings: 6000000,
    rating: 4.8,
    totalCustomers: 89,
    upcomingBookings: 5,
    responseTime: "15 phút"
  });

  const [recentOrders, setRecentOrders] = useState<Order[]>([
    {
      id: "order-001",
      customerName: "Nguyễn Văn A",
      serviceName: "Dịch Vụ Ống Nước",
      address: "Tòa T1, Căn hộ 1502, Times City",
      scheduledTime: "2024-01-25T14:00:00Z",
      status: "pending",
      price: 800000,
      isEmergency: false
    },
    {
      id: "order-002",
      customerName: "Trần Thị B",
      serviceName: "Dịch Vụ Điện",
      address: "Tòa T2, Căn hộ 2103, Times City",
      scheduledTime: "2024-01-26T09:00:00Z",
      status: "confirmed",
      price: 450000,
      isEmergency: false
    }
  ]);

  const [shops, setShops] = useState<ProviderShop | null>(null);

  useEffect(() => {
    // Fetch real data from API
    fetchDashboardData();
    fetchShopsData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Fetch provider profile and stats
      const profileResponse = await fetch('/api/booksy/providers/profile');
      const profile = await profileResponse.json();

      if (profile.stats) {
        setStats({
          totalBookings: profile.stats.totalBookings,
          completedBookings: profile.stats.completedBookings,
          pendingOrders: 3, // This would come from orders API
          totalEarnings: profile.stats.totalEarnings,
          thisMonthEarnings: profile.stats.thisMonthEarnings,
          rating: profile.ratings.averageRating,
          totalCustomers: profile.stats.totalBookings, // Approximate
          upcomingBookings: 5, // This would come from orders API
          responseTime: profile.stats.responseTime
        });
      }

      // Fetch recent orders
      const ordersResponse = await fetch('/api/booksy/providers/orders?limit=5');
      const ordersData = await ordersResponse.json();
      if (ordersData.orders) {
        const formattedOrders = ordersData.orders.map((order: any) => ({
          id: order.id,
          customerName: order.customerInfo.name,
          serviceName: order.serviceInfo.name,
          address: order.customerInfo.address,
          scheduledTime: order.scheduledDateTime,
          status: order.status,
          price: order.price.totalPrice,
          isEmergency: order.serviceInfo.isEmergency
        }));
        setRecentOrders(formattedOrders);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    }
  };

  const fetchShopsData = async () => {
    try {
      const providerId = 'provider-plumbing-1'; // In real app, get from auth/context
      const response = await fetch(`/api/booksy/providers/shops?providerId=${providerId}`);
      if (response.ok) {
        const data = await response.json();
        setShops(data.shops);
      }
    } catch (error) {
      console.error('Error fetching shops data:', error);
    }
  };

  const formatDateTime = (dateTimeString: string) => {
    const date = new Date(dateTimeString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return `Hôm nay, ${date.toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}`;
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return `Ngày mai, ${date.toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' })}`;
    } else {
      return date.toLocaleDateString('vi-VN') + ', ' + date.toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Chờ xác nhận';
      case 'confirmed':
        return 'Đã xác nhận';
      case 'in_progress':
        return 'Đang thực hiện';
      case 'completed':
        return 'Hoàn thành';
      default:
        return status;
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Xin chào, Minh Nguyễn</h1>
          <p className="text-gray-600">Quản lý dịch vụ gia đình của bạn</p>
        </div>
        <div className="flex space-x-3">
          <Link
            href="/booksy/apps/providers/bookings"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <Package className="h-4 w-4 mr-2" />
            Đơn Hàng
          </Link>
          <Link
            href="/booksy/apps/providers/profile"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
          >
            <Settings className="h-4 w-4 mr-2" />
            Hồ Sơ
          </Link>
        </div>
      </div>

      {/* Working Mode Section */}
      <div className="mb-8">
        <WorkingMode />
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 text-blue-600">
              <Package className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Đơn Hàng Chờ</p>
              <p className="text-lg font-semibold text-gray-900">{stats.pendingOrders}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 text-green-600">
              <CheckCircle className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Hoàn Thành</p>
              <p className="text-lg font-semibold text-gray-900">{stats.completedBookings}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100 text-yellow-600">
              <DollarSign className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Thu Nhập Tháng</p>
              <p className="text-lg font-semibold text-gray-900">{stats.thisMonthEarnings.toLocaleString()} VNĐ</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 text-purple-600">
              <Star className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Đánh Giá</p>
              <p className="text-lg font-semibold text-gray-900">{stats.rating}/5.0</p>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Stats Row */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Tổng Thu Nhập</p>
              <p className="text-2xl font-bold text-green-600">{stats.totalEarnings.toLocaleString()} VNĐ</p>
            </div>
            <TrendingUp className="h-8 w-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Thời Gian Phản Hồi</p>
              <p className="text-2xl font-bold text-blue-600">{stats.responseTime}</p>
            </div>
            <Clock className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Tổng Khách Hàng</p>
              <p className="text-2xl font-bold text-purple-600">{stats.totalCustomers}</p>
            </div>
            <Users className="h-8 w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* My Shops Section */}
      {shops && (
        <div className="mb-8">
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
              <h2 className="text-lg font-medium text-gray-900 flex items-center">
                <Store className="h-5 w-5 mr-2" />
                Cửa Hàng Của Tôi
              </h2>
              <Link
                href="/booksy/apps/providers/shops"
                className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
              >
                Quản lý cửa hàng
                <ArrowRight className="h-4 w-4 ml-1" />
              </Link>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Kiosk Shop */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <Monitor className="h-5 w-5 text-blue-600" />
                      <h3 className="font-medium text-gray-900">Kiosk Shop</h3>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      shops.kioskShop.enabled && shops.kioskShop.storeId
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {shops.kioskShop.enabled && shops.kioskShop.storeId ? 'Hoạt động' : 'Chưa tạo'}
                    </span>
                  </div>
                  {shops.kioskShop.enabled && shops.kioskShop.storeId ? (
                    <div>
                      <p className="text-sm text-gray-600 mb-2">{shops.kioskShop.storeName}</p>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => window.open(`/platforms/abnshops/kiosk/stores/${shops.kioskShop.storeId}`, '_blank')}
                          className="flex-1 px-3 py-2 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                        >
                          Quản lý
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <p className="text-sm text-gray-600 mb-3">Tạo cửa hàng kiosk để bán hàng trực tiếp</p>
                      <button
                        onClick={() => window.location.href = '/booksy/apps/providers/shops/create?type=kiosk'}
                        className="w-full px-3 py-2 border border-blue-600 text-blue-600 rounded text-sm hover:bg-blue-50"
                      >
                        Tạo Kiosk Shop
                      </button>
                    </div>
                  )}
                </div>

                {/* Ecommerce Shop */}
                <div className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <Globe className="h-5 w-5 text-green-600" />
                      <h3 className="font-medium text-gray-900">Ecommerce Shop</h3>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      shops.ecommerceShop.enabled && shops.ecommerceShop.storeId
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {shops.ecommerceShop.enabled && shops.ecommerceShop.storeId ? 'Hoạt động' : 'Chưa tạo'}
                    </span>
                  </div>
                  {shops.ecommerceShop.enabled && shops.ecommerceShop.storeId ? (
                    <div>
                      <p className="text-sm text-gray-600 mb-1">{shops.ecommerceShop.storeName}</p>
                      <p className="text-xs text-gray-500 mb-2">{shops.ecommerceShop.domain}</p>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => window.open(`/platforms/abnshops/ecommerce/stores/${shops.ecommerceShop.storeId}`, '_blank')}
                          className="flex-1 px-3 py-2 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                        >
                          Quản lý
                        </button>
                        <button
                          onClick={() => window.open(`https://${shops.ecommerceShop.domain}`, '_blank')}
                          className="px-3 py-2 border border-green-600 text-green-600 rounded text-sm hover:bg-green-50"
                        >
                          <ExternalLink className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <p className="text-sm text-gray-600 mb-3">Tạo cửa hàng online để bán hàng trên web</p>
                      <button
                        onClick={() => window.location.href = '/booksy/apps/providers/shops/create?type=ecommerce'}
                        className="w-full px-3 py-2 border border-green-600 text-green-600 rounded text-sm hover:bg-green-50"
                      >
                        Tạo Ecommerce Shop
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Orders */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
              <h2 className="text-lg font-medium text-gray-900">Đơn Hàng Gần Đây</h2>
              <Link
                href="/booksy/apps/providers/bookings"
                className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
              >
                Xem tất cả
                <ArrowRight className="h-4 w-4 ml-1" />
              </Link>
            </div>
            <div className="p-6">
              {recentOrders.length === 0 ? (
                <div className="text-center py-8">
                  <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Chưa có đơn hàng nào</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {recentOrders.map((order) => (
                    <div key={order.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                            {order.serviceName.includes('Ống Nước') ? (
                              <Wrench className="h-6 w-6 text-blue-600" />
                            ) : (
                              <Home className="h-6 w-6 text-blue-600" />
                            )}
                          </div>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <p className="text-sm font-medium text-gray-900">{order.customerName}</p>
                            {order.isEmergency && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                <AlertTriangle className="h-3 w-3 mr-1" />
                                Khẩn Cấp
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-600">{order.serviceName}</p>
                          <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                            <div className="flex items-center space-x-1">
                              <MapPin className="h-3 w-3" />
                              <span>{order.address}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Clock className="h-3 w-3" />
                              <span>{formatDateTime(order.scheduledTime)}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                          {getStatusText(order.status)}
                        </span>
                        <p className="text-sm font-medium text-gray-900 mt-1">
                          {order.price.toLocaleString()} VNĐ
                        </p>
                        <button
                          onClick={() => router.push(`/booksy/apps/providers/bookings`)}
                          className="mt-2 text-xs text-blue-600 hover:text-blue-800 flex items-center"
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          Chi tiết
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Quick Actions & Activity */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Thao Tác Nhanh</h2>
            </div>
            <div className="p-6">
              <div className="space-y-3">
                <Link
                  href="/booksy/apps/providers/bookings"
                  className="flex items-center justify-between p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <Package className="h-5 w-5 text-blue-600" />
                    <span className="font-medium text-blue-900">Quản Lý Đơn Hàng</span>
                  </div>
                  <ArrowRight className="h-4 w-4 text-blue-600" />
                </Link>

                <Link
                  href="/booksy/apps/providers/workpool"
                  className="flex items-center justify-between p-3 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <Wrench className="h-5 w-5 text-purple-600" />
                    <span className="font-medium text-purple-900">Kho Công Việc</span>
                  </div>
                  <ArrowRight className="h-4 w-4 text-purple-600" />
                </Link>

                <Link
                  href="/booksy/apps/providers/food"
                  className="flex items-center justify-between p-3 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <UtensilsCrossed className="h-5 w-5 text-orange-600" />
                    <span className="font-medium text-orange-900">Quản Lý Thực Đơn</span>
                  </div>
                  <ArrowRight className="h-4 w-4 text-orange-600" />
                </Link>

                <Link
                  href="/booksy/apps/providers/profile"
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <User className="h-5 w-5 text-gray-600" />
                    <span className="font-medium text-gray-900">Cập Nhật Hồ Sơ</span>
                  </div>
                  <ArrowRight className="h-4 w-4 text-gray-600" />
                </Link>

                <button className="w-full flex items-center justify-between p-3 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                  <div className="flex items-center space-x-3">
                    <Phone className="h-5 w-5 text-green-600" />
                    <span className="font-medium text-green-900">Hỗ Trợ Khách Hàng</span>
                  </div>
                  <ArrowRight className="h-4 w-4 text-green-600" />
                </button>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Hoạt Động Gần Đây</h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <div className="p-2 rounded-full bg-green-100">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-900">Đơn hàng hoàn thành</p>
                    <p className="text-sm text-gray-500">Dịch vụ ống nước cho Nguyễn Văn A</p>
                    <p className="text-xs text-gray-400">2 giờ trước</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <div className="p-2 rounded-full bg-blue-100">
                      <Package className="h-4 w-4 text-blue-600" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-900">Đơn hàng mới</p>
                    <p className="text-sm text-gray-500">Trần Thị B đặt dịch vụ điện</p>
                    <p className="text-xs text-gray-400">4 giờ trước</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <div className="p-2 rounded-full bg-yellow-100">
                      <Star className="h-4 w-4 text-yellow-600" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-900">Đánh giá mới</p>
                    <p className="text-sm text-gray-500">Lê Văn C đánh giá 5 sao</p>
                    <p className="text-xs text-gray-400">1 ngày trước</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <div className="p-2 rounded-full bg-purple-100">
                      <DollarSign className="h-4 w-4 text-purple-600" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-900">Thanh toán nhận được</p>
                    <p className="text-sm text-gray-500">800.000 VNĐ từ đơn hàng #001</p>
                    <p className="text-xs text-gray-400">1 ngày trước</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 