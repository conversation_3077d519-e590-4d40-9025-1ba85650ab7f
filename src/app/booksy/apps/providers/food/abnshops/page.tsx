'use client';

import { useState, useEffect } from 'react';
import { 
  Store, 
  Sync, 
  Settings, 
  ExternalLink,
  CheckCircle,
  XCircle,
  AlertCircle,
  Package,
  ShoppingBag,
  TrendingUp,
  ArrowLeft,
  RefreshCw,
  Plus,
  Eye,
  Edit,
  Trash2,
  Globe,
  DollarSign
} from 'lucide-react';
import Link from 'next/link';

interface SyncStats {
  totalRestaurants: number;
  totalMenuItems: number;
  syncedProducts: number;
  totalStores: number;
}

interface Restaurant {
  id: string;
  name: string;
  description: string;
  status: string;
  menuItemsCount: number;
  syncedProductsCount: number;
  lastSyncAt?: string;
  storeId?: string;
}

interface SyncedProduct {
  id: string;
  name: string;
  basePrice: number;
  status: string;
  storeId: string;
  menuItemId: string;
  lastUpdated: string;
}

const ABNShopsIntegrationPage = () => {
  const [stats, setStats] = useState<SyncStats>({
    totalRestaurants: 0,
    totalMenuItems: 0,
    syncedProducts: 0,
    totalStores: 0
  });
  
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [syncedProducts, setSyncedProducts] = useState<SyncedProduct[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSyncing, setIsSyncing] = useState(false);
  const [selectedRestaurant, setSelectedRestaurant] = useState<string>('');
  const [syncSettings, setSyncSettings] = useState({
    autoSync: false,
    syncInterval: 60,
    includeInactive: false,
    replaceExisting: true
  });

  const providerId = 'provider-food-001'; // Mock provider ID

  useEffect(() => {
    fetchIntegrationData();
  }, []);

  const fetchIntegrationData = async () => {
    try {
      setIsLoading(true);
      
      // Mock data for development
      setStats({
        totalRestaurants: 2,
        totalMenuItems: 15,
        syncedProducts: 8,
        totalStores: 1
      });

      setRestaurants([
        {
          id: 'restaurant-001',
          name: 'Phở Hà Nội Truyền Thống',
          description: 'Phở Hà Nội chính hiệu với nước dùng niêu trong suốt 24 giờ',
          status: 'active',
          menuItemsCount: 12,
          syncedProductsCount: 8,
          lastSyncAt: '2024-01-15T10:30:00Z',
          storeId: 'store_food_restaurant-001'
        },
        {
          id: 'restaurant-002',
          name: 'Pizza Italia Express',
          description: 'Pizza Ý chính hiệu với đế bánh mỏng giòn',
          status: 'active',
          menuItemsCount: 8,
          syncedProductsCount: 0,
          lastSyncAt: undefined,
          storeId: undefined
        }
      ]);

      setSyncedProducts([
        {
          id: 'food_menu-item-001',
          name: 'Phở Bò Tái',
          basePrice: 65,
          status: 'active',
          storeId: 'store_food_restaurant-001',
          menuItemId: 'menu-item-001',
          lastUpdated: '2024-01-15T10:30:00Z'
        },
        {
          id: 'food_menu-item-002',
          name: 'Phở Gà',
          basePrice: 60,
          status: 'active',
          storeId: 'store_food_restaurant-001',
          menuItemId: 'menu-item-002',
          lastUpdated: '2024-01-15T10:30:00Z'
        }
      ]);
    } catch (error) {
      console.error('Failed to fetch integration data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const syncRestaurantToABNShops = async (restaurantId: string) => {
    try {
      setIsSyncing(true);
      
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Update restaurant sync status
      setRestaurants(prev => 
        prev.map(restaurant => 
          restaurant.id === restaurantId
            ? {
                ...restaurant,
                syncedProductsCount: restaurant.menuItemsCount,
                lastSyncAt: new Date().toISOString(),
                storeId: restaurant.storeId || `store_food_${restaurantId}`
              }
            : restaurant
        )
      );

      // Update stats
      setStats(prev => ({
        ...prev,
        syncedProducts: prev.syncedProducts + 5,
        totalStores: prev.totalStores + (restaurants.find(r => r.id === restaurantId)?.storeId ? 0 : 1)
      }));

      alert('Đồng bộ thành công!');
    } catch (error) {
      console.error('Failed to sync restaurant:', error);
      alert('Đồng bộ thất bại!');
    } finally {
      setIsSyncing(false);
    }
  };

  const removeSyncedProducts = async (restaurantId: string) => {
    try {
      setIsSyncing(true);
      
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Update restaurant sync status
      setRestaurants(prev => 
        prev.map(restaurant => 
          restaurant.id === restaurantId
            ? {
                ...restaurant,
                syncedProductsCount: 0,
                lastSyncAt: undefined
              }
            : restaurant
        )
      );

      // Remove synced products
      setSyncedProducts(prev => 
        prev.filter(product => 
          !restaurants.find(r => r.id === restaurantId)?.storeId || 
          product.storeId !== restaurants.find(r => r.id === restaurantId)?.storeId
        )
      );

      // Update stats
      const restaurant = restaurants.find(r => r.id === restaurantId);
      setStats(prev => ({
        ...prev,
        syncedProducts: prev.syncedProducts - (restaurant?.syncedProductsCount || 0)
      }));

      alert('Đã xóa sản phẩm đồng bộ!');
    } catch (error) {
      console.error('Failed to remove synced products:', error);
      alert('Xóa thất bại!');
    } finally {
      setIsSyncing(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN') + ' ' + date.toLocaleTimeString('vi-VN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getSyncStatus = (restaurant: Restaurant) => {
    if (restaurant.syncedProductsCount === 0) {
      return { status: 'not_synced', text: 'Chưa đồng bộ', color: 'text-gray-500', icon: XCircle };
    } else if (restaurant.syncedProductsCount === restaurant.menuItemsCount) {
      return { status: 'synced', text: 'Đã đồng bộ', color: 'text-green-600', icon: CheckCircle };
    } else {
      return { status: 'partial', text: 'Đồng bộ một phần', color: 'text-yellow-600', icon: AlertCircle };
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải dữ liệu...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/booksy/apps/providers/food" className="text-gray-500 hover:text-gray-700">
                <ArrowLeft className="w-5 h-5" />
              </Link>
              <Store className="w-6 h-6 text-orange-500" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">Tích Hợp ABN Shops</h1>
                <p className="text-sm text-gray-500">Đồng bộ thực đơn với cửa hàng trực tuyến</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={fetchIntegrationData}
                disabled={isSyncing}
                className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 disabled:opacity-50 flex items-center space-x-2"
              >
                <RefreshCw className={`w-4 h-4 ${isSyncing ? 'animate-spin' : ''}`} />
                <span>Làm mới</span>
              </button>
              <Link
                href="/platforms/abnshops/ecommerce"
                target="_blank"
                className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 flex items-center space-x-2"
              >
                <ExternalLink className="w-4 h-4" />
                <span>Mở ABN Shops</span>
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Nhà hàng</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalRestaurants}</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Store className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Món ăn</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalMenuItems}</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Package className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Đã đồng bộ</p>
                <p className="text-2xl font-bold text-gray-900">{stats.syncedProducts}</p>
              </div>
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <Sync className="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Cửa hàng</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalStores}</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <ShoppingBag className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Restaurants List */}
        <div className="bg-white rounded-lg shadow-sm mb-8">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Nhà Hàng & Trạng Thái Đồng Bộ</h3>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Nhà hàng
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Món ăn
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Trạng thái
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Lần cuối đồng bộ
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Hành động
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {restaurants.map((restaurant) => {
                  const syncStatus = getSyncStatus(restaurant);
                  const StatusIcon = syncStatus.icon;
                  
                  return (
                    <tr key={restaurant.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{restaurant.name}</div>
                          <div className="text-sm text-gray-500">{restaurant.description.substring(0, 50)}...</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {restaurant.syncedProductsCount}/{restaurant.menuItemsCount} đã đồng bộ
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`flex items-center space-x-1 ${syncStatus.color}`}>
                          <StatusIcon className="w-4 h-4" />
                          <span className="text-sm">{syncStatus.text}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {restaurant.lastSyncAt ? formatDate(restaurant.lastSyncAt) : 'Chưa đồng bộ'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => syncRestaurantToABNShops(restaurant.id)}
                            disabled={isSyncing}
                            className="bg-orange-500 text-white px-3 py-1 rounded hover:bg-orange-600 disabled:opacity-50 flex items-center space-x-1"
                          >
                            <Sync className="w-4 h-4" />
                            <span>Đồng bộ</span>
                          </button>
                          
                          {restaurant.storeId && (
                            <>
                              <Link
                                href={`/platforms/abnshops/ecommerce/storefront/${restaurant.storeId}`}
                                target="_blank"
                                className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 flex items-center space-x-1"
                              >
                                <Globe className="w-4 h-4" />
                                <span>Xem</span>
                              </Link>
                              
                              <button
                                onClick={() => removeSyncedProducts(restaurant.id)}
                                disabled={isSyncing}
                                className="bg-red-500 text-white px-3 py-1 rounded hover:bg-red-600 disabled:opacity-50 flex items-center space-x-1"
                              >
                                <Trash2 className="w-4 h-4" />
                                <span>Xóa</span>
                              </button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>

        {/* Synced Products */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Sản Phẩm Đã Đồng Bộ</h3>
          </div>
          
          {syncedProducts.length === 0 ? (
            <div className="p-12 text-center">
              <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Chưa có sản phẩm nào được đồng bộ</h3>
              <p className="text-gray-500">Đồng bộ thực đơn nhà hàng để tạo cửa hàng trực tuyến</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Sản phẩm
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Giá
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Trạng thái
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Cập nhật cuối
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Hành động
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {syncedProducts.map((product) => (
                    <tr key={product.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{product.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{product.basePrice.toLocaleString()}k VNĐ</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          product.status === 'active' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {product.status === 'active' ? 'Hoạt động' : 'Không hoạt động'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(product.lastUpdated)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <Link
                            href={`/platforms/abnshops/ecommerce/products/${product.id}`}
                            target="_blank"
                            className="text-blue-600 hover:text-blue-900"
                          >
                            <Eye className="w-4 h-4" />
                          </Link>
                          <Link
                            href={`/platforms/abnshops/ecommerce/products/${product.id}/edit`}
                            target="_blank"
                            className="text-orange-600 hover:text-orange-900"
                          >
                            <Edit className="w-4 h-4" />
                          </Link>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ABNShopsIntegrationPage;
