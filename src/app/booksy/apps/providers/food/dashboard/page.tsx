'use client';

import { useState, useEffect } from 'react';
import { 
  Bar<PERSON>hart3, 
  TrendingUp, 
  TrendingDown,
  DollarSign, 
  ShoppingBag, 
  Clock, 
  Star,
  Users,
  Package,
  AlertCircle,
  CheckCircle,
  XCircle,
  Eye,
  MessageSquare,
  Phone,
  ArrowLeft,
  Calendar,
  Filter,
  Download
} from 'lucide-react';
import Link from 'next/link';

interface OrderStats {
  total: number;
  pending: number;
  preparing: number;
  ready: number;
  completed: number;
  cancelled: number;
}

interface SalesData {
  today: number;
  yesterday: number;
  thisWeek: number;
  lastWeek: number;
  thisMonth: number;
  lastMonth: number;
}

interface Order {
  id: string;
  orderNumber: string;
  customerInfo: {
    name: string;
    phone: string;
  };
  items: Array<{
    name: string;
    quantity: number;
    price: number;
  }>;
  total: number;
  status: string;
  createdAt: string;
  estimatedTime: number;
  paymentMethod: string;
  deliveryInfo?: {
    address: { fullAddress: string };
  };
}

const FoodProviderDashboard = () => {
  const [orderStats, setOrderStats] = useState<OrderStats>({
    total: 0,
    pending: 0,
    preparing: 0,
    ready: 0,
    completed: 0,
    cancelled: 0
  });
  
  const [salesData, setSalesData] = useState<SalesData>({
    today: 0,
    yesterday: 0,
    thisWeek: 0,
    lastWeek: 0,
    thisMonth: 0,
    lastMonth: 0
  });
  
  const [recentOrders, setRecentOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTimeRange, setSelectedTimeRange] = useState('today');

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Mock data for development
        setOrderStats({
          total: 156,
          pending: 8,
          preparing: 12,
          ready: 3,
          completed: 128,
          cancelled: 5
        });

        setSalesData({
          today: 2450000,
          yesterday: 1890000,
          thisWeek: 15600000,
          lastWeek: 12300000,
          thisMonth: 45200000,
          lastMonth: 38900000
        });

        setRecentOrders([
          {
            id: 'food-order-001',
            orderNumber: 'FO240115001',
            customerInfo: {
              name: 'Nguyễn Văn An',
              phone: '+84 912 345 678'
            },
            items: [
              { name: 'Phở Bò Tái', quantity: 2, price: 85000 },
              { name: 'Cà Phê Đen', quantity: 1, price: 25000 }
            ],
            total: 195000,
            status: 'preparing',
            createdAt: '2024-01-15T10:15:00Z',
            estimatedTime: 15,
            paymentMethod: 'momo',
            deliveryInfo: {
              address: { fullAddress: 'Tòa T1, Times City, Minh Khai, Hai Bà Trưng, Hà Nội' }
            }
          },
          {
            id: 'food-order-002',
            orderNumber: 'FO240115002',
            customerInfo: {
              name: 'Lê Thị Bình',
              phone: '+84 913 456 789'
            },
            items: [
              { name: 'Phở Gà', quantity: 1, price: 60000 }
            ],
            total: 60000,
            status: 'pending',
            createdAt: '2024-01-15T10:30:00Z',
            estimatedTime: 12,
            paymentMethod: 'cash'
          },
          {
            id: 'food-order-003',
            orderNumber: 'FO240115003',
            customerInfo: {
              name: 'Trần Văn Cường',
              phone: '+84 914 567 890'
            },
            items: [
              { name: 'Phở Bò Tái', quantity: 1, price: 65000 },
              { name: 'Cà Phê Sữa', quantity: 2, price: 30000 }
            ],
            total: 125000,
            status: 'ready',
            createdAt: '2024-01-15T09:45:00Z',
            estimatedTime: 0,
            paymentMethod: 'zalopay'
          }
        ]);
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'preparing':
        return 'bg-blue-100 text-blue-800';
      case 'ready':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4" />;
      case 'preparing':
        return <Package className="w-4 h-4" />;
      case 'ready':
        return <CheckCircle className="w-4 h-4" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      case 'cancelled':
        return <XCircle className="w-4 h-4" />;
      default:
        return <AlertCircle className="w-4 h-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Chờ xác nhận';
      case 'preparing':
        return 'Đang chuẩn bị';
      case 'ready':
        return 'Sẵn sàng';
      case 'completed':
        return 'Hoàn thành';
      case 'cancelled':
        return 'Đã hủy';
      default:
        return status;
    }
  };

  const calculateGrowth = (current: number, previous: number) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  };

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('vi-VN') + 'đ';
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('vi-VN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải dữ liệu...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/booksy/apps/providers/food" className="text-gray-500 hover:text-gray-700">
                <ArrowLeft className="w-5 h-5" />
              </Link>
              <BarChart3 className="w-6 h-6 text-orange-500" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">Dashboard Nhà Hàng</h1>
                <p className="text-sm text-gray-500">Phở Hà Nội Truyền Thống</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <select
                value={selectedTimeRange}
                onChange={(e) => setSelectedTimeRange(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                <option value="today">Hôm nay</option>
                <option value="week">Tuần này</option>
                <option value="month">Tháng này</option>
              </select>
              <button className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 flex items-center space-x-2">
                <Download className="w-4 h-4" />
                <span>Xuất báo cáo</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Revenue Today */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Doanh thu hôm nay</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(salesData.today)}</p>
                <div className="flex items-center mt-2">
                  {salesData.today > salesData.yesterday ? (
                    <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="w-4 h-4 text-red-500 mr-1" />
                  )}
                  <span className={`text-sm ${salesData.today > salesData.yesterday ? 'text-green-600' : 'text-red-600'}`}>
                    {Math.abs(calculateGrowth(salesData.today, salesData.yesterday)).toFixed(1)}%
                  </span>
                  <span className="text-sm text-gray-500 ml-1">so với hôm qua</span>
                </div>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-green-600" />
              </div>
            </div>
          </div>

          {/* Total Orders */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Tổng đơn hàng</p>
                <p className="text-2xl font-bold text-gray-900">{orderStats.total}</p>
                <div className="flex items-center mt-2">
                  <span className="text-sm text-gray-500">
                    {orderStats.pending + orderStats.preparing} đang xử lý
                  </span>
                </div>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <ShoppingBag className="w-6 h-6 text-blue-600" />
              </div>
            </div>
          </div>

          {/* Average Rating */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Đánh giá trung bình</p>
                <p className="text-2xl font-bold text-gray-900">4.8</p>
                <div className="flex items-center mt-2">
                  <Star className="w-4 h-4 text-yellow-400 fill-current mr-1" />
                  <span className="text-sm text-gray-500">324 đánh giá</span>
                </div>
              </div>
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Star className="w-6 h-6 text-yellow-600" />
              </div>
            </div>
          </div>

          {/* Response Time */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Thời gian phản hồi</p>
                <p className="text-2xl font-bold text-gray-900">5 phút</p>
                <div className="flex items-center mt-2">
                  <span className="text-sm text-green-600">Tốt</span>
                </div>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Clock className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Order Status Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <div className="lg:col-span-2 bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Trạng Thái Đơn Hàng</h3>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="text-center">
                <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <Clock className="w-8 h-8 text-yellow-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{orderStats.pending}</p>
                <p className="text-sm text-gray-600">Chờ xác nhận</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <Package className="w-8 h-8 text-blue-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{orderStats.preparing}</p>
                <p className="text-sm text-gray-600">Đang chuẩn bị</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{orderStats.ready}</p>
                <p className="text-sm text-gray-600">Sẵn sàng</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <CheckCircle className="w-8 h-8 text-gray-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{orderStats.completed}</p>
                <p className="text-sm text-gray-600">Hoàn thành</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-2">
                  <XCircle className="w-8 h-8 text-red-600" />
                </div>
                <p className="text-2xl font-bold text-gray-900">{orderStats.cancelled}</p>
                <p className="text-sm text-gray-600">Đã hủy</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Doanh Thu Tháng Này</h3>
            <div className="text-center">
              <p className="text-3xl font-bold text-gray-900 mb-2">{formatCurrency(salesData.thisMonth)}</p>
              <div className="flex items-center justify-center">
                {salesData.thisMonth > salesData.lastMonth ? (
                  <TrendingUp className="w-5 h-5 text-green-500 mr-2" />
                ) : (
                  <TrendingDown className="w-5 h-5 text-red-500 mr-2" />
                )}
                <span className={`text-sm ${salesData.thisMonth > salesData.lastMonth ? 'text-green-600' : 'text-red-600'}`}>
                  {Math.abs(calculateGrowth(salesData.thisMonth, salesData.lastMonth)).toFixed(1)}%
                </span>
                <span className="text-sm text-gray-500 ml-1">so với tháng trước</span>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Orders */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Đơn Hàng Gần Đây</h3>
              <Link
                href="/booksy/apps/providers/food/orders"
                className="text-orange-600 hover:text-orange-800 text-sm font-medium"
              >
                Xem tất cả
              </Link>
            </div>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Đơn hàng
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Khách hàng
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Món ăn
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tổng tiền
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Trạng thái
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Thời gian
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Hành động
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {recentOrders.map((order) => (
                  <tr key={order.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{order.orderNumber}</div>
                        <div className="text-sm text-gray-500">{order.paymentMethod}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{order.customerInfo.name}</div>
                        <div className="text-sm text-gray-500">{order.customerInfo.phone}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900">
                        {order.items.map((item, index) => (
                          <div key={index}>
                            {item.quantity}x {item.name}
                          </div>
                        ))}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{formatCurrency(order.total)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                        {getStatusIcon(order.status)}
                        <span className="ml-1">{getStatusText(order.status)}</span>
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatTime(order.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button className="text-orange-600 hover:text-orange-900">
                          <Eye className="w-4 h-4" />
                        </button>
                        <button className="text-blue-600 hover:text-blue-900">
                          <MessageSquare className="w-4 h-4" />
                        </button>
                        <button className="text-green-600 hover:text-green-900">
                          <Phone className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FoodProviderDashboard;
