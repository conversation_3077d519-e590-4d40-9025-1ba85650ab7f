'use client';

import { useState, useEffect } from 'react';
import { 
  Package, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Phone,
  MessageSquare,
  MapPin,
  DollarSign,
  User,
  ArrowLeft,
  Filter,
  Search,
  RefreshCw,
  Eye,
  Printer,
  MoreHorizontal
} from 'lucide-react';
import Link from 'next/link';

interface Order {
  id: string;
  orderNumber: string;
  customerInfo: {
    name: string;
    phone: string;
    email: string;
  };
  items: Array<{
    id: string;
    name: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    customizations?: Array<{
      name: string;
      selectedOptions?: Array<{ name: string; priceAdjustment: number }>;
    }>;
    specialInstructions?: string;
  }>;
  pricing: {
    subtotal: number;
    deliveryFee: number;
    serviceFee: number;
    discount: number;
    total: number;
  };
  deliveryInfo?: {
    address: { fullAddress: string };
    deliveryInstructions?: string;
    estimatedDeliveryTime: { min: number; max: number };
  };
  paymentInfo: {
    method: string;
    status: string;
    amount: number;
  };
  status: string;
  timeline: {
    orderedAt: string;
    confirmedAt?: string;
    preparingAt?: string;
    readyAt?: string;
    pickedUpAt?: string;
    deliveredAt?: string;
    completedAt?: string;
  };
  estimatedPreparationTime: number;
  specialRequests: string[];
  createdAt: string;
}

const OrderManagementPage = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);

  const statusOptions = [
    { value: 'all', label: 'Tất cả', count: 0 },
    { value: 'pending', label: 'Chờ xác nhận', count: 0 },
    { value: 'confirmed', label: 'Đã xác nhận', count: 0 },
    { value: 'preparing', label: 'Đang chuẩn bị', count: 0 },
    { value: 'ready', label: 'Sẵn sàng', count: 0 },
    { value: 'picked_up', label: 'Đã lấy hàng', count: 0 },
    { value: 'delivered', label: 'Đã giao', count: 0 },
    { value: 'completed', label: 'Hoàn thành', count: 0 },
    { value: 'cancelled', label: 'Đã hủy', count: 0 }
  ];

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        // Mock data for development
        const mockOrders: Order[] = [
          {
            id: 'food-order-001',
            orderNumber: 'FO240115001',
            customerInfo: {
              name: 'Nguyễn Văn An',
              phone: '+84 912 345 678',
              email: '<EMAIL>'
            },
            items: [
              {
                id: 'order-item-001',
                name: 'Phở Bò Tái',
                quantity: 2,
                unitPrice: 85000,
                totalPrice: 170000,
                customizations: [
                  {
                    name: 'Mức độ chín của thịt',
                    selectedOptions: [{ name: 'Tái', priceAdjustment: 0 }]
                  }
                ],
                specialInstructions: 'Ít muối, không hành tây'
              }
            ],
            pricing: {
              subtotal: 170000,
              deliveryFee: 15000,
              serviceFee: 5000,
              discount: 0,
              total: 190000
            },
            deliveryInfo: {
              address: { fullAddress: 'Tòa T1, Times City, Minh Khai, Hai Bà Trưng, Hà Nội' },
              deliveryInstructions: 'Gọi điện khi đến tầng trệt',
              estimatedDeliveryTime: { min: 25, max: 35 }
            },
            paymentInfo: {
              method: 'momo',
              status: 'paid',
              amount: 190000
            },
            status: 'preparing',
            timeline: {
              orderedAt: '2024-01-15T10:15:00Z',
              confirmedAt: '2024-01-15T10:17:00Z',
              preparingAt: '2024-01-15T10:18:00Z'
            },
            estimatedPreparationTime: 15,
            specialRequests: ['Không cần đũa', 'Gói riêng nước mắm'],
            createdAt: '2024-01-15T10:15:00Z'
          },
          {
            id: 'food-order-002',
            orderNumber: 'FO240115002',
            customerInfo: {
              name: 'Lê Thị Bình',
              phone: '+84 913 456 789',
              email: '<EMAIL>'
            },
            items: [
              {
                id: 'order-item-002',
                name: 'Phở Gà',
                quantity: 1,
                unitPrice: 60000,
                totalPrice: 60000
              }
            ],
            pricing: {
              subtotal: 60000,
              deliveryFee: 15000,
              serviceFee: 3000,
              discount: 0,
              total: 78000
            },
            paymentInfo: {
              method: 'cash',
              status: 'pending',
              amount: 78000
            },
            status: 'pending',
            timeline: {
              orderedAt: '2024-01-15T10:30:00Z'
            },
            estimatedPreparationTime: 12,
            specialRequests: [],
            createdAt: '2024-01-15T10:30:00Z'
          }
        ];

        setOrders(mockOrders);
      } catch (error) {
        console.error('Failed to fetch orders:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrders();
  }, []);

  const updateOrderStatus = async (orderId: string, newStatus: string) => {
    try {
      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === orderId
            ? {
                ...order,
                status: newStatus,
                timeline: {
                  ...order.timeline,
                  [getTimelineField(newStatus)]: new Date().toISOString()
                }
              }
            : order
        )
      );
    } catch (error) {
      console.error('Failed to update order status:', error);
    }
  };

  const getTimelineField = (status: string): string => {
    const mapping: { [key: string]: string } = {
      'confirmed': 'confirmedAt',
      'preparing': 'preparingAt',
      'ready': 'readyAt',
      'picked_up': 'pickedUpAt',
      'delivered': 'deliveredAt',
      'completed': 'completedAt'
    };
    return mapping[status] || '';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'preparing':
        return 'bg-orange-100 text-orange-800 border-orange-300';
      case 'ready':
        return 'bg-green-100 text-green-800 border-green-300';
      case 'picked_up':
        return 'bg-purple-100 text-purple-800 border-purple-300';
      case 'delivered':
        return 'bg-indigo-100 text-indigo-800 border-indigo-300';
      case 'completed':
        return 'bg-gray-100 text-gray-800 border-gray-300';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4" />;
      case 'confirmed':
      case 'preparing':
        return <Package className="w-4 h-4" />;
      case 'ready':
      case 'picked_up':
      case 'delivered':
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      case 'cancelled':
        return <XCircle className="w-4 h-4" />;
      default:
        return <AlertCircle className="w-4 h-4" />;
    }
  };

  const getNextStatusOptions = (currentStatus: string) => {
    switch (currentStatus) {
      case 'pending':
        return [
          { value: 'confirmed', label: 'Xác nhận đơn hàng', color: 'bg-blue-500' },
          { value: 'cancelled', label: 'Hủy đơn hàng', color: 'bg-red-500' }
        ];
      case 'confirmed':
        return [
          { value: 'preparing', label: 'Bắt đầu chuẩn bị', color: 'bg-orange-500' }
        ];
      case 'preparing':
        return [
          { value: 'ready', label: 'Sẵn sàng giao hàng', color: 'bg-green-500' }
        ];
      case 'ready':
        return [
          { value: 'picked_up', label: 'Đã được lấy hàng', color: 'bg-purple-500' }
        ];
      case 'picked_up':
        return [
          { value: 'delivered', label: 'Đã giao thành công', color: 'bg-indigo-500' }
        ];
      case 'delivered':
        return [
          { value: 'completed', label: 'Hoàn thành đơn hàng', color: 'bg-gray-500' }
        ];
      default:
        return [];
    }
  };

  const filteredOrders = orders.filter(order => {
    const matchesStatus = selectedStatus === 'all' || order.status === selectedStatus;
    const matchesSearch = order.orderNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.customerInfo.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         order.customerInfo.phone.includes(searchQuery);
    return matchesStatus && matchesSearch;
  });

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('vi-VN') + 'đ';
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('vi-VN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải đơn hàng...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/booksy/apps/providers/food" className="text-gray-500 hover:text-gray-700">
                <ArrowLeft className="w-5 h-5" />
              </Link>
              <Package className="w-6 h-6 text-orange-500" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">Quản Lý Đơn Hàng</h1>
                <p className="text-sm text-gray-500">{filteredOrders.length} đơn hàng</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center space-x-2">
                <RefreshCw className="w-4 h-4" />
                <span>Làm mới</span>
              </button>
              <button className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 flex items-center space-x-2">
                <Printer className="w-4 h-4" />
                <span>In đơn hàng</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div className="flex space-x-2 overflow-x-auto">
              {statusOptions.map((status) => (
                <button
                  key={status.value}
                  onClick={() => setSelectedStatus(status.value)}
                  className={`px-4 py-2 rounded-lg whitespace-nowrap text-sm font-medium ${
                    selectedStatus === status.value
                      ? 'bg-orange-500 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {status.label}
                  {status.count > 0 && (
                    <span className="ml-2 bg-white bg-opacity-20 px-2 py-1 rounded-full text-xs">
                      {status.count}
                    </span>
                  )}
                </button>
              ))}
            </div>
            
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Tìm kiếm đơn hàng, khách hàng..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 w-64"
              />
            </div>
          </div>
        </div>

        {/* Orders List */}
        <div className="space-y-4">
          {filteredOrders.map((order) => (
            <div key={order.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{order.orderNumber}</h3>
                      <p className="text-sm text-gray-500">
                        {formatDate(order.createdAt)} lúc {formatTime(order.createdAt)}
                      </p>
                    </div>
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(order.status)}`}>
                      {getStatusIcon(order.status)}
                      <span className="ml-1">{statusOptions.find(s => s.value === order.status)?.label}</span>
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => {
                        setSelectedOrder(order);
                        setShowOrderDetails(true);
                      }}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <Eye className="w-5 h-5" />
                    </button>
                    <button className="text-gray-400 hover:text-gray-600">
                      <MoreHorizontal className="w-5 h-5" />
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Customer Info */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2 flex items-center space-x-1">
                      <User className="w-4 h-4" />
                      <span>Khách hàng</span>
                    </h4>
                    <div className="text-sm text-gray-600">
                      <p className="font-medium">{order.customerInfo.name}</p>
                      <p>{order.customerInfo.phone}</p>
                      {order.deliveryInfo && (
                        <p className="mt-1 flex items-start space-x-1">
                          <MapPin className="w-4 h-4 mt-0.5 flex-shrink-0" />
                          <span>{order.deliveryInfo.address.fullAddress}</span>
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Order Items */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Món ăn</h4>
                    <div className="text-sm text-gray-600 space-y-1">
                      {order.items.map((item, index) => (
                        <div key={index} className="flex justify-between">
                          <span>{item.quantity}x {item.name}</span>
                          <span>{formatCurrency(item.totalPrice)}</span>
                        </div>
                      ))}
                      {order.specialRequests.length > 0 && (
                        <div className="mt-2 pt-2 border-t border-gray-200">
                          <p className="text-xs text-gray-500">Yêu cầu đặc biệt:</p>
                          {order.specialRequests.map((request, index) => (
                            <p key={index} className="text-xs text-gray-600">• {request}</p>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Payment & Actions */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2 flex items-center space-x-1">
                      <DollarSign className="w-4 h-4" />
                      <span>Thanh toán</span>
                    </h4>
                    <div className="text-sm text-gray-600 mb-4">
                      <p className="font-medium text-lg text-gray-900">{formatCurrency(order.pricing.total)}</p>
                      <p className="capitalize">{order.paymentInfo.method}</p>
                      <p className={`text-xs ${order.paymentInfo.status === 'paid' ? 'text-green-600' : 'text-yellow-600'}`}>
                        {order.paymentInfo.status === 'paid' ? 'Đã thanh toán' : 'Chưa thanh toán'}
                      </p>
                    </div>

                    {/* Action Buttons */}
                    <div className="space-y-2">
                      {getNextStatusOptions(order.status).map((action) => (
                        <button
                          key={action.value}
                          onClick={() => updateOrderStatus(order.id, action.value)}
                          className={`w-full ${action.color} text-white px-3 py-2 rounded-lg hover:opacity-90 text-sm font-medium`}
                        >
                          {action.label}
                        </button>
                      ))}
                      
                      <div className="flex space-x-2 mt-2">
                        <button className="flex-1 bg-gray-100 text-gray-700 px-3 py-2 rounded-lg hover:bg-gray-200 flex items-center justify-center space-x-1">
                          <Phone className="w-4 h-4" />
                          <span>Gọi</span>
                        </button>
                        <button className="flex-1 bg-gray-100 text-gray-700 px-3 py-2 rounded-lg hover:bg-gray-200 flex items-center justify-center space-x-1">
                          <MessageSquare className="w-4 h-4" />
                          <span>Nhắn tin</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredOrders.length === 0 && (
          <div className="bg-white rounded-lg shadow-sm p-12 text-center">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Không có đơn hàng</h3>
            <p className="text-gray-500">
              {selectedStatus === 'all' 
                ? 'Chưa có đơn hàng nào được tạo'
                : `Không có đơn hàng nào ở trạng thái "${statusOptions.find(s => s.value === selectedStatus)?.label}"`
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderManagementPage;
