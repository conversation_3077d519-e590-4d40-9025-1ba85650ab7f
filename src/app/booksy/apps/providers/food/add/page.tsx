'use client';

import { useState } from 'react';
import { 
  ArrowLef<PERSON>, 
  Save, 
  Plus, 
  Trash2, 
  Upload,
  Clock,
  DollarSign,
  Tag,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import Link from 'next/link';

interface MenuItemForm {
  name: string;
  description: string;
  shortDescription: string;
  category: string;
  subcategory: string;
  cuisineType: string;
  basePrice: number;
  variants: Array<{
    id: string;
    name: string;
    price: number;
    description: string;
  }>;
  preparationTime: {
    min: number;
    max: number;
  };
  ingredients: Array<{
    name: string;
    isMain: boolean;
    allergens: string[];
  }>;
  allergens: string[];
  dietaryInfo: {
    isVegetarian: boolean;
    isVegan: boolean;
    isGlutenFree: boolean;
    isHalal: boolean;
    isDairyFree: boolean;
    isNutFree: boolean;
    isSpicy: boolean;
    spicyLevel: number;
  };
  availability: {
    isAvailable: boolean;
    availableFrom: string;
    availableTo: string;
    daysAvailable: string[];
  };
  customizations: Array<{
    id: string;
    name: string;
    type: 'single_choice' | 'multiple_choice' | 'text_input';
    isRequired: boolean;
    options?: Array<{
      id: string;
      name: string;
      priceAdjustment: number;
    }>;
  }>;
  tags: string[];
  isSignature: boolean;
  isPopular: boolean;
  isFeatured: boolean;
  status: 'active' | 'inactive';
}

const AddMenuItemPage = () => {
  const [formData, setFormData] = useState<MenuItemForm>({
    name: '',
    description: '',
    shortDescription: '',
    category: 'main_course',
    subcategory: 'noodles',
    cuisineType: 'vietnamese',
    basePrice: 0,
    variants: [
      { id: 'variant-1', name: 'Size Nhỏ', price: 0, description: '' }
    ],
    preparationTime: { min: 10, max: 15 },
    ingredients: [
      { name: '', isMain: true, allergens: [] }
    ],
    allergens: [],
    dietaryInfo: {
      isVegetarian: false,
      isVegan: false,
      isGlutenFree: false,
      isHalal: false,
      isDairyFree: false,
      isNutFree: false,
      isSpicy: false,
      spicyLevel: 0
    },
    availability: {
      isAvailable: true,
      availableFrom: '06:00',
      availableTo: '22:00',
      daysAvailable: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
    },
    customizations: [],
    tags: [],
    isSignature: false,
    isPopular: false,
    isFeatured: false,
    status: 'active'
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const categories = [
    { id: 'main_course', name: 'Món Chính' },
    { id: 'appetizer', name: 'Khai Vị' },
    { id: 'soup', name: 'Canh & Súp' },
    { id: 'dessert', name: 'Tráng Miệng' },
    { id: 'beverage', name: 'Đồ Uống' },
    { id: 'snack', name: 'Đồ Ăn Vặt' }
  ];

  const subcategories = {
    main_course: [
      { id: 'noodles', name: 'Mì & Phở' },
      { id: 'rice', name: 'Cơm' },
      { id: 'pizza', name: 'Pizza' },
      { id: 'burger', name: 'Burger' },
      { id: 'fried_chicken', name: 'Gà Rán' },
      { id: 'seafood', name: 'Hải Sản' },
      { id: 'vegetarian', name: 'Chay' }
    ],
    appetizer: [
      { id: 'salad', name: 'Salad' },
      { id: 'spring_rolls', name: 'Nem Cuốn' }
    ],
    beverage: [
      { id: 'coffee', name: 'Cà Phê' },
      { id: 'milk_tea', name: 'Trà Sữa' },
      { id: 'fresh_juice', name: 'Nước Ép' }
    ],
    dessert: [
      { id: 'cake', name: 'Bánh Ngọt' },
      { id: 'ice_cream', name: 'Kem' }
    ]
  };

  const cuisineTypes = [
    { id: 'vietnamese', name: 'Món Việt' },
    { id: 'italian', name: 'Món Ý' },
    { id: 'chinese', name: 'Món Trung Hoa' },
    { id: 'japanese', name: 'Món Nhật' },
    { id: 'korean', name: 'Món Hàn' },
    { id: 'thai', name: 'Món Thái' },
    { id: 'western', name: 'Món Âu' }
  ];

  const allergensList = [
    { id: 'gluten', name: 'Gluten' },
    { id: 'dairy', name: 'Sữa' },
    { id: 'eggs', name: 'Trứng' },
    { id: 'nuts', name: 'Hạt' },
    { id: 'peanuts', name: 'Đậu Phộng' },
    { id: 'soy', name: 'Đậu Nành' },
    { id: 'fish', name: 'Cá' },
    { id: 'shellfish', name: 'Tôm Cua' }
  ];

  const addVariant = () => {
    const newVariant = {
      id: `variant-${Date.now()}`,
      name: '',
      price: formData.basePrice,
      description: ''
    };
    setFormData(prev => ({
      ...prev,
      variants: [...prev.variants, newVariant]
    }));
  };

  const removeVariant = (index: number) => {
    if (formData.variants.length > 1) {
      setFormData(prev => ({
        ...prev,
        variants: prev.variants.filter((_, i) => i !== index)
      }));
    }
  };

  const addIngredient = () => {
    const newIngredient = {
      name: '',
      isMain: false,
      allergens: []
    };
    setFormData(prev => ({
      ...prev,
      ingredients: [...prev.ingredients, newIngredient]
    }));
  };

  const removeIngredient = (index: number) => {
    if (formData.ingredients.length > 1) {
      setFormData(prev => ({
        ...prev,
        ingredients: prev.ingredients.filter((_, i) => i !== index)
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock success
      setSubmitStatus('success');
      
      // Redirect after success
      setTimeout(() => {
        window.location.href = '/booksy/apps/providers/food';
      }, 1500);
    } catch (error) {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link href="/booksy/apps/providers/food" className="text-gray-500 hover:text-gray-700">
                <ArrowLeft className="w-5 h-5" />
              </Link>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">Thêm Món Mới</h1>
                <p className="text-sm text-gray-500">Tạo món ăn mới cho thực đơn</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              {submitStatus === 'success' && (
                <div className="flex items-center space-x-2 text-green-600">
                  <CheckCircle className="w-5 h-5" />
                  <span className="text-sm">Đã lưu thành công!</span>
                </div>
              )}
              {submitStatus === 'error' && (
                <div className="flex items-center space-x-2 text-red-600">
                  <AlertCircle className="w-5 h-5" />
                  <span className="text-sm">Có lỗi xảy ra!</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Basic Information */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Thông Tin Cơ Bản</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tên món ăn *
                </label>
                <input
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  placeholder="Ví dụ: Phở Bò Tái"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Danh mục *
                </label>
                <select
                  required
                  value={formData.category}
                  onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                >
                  {categories.map(cat => (
                    <option key={cat.id} value={cat.id}>{cat.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Danh mục con *
                </label>
                <select
                  required
                  value={formData.subcategory}
                  onChange={(e) => setFormData(prev => ({ ...prev, subcategory: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                >
                  {(subcategories[formData.category as keyof typeof subcategories] || []).map(subcat => (
                    <option key={subcat.id} value={subcat.id}>{subcat.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Loại ẩm thực *
                </label>
                <select
                  required
                  value={formData.cuisineType}
                  onChange={(e) => setFormData(prev => ({ ...prev, cuisineType: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                >
                  {cuisineTypes.map(cuisine => (
                    <option key={cuisine.id} value={cuisine.id}>{cuisine.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Giá cơ bản (VNĐ) *
                </label>
                <input
                  type="number"
                  required
                  min="0"
                  value={formData.basePrice}
                  onChange={(e) => setFormData(prev => ({ ...prev, basePrice: parseInt(e.target.value) || 0 }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  placeholder="50000"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Mô tả ngắn *
                </label>
                <input
                  type="text"
                  required
                  value={formData.shortDescription}
                  onChange={(e) => setFormData(prev => ({ ...prev, shortDescription: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  placeholder="Mô tả ngắn gọn về món ăn"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Mô tả chi tiết *
                </label>
                <textarea
                  required
                  rows={4}
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  placeholder="Mô tả chi tiết về món ăn, nguyên liệu, cách chế biến..."
                />
              </div>
            </div>
          </div>

          {/* Variants */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900">Phiên Bản Món Ăn</h2>
              <button
                type="button"
                onClick={addVariant}
                className="bg-orange-500 text-white px-3 py-1 rounded-lg hover:bg-orange-600 flex items-center space-x-1"
              >
                <Plus className="w-4 h-4" />
                <span>Thêm</span>
              </button>
            </div>

            <div className="space-y-4">
              {formData.variants.map((variant, index) => (
                <div key={variant.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-medium text-gray-900">Phiên bản {index + 1}</h3>
                    {formData.variants.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeVariant(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Tên phiên bản
                      </label>
                      <input
                        type="text"
                        value={variant.name}
                        onChange={(e) => {
                          const newVariants = [...formData.variants];
                          newVariants[index].name = e.target.value;
                          setFormData(prev => ({ ...prev, variants: newVariants }));
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                        placeholder="Size Nhỏ"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Giá (VNĐ)
                      </label>
                      <input
                        type="number"
                        min="0"
                        value={variant.price}
                        onChange={(e) => {
                          const newVariants = [...formData.variants];
                          newVariants[index].price = parseInt(e.target.value) || 0;
                          setFormData(prev => ({ ...prev, variants: newVariants }));
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Mô tả
                      </label>
                      <input
                        type="text"
                        value={variant.description}
                        onChange={(e) => {
                          const newVariants = [...formData.variants];
                          newVariants[index].description = e.target.value;
                          setFormData(prev => ({ ...prev, variants: newVariants }));
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                        placeholder="Phù hợp cho 1 người"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Preparation Time */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Thời Gian Chuẩn Bị</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Thời gian tối thiểu (phút)
                </label>
                <input
                  type="number"
                  min="1"
                  value={formData.preparationTime.min}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    preparationTime: { ...prev.preparationTime, min: parseInt(e.target.value) || 1 }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Thời gian tối đa (phút)
                </label>
                <input
                  type="number"
                  min="1"
                  value={formData.preparationTime.max}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    preparationTime: { ...prev.preparationTime, max: parseInt(e.target.value) || 1 }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                />
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <Link
              href="/booksy/apps/providers/food"
              className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              Hủy
            </Link>
            <button
              type="submit"
              disabled={isSubmitting}
              className="bg-orange-500 text-white px-6 py-2 rounded-lg hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Đang lưu...</span>
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  <span>Lưu Món Ăn</span>
                </>
              )}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default AddMenuItemPage;
