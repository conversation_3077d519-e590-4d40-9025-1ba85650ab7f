# Alba Free - Standalone Layout Documentation

## 🎯 Overview

Alba Free now has its own standalone layout that **does not inherit** the parent Booksy layout (header, sidebar, navigation menu). This creates a clean, mobile-first experience similar to the minirent tenants app.

## 📁 Layout Structure

```
src/app/booksy/apps/albafree/
├── layout.tsx              # Standalone layout (overrides parent)
├── globals.css             # Alba Free specific styles
├── page.tsx                # Main Alba Free page
├── profile/[id]/page.tsx   # User profile pages
├── job/[id]/page.tsx       # Job detail pages
├── post-job/page.tsx       # Job posting page
└── LAYOUT.md               # This documentation
```

## 🔧 Key Features

### 1. **Independent Layout**
- **No parent inheritance**: Completely bypasses Booksy's header/sidebar/footer
- **Mobile-first design**: Optimized for mobile devices
- **Standalone experience**: Works as a separate app within the Booksy ecosystem

### 2. **PWA Ready**
- **Manifest file**: `/public/albafree-manifest.json`
- **App icons**: Prepared for various sizes
- **Shortcuts**: Quick actions for job search and posting
- **Installable**: Can be installed as a standalone app

### 3. **Vietnamese Localization**
- **Language**: Complete Vietnamese interface
- **Typography**: Optimized for Vietnamese text rendering
- **Cultural adaptation**: Vietnamese names, addresses, currency

### 4. **Custom Styling**
- **Responsive design**: Mobile-first approach
- **Custom animations**: Smooth transitions and loading states
- **Accessibility**: Focus states and high contrast support
- **Touch-friendly**: Minimum 44px touch targets

## 🚀 How It Works

### Layout Hierarchy
```
Next.js App Router
├── src/app/layout.tsx (Root layout)
├── src/app/booksy/layout.tsx (Booksy layout with header/sidebar)
└── src/app/booksy/apps/albafree/layout.tsx (Standalone - OVERRIDES parent)
```

### Layout Override
The `layout.tsx` file in the albafree directory **completely replaces** the parent layout:

```tsx
export default function AlbaFreeLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="vi">
      <body>
        {/* NO parent header/sidebar/footer */}
        <div className="min-h-screen bg-gray-50">
          {children}
        </div>
      </body>
    </html>
  );
}
```

## 📱 Mobile Experience

### Responsive Design
- **Breakpoints**: Mobile-first with tablet and desktop adaptations
- **Touch targets**: Minimum 44px for accessibility
- **Viewport**: Optimized for mobile devices
- **Font size**: 16px minimum to prevent iOS zoom

### PWA Features
- **Installable**: Add to home screen capability
- **Offline ready**: Prepared for service worker integration
- **App-like**: Standalone display mode
- **Fast loading**: Optimized assets and caching

## 🎨 Styling System

### CSS Architecture
```css
/* Mobile-first responsive */
.container {
  width: 100%;
  max-width: 448px; /* Mobile */
}

@media (min-width: 768px) {
  .container { max-width: 768px; } /* Tablet */
}

@media (min-width: 1024px) {
  .container { max-width: 1024px; } /* Desktop */
}
```

### Custom Components
- **Cards**: Hover effects and shadows
- **Buttons**: Primary/secondary with touch optimization
- **Status indicators**: Online/offline/busy states
- **Emergency styling**: Special highlighting for urgent jobs

## 🔍 Testing the Layout

### 1. **Verify Independence**
```bash
# Visit Alba Free
http://localhost:3000/booksy/apps/albafree

# Should NOT show:
- Booksy header
- Booksy sidebar navigation
- Booksy footer
```

### 2. **Check Mobile Experience**
- Open Developer Tools (F12)
- Switch to mobile view
- Verify responsive design
- Test touch interactions

### 3. **PWA Testing**
- Check manifest loading
- Test "Add to Home Screen"
- Verify app icons
- Test shortcuts

## 🔧 Customization

### Adding New Styles
Edit `src/app/booksy/apps/albafree/globals.css`:

```css
/* Add your custom styles here */
.my-custom-class {
  /* Your styles */
}
```

### Modifying Layout
Edit `src/app/booksy/apps/albafree/layout.tsx`:

```tsx
// Add global components, meta tags, or scripts
export default function AlbaFreeLayout({ children }) {
  return (
    <html lang="vi">
      <head>
        {/* Add custom meta tags */}
      </head>
      <body>
        {/* Add global components */}
        {children}
      </body>
    </html>
  );
}
```

## 🚀 Benefits

### 1. **Performance**
- **Faster loading**: No unnecessary parent components
- **Smaller bundle**: Only Alba Free specific code
- **Better caching**: Independent asset management

### 2. **User Experience**
- **App-like feel**: No distracting navigation
- **Focus**: Users stay within Alba Free context
- **Mobile optimized**: Designed for mobile-first usage

### 3. **Development**
- **Independence**: Can be developed separately
- **Testing**: Easier to test in isolation
- **Deployment**: Can be deployed as standalone app

## 📋 Comparison

| Feature | With Parent Layout | Standalone Layout |
|---------|-------------------|-------------------|
| Header | ✅ Booksy header | ❌ None (clean) |
| Sidebar | ✅ Booksy navigation | ❌ None (mobile-first) |
| Footer | ✅ Booksy footer | ❌ None (focused) |
| Mobile | 🔶 Responsive | ✅ Mobile-first |
| PWA | ❌ Limited | ✅ Full support |
| Performance | 🔶 Good | ✅ Excellent |
| Independence | ❌ Coupled | ✅ Standalone |

## 🎯 Next Steps

1. **Test thoroughly**: Verify all pages work correctly
2. **Add PWA features**: Service worker, offline support
3. **Optimize performance**: Image optimization, lazy loading
4. **Add analytics**: Track usage and performance
5. **User testing**: Get feedback on mobile experience

---

**Alba Free** is now a truly standalone mobile app experience! 📱✨
