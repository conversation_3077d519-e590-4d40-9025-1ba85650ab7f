"use client";

import { useState } from "react";

export default function TestAPIPage() {
  const [results, setResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testNearbyAPI = async (radius: number) => {
    setLoading(true);
    try {
      const response = await fetch(
        `/api/booksy/albafree/nearby?role=worker&lat=21.0285&lng=105.8542&radius=${radius}`
      );
      const data = await response.json();
      setResults({ type: 'nearby', radius, data });
    } catch (error) {
      setResults({ type: 'error', error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const testJobsAPI = async (radius: number) => {
    setLoading(true);
    try {
      const response = await fetch(
        `/api/booksy/albafree/jobs?status=open&lat=21.0285&lng=105.8542&radius=${radius}`
      );
      const data = await response.json();
      setResults({ type: 'jobs', radius, data });
    } catch (error) {
      setResults({ type: 'error', error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const testChatAPI = async () => {
    setLoading(true);
    try {
      // Test sending a message
      const sendResponse = await fetch('/api/booksy/albafree/chat/send', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          receiverId: 'user-002',
          content: 'Test message from API test page',
          type: 'text'
        })
      });
      const sendData = await sendResponse.json();
      
      // Test getting messages
      const getResponse = await fetch('/api/booksy/albafree/chat/user-001/user-002');
      const getData = await getResponse.json();
      
      setResults({ type: 'chat', sendData, getData });
    } catch (error) {
      setResults({ type: 'error', error: error.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-900 mb-8">AlbaFree API Test Page</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
          <div className="bg-white p-4 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4">Test Nearby API</h2>
            <div className="space-y-2">
              <button
                onClick={() => testNearbyAPI(2)}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
                disabled={loading}
              >
                Test 2km radius
              </button>
              <button
                onClick={() => testNearbyAPI(10)}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
                disabled={loading}
              >
                Test 10km radius
              </button>
              <button
                onClick={() => testNearbyAPI(50)}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
                disabled={loading}
              >
                Test 50km radius
              </button>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4">Test Jobs API</h2>
            <div className="space-y-2">
              <button
                onClick={() => testJobsAPI(5)}
                className="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700"
                disabled={loading}
              >
                Test Jobs 5km
              </button>
              <button
                onClick={() => testJobsAPI(20)}
                className="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700"
                disabled={loading}
              >
                Test Jobs 20km
              </button>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4">Test Chat API</h2>
            <div className="space-y-2">
              <button
                onClick={testChatAPI}
                className="w-full bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700"
                disabled={loading}
              >
                Test Chat Send/Get
              </button>
            </div>
          </div>
        </div>

        {loading && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600 mr-2"></div>
              <span>Testing API...</span>
            </div>
          </div>
        )}

        {results && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-4">Test Results</h2>
            <div className="bg-gray-100 p-4 rounded-lg overflow-auto">
              <pre className="text-sm">
                {JSON.stringify(results, null, 2)}
              </pre>
            </div>
          </div>
        )}

        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">Test Instructions</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Test different radius values to see filtering in action</li>
            <li>• Check that users/jobs are filtered by distance</li>
            <li>• Verify chat API can send and retrieve messages</li>
            <li>• All APIs should return JSON data without errors</li>
          </ul>
        </div>

        <div className="mt-4 text-center">
          <a
            href="/booksy/apps/albafree"
            className="bg-gray-600 text-white py-2 px-4 rounded hover:bg-gray-700"
          >
            Back to AlbaFree
          </a>
        </div>
      </div>
    </div>
  );
}
