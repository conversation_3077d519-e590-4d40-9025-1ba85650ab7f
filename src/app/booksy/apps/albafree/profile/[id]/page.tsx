"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import {
  ArrowLeft,
  Star,
  Shield,
  MapPin,
  Clock,
  Phone,
  MessageCircle,
  Heart,
  Share2,
  DollarSign,
  Calendar,
  User,
  Briefcase,
  Award,
  CheckCircle,
  XCircle,
  Zap,
  Eye,
  ThumbsUp,
  ThumbsDown,
  Edit,
  ExternalLink,
  Navigation
} from "lucide-react";

interface User {
  id: string;
  name: string;
  avatar: string;
  age: number;
  gender: string;
  phone: string;
  email: string;
  location: {
    lat: number;
    lng: number;
    address: string;
    district: string;
    city: string;
  };
  rating: number;
  totalRatings: number;
  isVerified: boolean;
  isOnline: boolean;
  lastSeen: string;
  distance: number;
  role: 'owner' | 'worker';
  profile: {
    description: string;
    skills: string[];
    experience: string;
    availability: string;
    hourlyRate?: number;
    businessType?: string;
    employeeCount?: number;
    aboutMe?: string;
    goodAt?: string;
    callMeWhen?: string;
  };
  stats: {
    completedJobs: number;
    responseTime: string;
    reliability: number;
    noShowRate: number;
  };
  preferences: {
    workTypes: string[];
    maxDistance: number;
    emergencyAvailable: boolean;
    preferredPayment: string[];
  };
  verification: {
    idVerified: boolean;
    phoneVerified: boolean;
    emailVerified: boolean;
    backgroundCheck?: boolean;
    businessLicense?: boolean;
  };
  workHistory: Array<{
    jobId: string;
    employerId: string;
    title: string;
    date: string;
    duration: string;
    rating: number;
    review: string;
  }>;
  businessInfo?: {
    businessName: string;
    businessType: string;
    address: string;
    operatingHours: string;
    employeeCount: number;
    urgentNeedFrequency: string;
  };
}

export default function ProfilePage() {
  const router = useRouter();
  const params = useParams();
  const userId = params.id as string;
  const currentUserId = "user-001"; // Mock current user

  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'history' | 'reviews'>('overview');
  const [currentLocation, setCurrentLocation] = useState<{lat: number; lng: number} | null>(null);

  const isOwnProfile = userId === currentUserId;

  useEffect(() => {
    loadUserProfile();
    if (isOwnProfile) {
      getCurrentLocation();
    }
  }, [userId, isOwnProfile]);

  const loadUserProfile = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/booksy/albafree/users/${userId}`);
      const userData = await response.json();
      setUser(userData);
    } catch (error) {
      console.error('Error loading user profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBbeonjjeokCall = async () => {
    try {
      await fetch('/api/booksy/albafree/bbeonjjeok-call', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          targetUserId: userId, 
          senderId: 'user-001', // Current user ID
          message: `${user?.role === 'worker' ? 'Yêu cầu làm việc ngay' : 'Tuyển dụng khẩn cấp'}!`
        })
      });
      alert('Cuộc gọi khẩn cấp đã được gửi!');
    } catch (error) {
      console.error('Error sending Bbeonjjeok Call:', error);
    }
  };

  const handleContact = () => {
    window.open(`tel:${user?.phone}`);
  };

  const handleMessage = () => {
    router.push(`/booksy/apps/albafree/chat/${userId}`);
  };

  const handleEditProfile = () => {
    router.push('/booksy/apps/albafree/profile/edit');
  };

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setCurrentLocation({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
        },
        (error) => {
          console.error('Error getting location:', error);
        }
      );
    }
  };

  const openInGoogleMaps = () => {
    if (currentLocation) {
      const url = `https://www.google.com/maps?q=${currentLocation.lat},${currentLocation.lng}`;
      window.open(url, '_blank');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải hồ sơ...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Không tìm thấy người dùng</h3>
          <button
            onClick={() => router.back()}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Quay lại
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-md mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <button
              onClick={() => router.back()}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600" />
            </button>
            <h1 className="text-lg font-semibold text-gray-900">Hồ sơ</h1>
            <div className="flex items-center space-x-2">
              <button className="p-2 hover:bg-gray-100 rounded-full">
                <Heart className="h-5 w-5 text-gray-600" />
              </button>
              <button className="p-2 hover:bg-gray-100 rounded-full">
                <Share2 className="h-5 w-5 text-gray-600" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Current Location (only for own profile) */}
      {isOwnProfile && (
        <div className="bg-blue-50 border-b">
          <div className="max-w-md mx-auto px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Navigation className="h-5 w-5 text-blue-600" />
                <div>
                  <h3 className="font-medium text-gray-900">Vị trí hiện tại của bạn</h3>
                  {currentLocation ? (
                    <p className="text-sm text-gray-600">
                      {currentLocation.lat.toFixed(6)}, {currentLocation.lng.toFixed(6)}
                    </p>
                  ) : (
                    <p className="text-sm text-gray-500">Đang xác định vị trí...</p>
                  )}
                </div>
              </div>
              {currentLocation && (
                <button
                  onClick={openInGoogleMaps}
                  className="bg-blue-600 text-white px-3 py-1 rounded-lg hover:bg-blue-700 flex items-center space-x-1 text-sm"
                >
                  <ExternalLink className="h-3 w-3" />
                  <span>Google Maps</span>
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Profile Header */}
      <div className="bg-white border-b">
        <div className="max-w-md mx-auto px-4 py-6">
          <div className="flex items-start space-x-4">
            <div className="relative">
              <img
                src={user.avatar}
                alt={user.name}
                className="w-20 h-20 rounded-full object-cover"
              />
              <div className={`absolute -bottom-1 -right-1 w-6 h-6 rounded-full border-2 border-white ${
                user.isOnline ? 'bg-green-500' : 'bg-gray-400'
              }`}></div>
              {user.isVerified && (
                <div className="absolute -top-1 -right-1 bg-blue-500 rounded-full p-1">
                  <Shield className="h-4 w-4 text-white" />
                </div>
              )}
            </div>

            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <h2 className="text-xl font-bold text-gray-900">{user.name}</h2>
                <span className="text-sm text-gray-500">({user.age}세)</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  user.role === 'worker'
                    ? 'bg-blue-100 text-blue-700'
                    : 'bg-green-100 text-green-700'
                }`}>
                  {user.role === 'worker' ? 'Người tìm việc' : 'Nhà tuyển dụng'}
                </span>
              </div>

              <div className="flex items-center space-x-4 mb-2">
                <div className="flex items-center space-x-1">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="font-medium text-gray-700">{user.rating}</span>
                  <span className="text-sm text-gray-500">({user.totalRatings})</span>
                </div>
                <div className="flex items-center space-x-1 text-sm text-gray-500">
                  <MapPin className="h-3 w-3" />
                  <span>{user.distance?.toFixed(1)}km</span>
                </div>
                <div className="flex items-center space-x-1 text-sm text-gray-500">
                  <Clock className="h-3 w-3" />
                  <span>{user.stats.responseTime}</span>
                </div>
              </div>

              <p className="text-sm text-gray-600 mb-3">{user.profile.description}</p>

              <div className="flex flex-wrap gap-1">
                {user.profile.skills.slice(0, 4).map((skill, index) => (
                  <span
                    key={index}
                    className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs"
                  >
                    {skill}
                  </span>
                ))}
                {user.profile.skills.length > 4 && (
                  <span className="text-xs text-gray-500">+{user.profile.skills.length - 4}</span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="bg-white border-b">
        <div className="max-w-md mx-auto px-4 py-4">
          {isOwnProfile ? (
            <div className="flex items-center space-x-3">
              <button
                onClick={handleEditProfile}
                className="flex-1 bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 flex items-center justify-center space-x-2"
              >
                <Edit className="h-4 w-4" />
                <span>Chỉnh sửa hồ sơ</span>
              </button>
            </div>
          ) : (
            <div className="flex items-center space-x-3">
              <button
                onClick={handleContact}
                className="flex-1 bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 flex items-center justify-center space-x-2"
              >
                <Phone className="h-4 w-4" />
                <span>Gọi điện</span>
              </button>
              <button
                onClick={handleMessage}
                className="flex-1 bg-green-600 text-white py-3 rounded-lg font-medium hover:bg-green-700 flex items-center justify-center space-x-2"
              >
                <MessageCircle className="h-4 w-4" />
                <span>Nhắn tin</span>
              </button>
              {user.preferences.emergencyAvailable && (
                <button
                  onClick={handleBbeonjjeokCall}
                  className="bg-yellow-500 text-white px-4 py-3 rounded-lg font-medium hover:bg-yellow-600 flex items-center space-x-1"
                >
                  <Zap className="h-4 w-4" />
                  <span>Gọi khẩn</span>
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="bg-white border-b">
        <div className="max-w-md mx-auto px-4 py-4">
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{user.stats.completedJobs}</div>
              <div className="text-sm text-gray-500">Công việc hoàn thành</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{user.stats.reliability}%</div>
              <div className="text-sm text-gray-500">Độ tin cậy</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{user.stats.noShowRate}%</div>
              <div className="text-sm text-gray-500">Tỷ lệ vắng mặt</div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white border-b">
        <div className="max-w-md mx-auto px-4">
          <div className="flex">
            {[
              { id: 'overview', label: 'Tổng quan' },
              { id: 'history', label: 'Lịch sử' },
              { id: 'reviews', label: 'Đánh giá' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex-1 py-3 text-sm font-medium border-b-2 ${
                  activeTab === tab.id
                    ? 'border-blue-600 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Tab Content */}
      <div className="max-w-md mx-auto px-4 py-4">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Basic Info */}
            <div className="bg-white rounded-lg shadow-sm border p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">기본 정보</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">위치</span>
                  <span className="text-gray-900">{user.location.address}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">경험</span>
                  <span className="text-gray-900">{user.profile.experience}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">가능 시간</span>
                  <span className="text-gray-900">{user.profile.availability}</span>
                </div>
                {user.profile.hourlyRate && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">희망 시급</span>
                    <span className="text-green-600 font-medium">
                      {user.profile.hourlyRate.toLocaleString()}원/시간
                    </span>
                  </div>
                )}
                {user.businessInfo && (
                  <>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">사업체</span>
                      <span className="text-gray-900">{user.businessInfo.businessName}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">운영시간</span>
                      <span className="text-gray-900">{user.businessInfo.operatingHours}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">직원 수</span>
                      <span className="text-gray-900">{user.businessInfo.employeeCount}명</span>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Skills */}
            <div className="bg-white rounded-lg shadow-sm border p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">스킬 & 전문분야</h3>
              <div className="flex flex-wrap gap-2">
                {user.profile.skills.map((skill, index) => (
                  <span
                    key={index}
                    className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm font-medium"
                  >
                    {skill}
                  </span>
                ))}
              </div>
            </div>

            {/* About Me Section */}
            {user.profile.aboutMe && (
              <div className="bg-white rounded-lg shadow-sm border p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Về tôi</h3>
                <p className="text-gray-700 text-sm leading-relaxed">{user.profile.aboutMe}</p>
              </div>
            )}

            {/* Good At Section */}
            {user.profile.goodAt && (
              <div className="bg-white rounded-lg shadow-sm border p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Tôi giỏi về</h3>
                <p className="text-gray-700 text-sm leading-relaxed">{user.profile.goodAt}</p>
              </div>
            )}

            {/* Call Me When Section */}
            {user.profile.callMeWhen && (
              <div className="bg-white rounded-lg shadow-sm border p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Gọi tôi khi</h3>
                <p className="text-gray-700 text-sm leading-relaxed">{user.profile.callMeWhen}</p>
              </div>
            )}

            {/* Verification */}
            <div className="bg-white rounded-lg shadow-sm border p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">인증 상태</h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">신분증 인증</span>
                  {user.verification.idVerified ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">전화번호 인증</span>
                  {user.verification.phoneVerified ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">이메일 인증</span>
                  {user.verification.emailVerified ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                </div>
                {user.verification.backgroundCheck !== undefined && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">신원조회</span>
                    {user.verification.backgroundCheck ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                  </div>
                )}
                {user.verification.businessLicense !== undefined && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">사업자등록증</span>
                    {user.verification.businessLicense ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Preferences */}
            <div className="bg-white rounded-lg shadow-sm border p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">선호사항</h3>
              <div className="space-y-3">
                <div>
                  <span className="text-gray-600 block mb-1">선호 업무</span>
                  <div className="flex flex-wrap gap-1">
                    {user.preferences.workTypes.map((type, index) => (
                      <span
                        key={index}
                        className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-sm"
                      >
                        {type}
                      </span>
                    ))}
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">최대 이동거리</span>
                  <span className="text-gray-900">{user.preferences.maxDistance}km</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">긴급 근무 가능</span>
                  {user.preferences.emergencyAvailable ? (
                    <span className="text-green-600 font-medium">가능</span>
                  ) : (
                    <span className="text-gray-500">불가능</span>
                  )}
                </div>
                <div>
                  <span className="text-gray-600 block mb-1">선호 결제방식</span>
                  <div className="flex flex-wrap gap-1">
                    {user.preferences.preferredPayment.map((method, index) => (
                      <span
                        key={index}
                        className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-sm"
                      >
                        {method}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'history' && (
          <div className="space-y-4">
            {user.workHistory.length > 0 ? (
              user.workHistory.map((work, index) => (
                <div key={index} className="bg-white rounded-lg shadow-sm border p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-semibold text-gray-900">{work.title}</h4>
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="text-sm font-medium">{work.rating}</span>
                    </div>
                  </div>
                  <div className="text-sm text-gray-600 mb-2">
                    {new Date(work.date).toLocaleDateString('ko-KR')} • {work.duration}
                  </div>
                  <p className="text-sm text-gray-700">{work.review}</p>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">아직 작업 이력이 없습니다.</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'reviews' && (
          <div className="space-y-4">
            <div className="bg-white rounded-lg shadow-sm border p-4">
              <div className="text-center mb-4">
                <div className="text-3xl font-bold text-gray-900 mb-1">{user.rating}</div>
                <div className="flex items-center justify-center space-x-1 mb-2">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      className={`h-5 w-5 ${
                        star <= Math.floor(user.rating)
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <div className="text-sm text-gray-500">{user.totalRatings}개의 리뷰</div>
              </div>
            </div>

            {user.workHistory.length > 0 ? (
              user.workHistory.map((work, index) => (
                <div key={index} className="bg-white rounded-lg shadow-sm border p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-1">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`h-4 w-4 ${
                            star <= work.rating
                              ? 'text-yellow-400 fill-current'
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-gray-500">
                      {new Date(work.date).toLocaleDateString('ko-KR')}
                    </span>
                  </div>
                  <p className="text-gray-700 mb-2">{work.review}</p>
                  <div className="text-sm text-gray-500">{work.title}</div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <Star className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">아직 리뷰가 없습니다.</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
