"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  MapPin,
  ExternalLink,
  Save,
  Camera,
  User,
  Clock,
  DollarSign,
  Phone,
  Mail,
  Star,
  Zap,
  Navigation,
  RefreshCw
} from "lucide-react";

interface UserProfile {
  id: string;
  name: string;
  avatar: string;
  age: number;
  gender: string;
  phone: string;
  email: string;
  location: {
    lat: number;
    lng: number;
    address: string;
    district: string;
    city: string;
  };
  profile: {
    description: string;
    skills: string[];
    experience: string;
    availability: string;
    hourlyRate: number;
    aboutMe?: string;
    goodAt?: string;
    callMeWhen?: string;
  };
  preferences: {
    workTypes: string[];
    maxDistance: number;
    emergencyAvailable: boolean;
    preferredPayment: string[];
  };
}

export default function EditProfilePage() {
  const router = useRouter();
  const [user, setUser] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [currentLocation, setCurrentLocation] = useState<{lat: number; lng: number} | null>(null);
  const [locationLoading, setLocationLoading] = useState(false);
  
  // Form states
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    description: '',
    aboutMe: '',
    goodAt: '',
    callMeWhen: '',
    skills: [] as string[],
    experience: '',
    availability: '',
    hourlyRate: 0,
    workTypes: [] as string[],
    maxDistance: 5,
    emergencyAvailable: false,
    preferredPayment: [] as string[]
  });

  const currentUserId = "user-001"; // Mock current user

  useEffect(() => {
    loadUserProfile();
    getCurrentLocation();
  }, []);

  const loadUserProfile = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/booksy/albafree/users/${currentUserId}`);
      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
        
        // Populate form data
        setFormData({
          name: userData.name || '',
          phone: userData.phone || '',
          email: userData.email || '',
          description: userData.profile?.description || '',
          aboutMe: userData.profile?.aboutMe || '',
          goodAt: userData.profile?.goodAt || '',
          callMeWhen: userData.profile?.callMeWhen || '',
          skills: userData.profile?.skills || [],
          experience: userData.profile?.experience || '',
          availability: userData.profile?.availability || '',
          hourlyRate: userData.profile?.hourlyRate || 0,
          workTypes: userData.preferences?.workTypes || [],
          maxDistance: userData.preferences?.maxDistance || 5,
          emergencyAvailable: userData.preferences?.emergencyAvailable || false,
          preferredPayment: userData.preferences?.preferredPayment || []
        });
      }
    } catch (error) {
      console.error('Error loading user profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const getCurrentLocation = () => {
    setLocationLoading(true);
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setCurrentLocation({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
          setLocationLoading(false);
        },
        (error) => {
          console.error('Error getting location:', error);
          setLocationLoading(false);
        }
      );
    } else {
      setLocationLoading(false);
    }
  };

  const openInGoogleMaps = () => {
    if (currentLocation) {
      const url = `https://www.google.com/maps?q=${currentLocation.lat},${currentLocation.lng}`;
      window.open(url, '_blank');
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleArrayChange = (field: string, value: string) => {
    const items = value.split(',').map(item => item.trim()).filter(item => item);
    setFormData(prev => ({
      ...prev,
      [field]: items
    }));
  };

  const handleSave = async () => {
    if (!user) return;
    
    setSaving(true);
    try {
      const updatedUser = {
        ...user,
        name: formData.name,
        phone: formData.phone,
        email: formData.email,
        profile: {
          ...user.profile,
          description: formData.description,
          aboutMe: formData.aboutMe,
          goodAt: formData.goodAt,
          callMeWhen: formData.callMeWhen,
          skills: formData.skills,
          experience: formData.experience,
          availability: formData.availability,
          hourlyRate: formData.hourlyRate
        },
        preferences: {
          ...user.preferences,
          workTypes: formData.workTypes,
          maxDistance: formData.maxDistance,
          emergencyAvailable: formData.emergencyAvailable,
          preferredPayment: formData.preferredPayment
        },
        // Update location if current location is available
        ...(currentLocation && {
          location: {
            ...user.location,
            lat: currentLocation.lat,
            lng: currentLocation.lng
          }
        })
      };

      const response = await fetch(`/api/booksy/albafree/users/${currentUserId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedUser)
      });

      if (response.ok) {
        alert('Hồ sơ đã được cập nhật thành công!');
        router.push(`/booksy/apps/albafree/profile/${currentUserId}`);
      } else {
        throw new Error('Failed to update profile');
      }
    } catch (error) {
      console.error('Error saving profile:', error);
      alert('Có lỗi xảy ra khi lưu hồ sơ. Vui lòng thử lại.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải hồ sơ...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Không tìm thấy hồ sơ</h3>
          <button
            onClick={() => router.back()}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Quay lại
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-md mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <button
              onClick={() => router.back()}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600" />
            </button>
            <h1 className="text-lg font-semibold text-gray-900">Chỉnh sửa hồ sơ</h1>
            <button
              onClick={handleSave}
              disabled={saving}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 flex items-center space-x-1"
            >
              {saving ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Save className="h-4 w-4" />
              )}
              <span>{saving ? 'Đang lưu...' : 'Lưu'}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Current Location */}
      <div className="bg-blue-50 border-b">
        <div className="max-w-md mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <MapPin className="h-5 w-5 text-blue-600" />
              <div>
                <h3 className="font-medium text-gray-900">Vị trí hiện tại</h3>
                {currentLocation ? (
                  <p className="text-sm text-gray-600">
                    {currentLocation.lat.toFixed(6)}, {currentLocation.lng.toFixed(6)}
                  </p>
                ) : (
                  <p className="text-sm text-gray-500">Chưa xác định vị trí</p>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={getCurrentLocation}
                disabled={locationLoading}
                className="p-2 text-blue-600 hover:bg-blue-100 rounded-full disabled:opacity-50"
              >
                <RefreshCw className={`h-4 w-4 ${locationLoading ? 'animate-spin' : ''}`} />
              </button>
              {currentLocation && (
                <button
                  onClick={openInGoogleMaps}
                  className="bg-blue-600 text-white px-3 py-1 rounded-lg hover:bg-blue-700 flex items-center space-x-1 text-sm"
                >
                  <ExternalLink className="h-3 w-3" />
                  <span>Google Maps</span>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className="max-w-md mx-auto px-4 py-6 space-y-6">
        {/* Basic Info */}
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Thông tin cơ bản</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Họ tên</label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Số điện thoại</label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* About Me Section */}
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Về tôi</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Mô tả bản thân</label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Giới thiệu ngắn gọn về bản thân..."
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Tôi là ai / Tôi thích gì</label>
              <textarea
                value={formData.aboutMe}
                onChange={(e) => handleInputChange('aboutMe', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Ví dụ: Tôi là sinh viên năm 3, thích giao tiếp với khách hàng, yêu thích môi trường năng động..."
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Tôi giỏi về</label>
              <textarea
                value={formData.goodAt}
                onChange={(e) => handleInputChange('goodAt', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Ví dụ: Pha chế cà phê, phục vụ khách hàng, tính toán nhanh, tiếng Anh giao tiếp..."
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Gọi tôi khi</label>
              <textarea
                value={formData.callMeWhen}
                onChange={(e) => handleInputChange('callMeWhen', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Ví dụ: Cần người gấp trong vòng 2 tiếng, ca cuối tuần, việc nhẹ nhàng, lương trên 40k/giờ..."
              />
            </div>
          </div>
        </div>

        {/* Skills & Experience */}
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Kỹ năng & Kinh nghiệm</h2>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Kỹ năng (phân cách bằng dấu phẩy)</label>
              <input
                type="text"
                value={formData.skills.join(', ')}
                onChange={(e) => handleArrayChange('skills', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Ví dụ: Quán cà phê, Phục vụ, Thu ngân, Tiếng Anh"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Kinh nghiệm làm việc</label>
              <textarea
                value={formData.experience}
                onChange={(e) => handleInputChange('experience', e.target.value)}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Ví dụ: Quán cà phê 6 tháng, nhà hàng 1 năm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Thời gian có thể làm việc</label>
              <textarea
                value={formData.availability}
                onChange={(e) => handleInputChange('availability', e.target.value)}
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Ví dụ: Tối các ngày trong tuần, cả ngày cuối tuần"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Mức lương mong muốn (VNĐ/giờ)</label>
              <input
                type="number"
                value={formData.hourlyRate}
                onChange={(e) => handleInputChange('hourlyRate', parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="35000"
              />
            </div>
          </div>
        </div>

        {/* Preferences */}
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Sở thích làm việc</h2>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Loại công việc ưa thích (phân cách bằng dấu phẩy)</label>
              <input
                type="text"
                value={formData.workTypes.join(', ')}
                onChange={(e) => handleArrayChange('workTypes', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Ví dụ: Quán cà phê, Nhà hàng, Cửa hàng tiện lợi"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Khoảng cách tối đa (km)</label>
              <input
                type="number"
                value={formData.maxDistance}
                onChange={(e) => handleInputChange('maxDistance', parseInt(e.target.value) || 5)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                min="1"
                max="50"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Phương thức thanh toán ưa thích (phân cách bằng dấu phẩy)</label>
              <input
                type="text"
                value={formData.preferredPayment.join(', ')}
                onChange={(e) => handleArrayChange('preferredPayment', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Ví dụ: Tiền mặt, Chuyển khoản, Ví điện tử"
              />
            </div>

            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="emergencyAvailable"
                checked={formData.emergencyAvailable}
                onChange={(e) => handleInputChange('emergencyAvailable', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="emergencyAvailable" className="text-sm font-medium text-gray-700">
                Sẵn sàng nhận việc khẩn cấp
              </label>
            </div>
          </div>
        </div>

        {/* Save Button */}
        <div className="pb-6">
          <button
            onClick={handleSave}
            disabled={saving}
            className="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 flex items-center justify-center space-x-2 font-medium"
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>Đang lưu hồ sơ...</span>
              </>
            ) : (
              <>
                <Save className="h-5 w-5" />
                <span>Lưu hồ sơ</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
