"use client";

import { useState } from "react";
import { RefreshCw, Users, Briefcase } from "lucide-react";

export default function SimpleTestPage() {
  const [loading, setLoading] = useState(false);

  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 1000);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-md mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-bold text-gray-900">Alba Free</h1>
            <button 
              onClick={handleRefresh}
              disabled={loading}
              className="p-2 hover:bg-gray-100 rounded-full disabled:opacity-50"
            >
              <RefreshCw className={`h-5 w-5 text-gray-600 ${loading ? 'animate-spin' : ''}`} />
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-md mx-auto px-4 py-6">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Kết nối việc làm ngay lập tức
          </h2>
          <p className="text-gray-600">
            Tìm việc làm và tuyển dụng nhanh chóng tại Việt Nam
          </p>
        </div>

        {/* Role Toggle */}
        <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
          <div className="flex items-center justify-center space-x-4">
            <span className="text-sm font-medium text-blue-600">Tìm việc</span>
            <div className="w-12 h-6 bg-gray-200 rounded-full"></div>
            <span className="text-sm font-medium text-gray-500">Tuyển người</span>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="bg-white rounded-lg shadow-sm border p-4 text-center">
            <Users className="h-8 w-8 text-blue-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">150+</div>
            <div className="text-sm text-gray-500">Người dùng</div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border p-4 text-center">
            <Briefcase className="h-8 w-8 text-green-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">50+</div>
            <div className="text-sm text-gray-500">Việc làm</div>
          </div>
        </div>

        {/* Test Cards */}
        <div className="space-y-4">
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <h3 className="font-semibold text-gray-900 mb-2">Nguyễn Minh Tuấn</h3>
            <p className="text-sm text-gray-600 mb-2">Sinh viên chăm chỉ, có kinh nghiệm quán cà phê</p>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500">Đống Đa, Hà Nội</span>
              <span className="bg-green-100 text-green-700 px-2 py-1 rounded text-xs">
                Online
              </span>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-4">
            <h3 className="font-semibold text-gray-900 mb-2">Trần Thị Lan</h3>
            <p className="text-sm text-gray-600 mb-2">Phục vụ thân thiện, biết tiếng Anh</p>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500">Hoàn Kiếm, Hà Nội</span>
              <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                Offline
              </span>
            </div>
          </div>

          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <span className="bg-red-100 text-red-600 px-2 py-1 rounded text-xs font-medium">
                Khẩn cấp
              </span>
              <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-medium">
                Gọi khẩn
              </span>
            </div>
            <h3 className="font-semibold text-gray-900 mb-2">
              Cần gấp nhân viên phục vụ quán cà phê!
            </h3>
            <p className="text-sm text-gray-600 mb-2">
              Cần người phục vụ từ 2h chiều đến 6h chiều hôm nay
            </p>
            <div className="flex items-center justify-between">
              <span className="text-sm text-green-600 font-medium">35.000-45.000đ/giờ</span>
              <span className="text-sm text-gray-500">4 giờ</span>
            </div>
          </div>
        </div>

        {/* Test Status */}
        <div className="mt-8 bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="text-sm font-semibold text-green-900 mb-2">
            ✅ Layout Test Successful
          </h3>
          <ul className="text-xs text-green-800 space-y-1">
            <li>• No Booksy header/sidebar/footer</li>
            <li>• Standalone layout working</li>
            <li>• Vietnamese interface</li>
            <li>• Mobile-optimized design</li>
            <li>• Refresh functionality</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
