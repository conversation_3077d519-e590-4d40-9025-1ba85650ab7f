# Alba Free - Nền tảng kết nối việc làm ngay lập tức

Alba Free là nền tảng kết nối việc làm ngay lập tức tiên tiến tại Việt Nam, kết nối nhà tuyển dụng và người lao động trong thời gian thực. Kh<PERSON>c với phương thức tuyển dụng truyền thống, Alba Free có tính năng "G<PERSON>i khẩn cấp" để đáp ứng ngay lập tức nhu cầu nhân lực khẩn cấp.

## 🚀 Tính năng chính

### Tính năng cốt lõi

- **Chuyển đổi vai trò**: Người dùng có thể tự do chuyển đổi giữa vai trò nhà tuyển dụng và người tìm việc
- **Kết nối theo vị trí**: <PERSON><PERSON><PERSON> kiếm người dùng xung quanh theo thời gian thực bằng GPS
- **G<PERSON><PERSON> khẩn cấp**: <PERSON><PERSON><PERSON> thông báo ngay lập tức cho người lao động xung quanh trong tình huống khẩn cấp
- **Thông báo thời gian thực**: Thông báo push và tích hợp Zalo/Messenger
- **Làm mới vị trí**: Nút làm mới để cập nhật vị trí GPS và danh sách công việc

### Tính năng cho nhà tuyển dụng

- **Tuyển dụng ngay**: Chọn và tuyển dụng người lao động ngay lập tức mà không cần phỏng vấn
- **Tin tuyển dụng khẩn cấp**: Đăng tin cần phản hồi trong vòng 15 phút
- **Tìm kiếm người lao động xung quanh**: Xem người lao động có sẵn trong bán kính theo thời gian thực
- **Kiểm tra độ tin cậy**: Xem tỷ lệ hoàn thành, tỷ lệ vắng mặt, đánh giá của người lao động

### Tính năng cho người tìm việc

- **Phản hồi ngay**: Ứng tuyển và chấp nhận công việc nhanh chóng
- **Việc làm xung quanh**: Tự động đề xuất việc làm gần đây dựa trên vị trí
- **Việc làm khẩn cấp**: Ưu tiên hiển thị việc làm có thể bắt đầu ngay lập tức
- **Quản lý hồ sơ**: Hồ sơ chi tiết với kỹ năng, kinh nghiệm, thời gian có thể làm việc

## 📱 사용자 인터페이스

### 메인 화면
- **역할 토글**: 고용주 ↔ 근로자 모드 전환
- **검색 및 필터**: 카테고리, 거리, 급여 등 다양한 필터
- **사용자 카드**: 프로필, 평점, 거리, 가능 시간 등 표시
- **긴급 공고**: 빨간색으로 강조된 긴급 채용 공고

### 프로필 페이지
- **기본 정보**: 이름, 나이, 위치, 평점, 인증 상태
- **상세 정보**: 경험, 스킬, 선호사항, 가능 시간
- **작업 이력**: 완료한 작업 및 받은 리뷰
- **인증 상태**: 신분증, 전화번호, 이메일, 신원조회 등

### 공고 등록
- **기본 정보**: 제목, 설명, 카테고리
- **긴급도 설정**: 보통/급함/초급함 선택
- **번쩍콜 옵션**: 긴급 시 주변 근로자에게 즉시 알림
- **급여 설정**: 시급/고정급, 최소/최대 금액
- **요구사항**: 필요한 스킬 및 조건

## 🛠 기술 구조

### Frontend
- **Framework**: Next.js 14 with TypeScript
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **State Management**: React Hooks

### Backend
- **API Routes**: Next.js API Routes
- **Data Storage**: JSON files
- **File Structure**: `/data/apps/booksy/albafree/`

### Data Files
```
data/apps/booksy/albafree/
├── users.json          # 사용자 프로필 및 정보
├── jobs.json           # 공고 및 작업 정보
├── matches.json        # 매칭 및 지원 정보
├── notifications.json  # 알림 및 번쩍콜 기록
└── config.json         # 앱 설정 및 카테고리
```

## 📊 데이터 구조

### User Schema
```typescript
interface User {
  id: string;
  name: string;
  avatar: string;
  age: number;
  gender: string;
  phone: string;
  email: string;
  location: Location;
  rating: number;
  totalRatings: number;
  isVerified: boolean;
  isOnline: boolean;
  role: 'owner' | 'worker';
  profile: Profile;
  stats: Stats;
  preferences: Preferences;
  verification: Verification;
  workHistory: WorkHistory[];
}
```

### Job Schema
```typescript
interface Job {
  id: string;
  title: string;
  description: string;
  category: string;
  urgency: 'low' | 'normal' | 'high' | 'emergency';
  budget: Budget;
  duration: string;
  location: Location;
  requirements: string[];
  postedBy: string;
  postedAt: string;
  startsAt: string;
  status: 'open' | 'assigned' | 'in_progress' | 'completed';
  isEmergency: boolean;
  bbeonjjeokCall: boolean;
}
```

## 🔧 API 엔드포인트

### 사용자 관리
- `GET /api/booksy/albafree/users` - 사용자 목록 조회
- `GET /api/booksy/albafree/users/[id]` - 특정 사용자 조회
- `POST /api/booksy/albafree/users` - 새 사용자 생성
- `PUT /api/booksy/albafree/users/[id]` - 사용자 정보 수정

### 위치 기반 서비스
- `GET /api/booksy/albafree/nearby` - 주변 사용자 조회
- `GET /api/booksy/albafree/user/current` - 현재 사용자 정보

### 공고 관리
- `GET /api/booksy/albafree/jobs` - 공고 목록 조회
- `POST /api/booksy/albafree/jobs` - 새 공고 등록

### 번쩍콜 시스템
- `POST /api/booksy/albafree/bbeonjjeok-call` - 번쩍콜 전송

### 설정
- `GET /api/booksy/albafree/config` - 앱 설정 조회

## 🎯 주요 특징

### 번쩍콜 (Bbeonjjeok Call) 시스템
- **즉시 알림**: 30분 내 만료되는 긴급 알림
- **전체 화면**: 받는 사람의 화면을 전체 화면으로 점유
- **KakaoTalk 연동**: 카카오톡을 통한 추가 알림
- **제한 기능**: 시간당 최대 10회 제한

### 신뢰도 시스템
- **완료율**: 지원한 작업 대비 완료한 작업 비율
- **노쇼율**: 약속 시간에 나타나지 않은 비율
- **응답 시간**: 평균 응답 시간 추적
- **평점 시스템**: 5점 만점 상호 평가

### 인증 시스템
- **신분증 인증**: 정부 발급 신분증 확인
- **전화번호 인증**: SMS 인증
- **이메일 인증**: 이메일 주소 확인
- **신원조회**: 선택적 백그라운드 체크
- **사업자등록증**: 고용주용 사업자 인증

## 🚀 시작하기

### 설치 및 실행
```bash
# 의존성 설치
yarn install

# 개발 서버 실행
yarn dev

# 앱 접속
http://localhost:3000/booksy/apps/albafree
```

### 데이터 초기화
앱 실행 전 `/data/apps/booksy/albafree/` 디렉토리에 필요한 JSON 파일들이 있는지 확인하세요.

## 📈 향후 계획

### 단기 계획
- [ ] 실시간 채팅 시스템
- [ ] 결제 시스템 연동
- [ ] 평가 및 리뷰 시스템 고도화
- [ ] 푸시 알림 구현

### 장기 계획
- [ ] AI 기반 매칭 알고리즘
- [ ] 다국어 지원 (영어, 중국어)
- [ ] 모바일 앱 개발
- [ ] 기업용 대량 채용 기능

## 📞 지원

문의사항이나 버그 리포트는 개발팀에 연락해주세요.

---

**Alba Free** - 일자리를 찾는 새로운 방법 🚀
