"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import {
  ArrowLeft,
  MapPin,
  Clock,
  DollarSign,
  Calendar,
  User,
  Star,
  Shield,
  Phone,
  MessageCircle,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Zap,
  Users,
  Briefcase,
  Heart,
  Share2
} from "lucide-react";

interface Job {
  id: string;
  title: string;
  description: string;
  category: string;
  urgency: 'low' | 'normal' | 'high' | 'emergency';
  budget: {
    min: number;
    max: number;
    currency: string;
    type: 'hourly' | 'fixed';
  };
  duration: string;
  location: {
    address: string;
    lat: number;
    lng: number;
  };
  requirements: string[];
  postedBy: string;
  postedAt: string;
  startsAt: string;
  status: 'open' | 'assigned' | 'in_progress' | 'completed' | 'cancelled';
  applicants: string[];
  assignedTo?: string;
  isEmergency: boolean;
  bbeonjjeokCall?: boolean;
  tags: string[];
  benefits: string[];
}

interface Employer {
  id: string;
  name: string;
  avatar: string;
  rating: number;
  totalRatings: number;
  isVerified: boolean;
  businessInfo?: {
    businessName: string;
    businessType: string;
    operatingHours: string;
  };
}

export default function JobDetailPage() {
  const router = useRouter();
  const params = useParams();
  const jobId = params.id as string;
  
  const [job, setJob] = useState<Job | null>(null);
  const [employer, setEmployer] = useState<Employer | null>(null);
  const [loading, setLoading] = useState(true);
  const [applying, setApplying] = useState(false);
  const [hasApplied, setHasApplied] = useState(false);

  useEffect(() => {
    loadJobDetails();
  }, [jobId]);

  const loadJobDetails = async () => {
    try {
      setLoading(true);
      
      // Load job details
      const jobResponse = await fetch(`/api/booksy/albafree/jobs/${jobId}`);
      const jobData = await jobResponse.json();
      setJob(jobData);
      
      // Load employer details
      const employerResponse = await fetch(`/api/booksy/albafree/users/${jobData.postedBy}`);
      const employerData = await employerResponse.json();
      setEmployer(employerData);
      
      // Check if current user has already applied
      const currentUserId = 'user-001'; // Mock current user
      setHasApplied(jobData.applicants.includes(currentUserId));
      
    } catch (error) {
      console.error('Error loading job details:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApply = async () => {
    if (!job) return;
    
    setApplying(true);
    try {
      const response = await fetch('/api/booksy/albafree/jobs/apply', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jobId: job.id,
          workerId: 'user-001', // Current user ID
          message: '안녕하세요! 이 일에 지원하고 싶습니다.'
        })
      });

      if (response.ok) {
        setHasApplied(true);
        alert('Ứng tuyển thành công!');
        loadJobDetails(); // Refresh data
      } else {
        throw new Error('Failed to apply');
      }
    } catch (error) {
      console.error('Error applying to job:', error);
      alert('Ứng tuyển thất bại. Vui lòng thử lại.');
    } finally {
      setApplying(false);
    }
  };

  const handleQuickAccept = async () => {
    if (!job) return;
    
    const confirmed = confirm('Bạn có muốn chấp nhận công việc này ngay không?');
    if (!confirmed) return;
    
    setApplying(true);
    try {
      const response = await fetch('/api/booksy/albafree/jobs/quick-accept', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jobId: job.id,
          workerId: 'user-001'
        })
      });

      if (response.ok) {
        alert('Công việc đã được xác nhận! Nhà tuyển dụng sẽ liên hệ với bạn sớm.');
        router.push('/booksy/apps/albafree');
      } else {
        throw new Error('Failed to accept job');
      }
    } catch (error) {
      console.error('Error accepting job:', error);
      alert('Chấp nhận thất bại. Vui lòng thử lại.');
    } finally {
      setApplying(false);
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'emergency': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'normal': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getUrgencyLabel = (urgency: string) => {
    switch (urgency) {
      case 'emergency': return 'Rất gấp';
      case 'high': return 'Gấp';
      case 'normal': return 'Bình thường';
      default: return 'Không gấp';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải tin tuyển dụng...</p>
        </div>
      </div>
    );
  }

  if (!job || !employer) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Không tìm thấy tin tuyển dụng</h3>
          <button
            onClick={() => router.back()}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Quay lại
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-md mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <button
              onClick={() => router.back()}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600" />
            </button>
            <h1 className="text-lg font-semibold text-gray-900">Chi tiết tin tuyển dụng</h1>
            <div className="flex items-center space-x-2">
              <button className="p-2 hover:bg-gray-100 rounded-full">
                <Heart className="h-5 w-5 text-gray-600" />
              </button>
              <button className="p-2 hover:bg-gray-100 rounded-full">
                <Share2 className="h-5 w-5 text-gray-600" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Job Header */}
      <div className={`${job.isEmergency ? 'bg-red-50 border-red-200' : 'bg-white'} border-b`}>
        <div className="max-w-md mx-auto px-4 py-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <h1 className="text-xl font-bold text-gray-900">{job.title}</h1>
                {job.bbeonjjeokCall && (
                  <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs font-medium">
                    Gọi khẩn
                  </span>
                )}
              </div>
              <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${getUrgencyColor(job.urgency)}`}>
                {getUrgencyLabel(job.urgency)}
              </span>
            </div>
            {job.isEmergency && (
              <div className="flex items-center space-x-1 text-red-600">
                <AlertTriangle className="h-5 w-5" />
                <span className="text-sm font-medium">Khẩn cấp</span>
              </div>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-green-600" />
              <span className="text-sm">
                {job.budget.min.toLocaleString()}-{job.budget.max.toLocaleString()}đ
                {job.budget.type === 'hourly' ? '/giờ' : ''}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-blue-600" />
              <span className="text-sm">{job.duration}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-purple-600" />
              <span className="text-sm">
                {new Date(job.startsAt).toLocaleDateString('vi-VN')}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4 text-red-600" />
              <span className="text-sm">Gần đây</span>
            </div>
          </div>

          <p className="text-gray-700 mb-4">{job.description}</p>

          {job.tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {job.tags.map((tag, index) => (
                <span
                  key={index}
                  className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-xs"
                >
                  #{tag}
                </span>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Employer Info */}
      <div className="bg-white border-b">
        <div className="max-w-md mx-auto px-4 py-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-3">고용주 정보</h2>
          <div className="flex items-center space-x-3">
            <div className="relative">
              <img
                src={employer.avatar}
                alt={employer.name}
                className="w-12 h-12 rounded-full object-cover"
              />
              {employer.isVerified && (
                <div className="absolute -top-1 -right-1 bg-blue-500 rounded-full p-1">
                  <Shield className="h-3 w-3 text-white" />
                </div>
              )}
            </div>
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <h3 className="font-semibold text-gray-900">{employer.name}</h3>
                <div className="flex items-center space-x-1">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="text-sm font-medium">{employer.rating}</span>
                  <span className="text-xs text-gray-500">({employer.totalRatings})</span>
                </div>
              </div>
              {employer.businessInfo && (
                <div className="text-sm text-gray-600">
                  <div>{employer.businessInfo.businessName}</div>
                  <div className="text-xs text-gray-500">{employer.businessInfo.businessType}</div>
                </div>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <button className="p-2 text-blue-600 hover:bg-blue-50 rounded-full">
                <Phone className="h-4 w-4" />
              </button>
              <button className="p-2 text-green-600 hover:bg-green-50 rounded-full">
                <MessageCircle className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Job Details */}
      <div className="max-w-md mx-auto px-4 py-4 space-y-4">
        {/* Requirements */}
        {job.requirements.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">요구사항</h3>
            <ul className="space-y-2">
              {job.requirements.map((req, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-gray-700">{req}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Benefits */}
        {job.benefits.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">혜택</h3>
            <ul className="space-y-2">
              {job.benefits.map((benefit, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <Star className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-gray-700">{benefit}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Location */}
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">위치</h3>
          <div className="flex items-start space-x-2">
            <MapPin className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
            <span className="text-sm text-gray-700">{job.location.address}</span>
          </div>
        </div>

        {/* Applicants */}
        {job.applicants.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">지원자</h3>
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-blue-500" />
              <span className="text-sm text-gray-700">{job.applicants.length}명이 지원했습니다</span>
            </div>
          </div>
        )}

        {/* Job Status */}
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">공고 정보</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">등록일</span>
              <span className="text-gray-900">
                {new Date(job.postedAt).toLocaleDateString('ko-KR')}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">시작 시간</span>
              <span className="text-gray-900">
                {new Date(job.startsAt).toLocaleString('ko-KR')}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">상태</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                job.status === 'open' ? 'bg-green-100 text-green-700' :
                job.status === 'assigned' ? 'bg-blue-100 text-blue-700' :
                'bg-gray-100 text-gray-700'
              }`}>
                {job.status === 'open' ? '모집중' :
                 job.status === 'assigned' ? '배정됨' :
                 job.status === 'in_progress' ? '진행중' :
                 job.status === 'completed' ? '완료' : '취소됨'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      {job.status === 'open' && (
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t p-4">
          <div className="max-w-md mx-auto">
            {hasApplied ? (
              <div className="text-center py-3">
                <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
                <p className="text-green-600 font-medium">지원 완료</p>
                <p className="text-sm text-gray-500">고용주의 응답을 기다리고 있습니다</p>
              </div>
            ) : (
              <div className="space-y-3">
                {job.isEmergency && (
                  <button
                    onClick={handleQuickAccept}
                    disabled={applying}
                    className="w-full bg-red-600 text-white py-4 rounded-lg font-medium hover:bg-red-700 disabled:bg-gray-400 flex items-center justify-center space-x-2"
                  >
                    {applying ? (
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    ) : (
                      <>
                        <Zap className="h-5 w-5" />
                        <span>즉시 수락</span>
                      </>
                    )}
                  </button>
                )}
                <button
                  onClick={handleApply}
                  disabled={applying}
                  className="w-full bg-blue-600 text-white py-4 rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-400 flex items-center justify-center space-x-2"
                >
                  {applying ? (
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  ) : (
                    <>
                      <CheckCircle className="h-5 w-5" />
                      <span>지원하기</span>
                    </>
                  )}
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {job.status !== 'open' && (
        <div className="fixed bottom-0 left-0 right-0 bg-gray-100 border-t p-4">
          <div className="max-w-md mx-auto text-center">
            <XCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-600 font-medium">
              {job.status === 'assigned' ? '이미 배정된 공고입니다' :
               job.status === 'completed' ? '완료된 공고입니다' :
               '취소된 공고입니다'}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
