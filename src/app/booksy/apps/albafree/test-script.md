# Alba Free (알바프리) - 테스트 스크립트

## 🧪 테스트 개요

이 테스트 스크립트는 Alba Free 앱의 모든 주요 기능을 검증하기 위한 단계별 가이드입니다.

## 📱 테스트 시나리오

### 1. 앱 접속 및 초기 화면
```
URL: http://localhost:3000/booksy/apps/albafree
```

**확인사항:**
- [ ] Alba Free 메인 화면 로드
- [ ] 역할 전환 토글 (구직자 ↔ 고용주) 표시
- [ ] 현재 사용자 상태 표시
- [ ] 검색 및 필터 기능 표시

### 2. 역할 전환 테스트

**2.1 구직자 모드 (Worker Mode)**
- [ ] 토글을 "구직자" 위치로 설정
- [ ] 주변 고용주 목록 표시 확인
- [ ] 긴급 공고 섹션 표시 확인
- [ ] 사용자 카드에 고용주 정보 표시

**2.2 고용주 모드 (Owner Mode)**
- [ ] 토글을 "고용주" 위치로 설정
- [ ] 주변 근로자 목록 표시 확인
- [ ] "공고 등록" 버튼 표시 확인
- [ ] 사용자 카드에 근로자 정보 표시

### 3. 사용자 프로필 테스트

**3.1 프로필 접근**
- [ ] 사용자 카드 클릭
- [ ] 프로필 페이지 로드 확인
- [ ] 기본 정보 표시 (이름, 나이, 평점, 거리)
- [ ] 인증 상태 표시

**3.2 프로필 상세 정보**
- [ ] "개요" 탭: 기본 정보, 스킬, 인증 상태
- [ ] "이력" 탭: 작업 이력 표시
- [ ] "리뷰" 탭: 평점 및 리뷰 표시
- [ ] 연락 버튼 (전화, 메시지) 작동

**3.3 번쩍콜 기능**
- [ ] 긴급 근무 가능한 사용자에게 "번쩍콜" 버튼 표시
- [ ] 번쩍콜 전송 시 확인 메시지 표시

### 4. 공고 등록 테스트 (고용주 모드)

**4.1 공고 등록 페이지 접근**
- [ ] "공고 등록" 버튼 클릭
- [ ] 공고 등록 폼 로드 확인

**4.2 기본 정보 입력**
- [ ] 제목 입력 필드
- [ ] 설명 텍스트 영역
- [ ] 카테고리 선택 드롭다운

**4.3 긴급도 및 시간 설정**
- [ ] 긴급도 선택 (보통/급함/초급함)
- [ ] 시작 시간 설정
- [ ] 근무 시간 입력
- [ ] 초급함 선택 시 번쩍콜 옵션 표시

**4.4 급여 설정**
- [ ] 급여 유형 선택 (시급/고정급)
- [ ] 최소/최대 금액 입력

**4.5 공고 등록 완료**
- [ ] 모든 필수 항목 입력 후 등록 버튼 활성화
- [ ] 등록 성공 메시지 표시
- [ ] 메인 화면으로 리다이렉트

### 5. 공고 상세 및 지원 테스트 (구직자 모드)

**5.1 공고 상세 페이지 접근**
- [ ] 긴급 공고 카드 클릭
- [ ] 공고 상세 페이지 로드

**5.2 공고 정보 확인**
- [ ] 공고 제목 및 설명 표시
- [ ] 긴급도 표시 (색상 구분)
- [ ] 급여, 시간, 위치 정보 표시
- [ ] 고용주 정보 섹션

**5.3 지원 기능**
- [ ] "지원하기" 버튼 표시
- [ ] 긴급 공고의 경우 "즉시 수락" 버튼 표시
- [ ] 지원 후 상태 변경 확인

**5.4 즉시 수락 기능**
- [ ] "즉시 수락" 버튼 클릭
- [ ] 확인 다이얼로그 표시
- [ ] 수락 후 성공 메시지 및 리다이렉트

### 6. API 엔드포인트 테스트

**6.1 사용자 관리 API**
```bash
# 사용자 목록 조회
curl http://localhost:3000/api/booksy/albafree/users

# 특정 사용자 조회
curl http://localhost:3000/api/booksy/albafree/users/user-001

# 현재 사용자 조회
curl http://localhost:3000/api/booksy/albafree/user/current
```

**6.2 위치 기반 API**
```bash
# 주변 근로자 조회
curl "http://localhost:3000/api/booksy/albafree/nearby?role=worker&lat=37.5665&lng=126.9780"

# 주변 고용주 조회
curl "http://localhost:3000/api/booksy/albafree/nearby?role=owner&lat=37.5665&lng=126.9780"
```

**6.3 공고 관리 API**
```bash
# 공고 목록 조회
curl http://localhost:3000/api/booksy/albafree/jobs

# 긴급 공고만 조회
curl "http://localhost:3000/api/booksy/albafree/jobs?emergency=true"

# 특정 공고 조회
curl http://localhost:3000/api/booksy/albafree/jobs/job-001
```

**6.4 번쩍콜 API**
```bash
# 번쩍콜 전송
curl -X POST http://localhost:3000/api/booksy/albafree/bbeonjjeok-call \
  -H "Content-Type: application/json" \
  -d '{"targetUserId":"user-001","senderId":"user-010","message":"긴급 채용!"}'
```

**6.5 지원 API**
```bash
# 공고 지원
curl -X POST http://localhost:3000/api/booksy/albafree/jobs/apply \
  -H "Content-Type: application/json" \
  -d '{"jobId":"job-001","workerId":"user-001","message":"지원합니다!"}'

# 즉시 수락
curl -X POST http://localhost:3000/api/booksy/albafree/jobs/quick-accept \
  -H "Content-Type: application/json" \
  -d '{"jobId":"job-001","workerId":"user-001"}'
```

### 7. 데이터 검증

**7.1 JSON 파일 확인**
```bash
# 사용자 데이터
cat data/apps/booksy/albafree/users.json

# 공고 데이터
cat data/apps/booksy/albafree/jobs.json

# 매칭 데이터
cat data/apps/booksy/albafree/matches.json

# 알림 데이터
cat data/apps/booksy/albafree/notifications.json
```

**7.2 데이터 무결성 확인**
- [ ] 사용자 ID 일관성
- [ ] 공고와 매칭 데이터 연결
- [ ] 알림 데이터 정확성

### 8. 모바일 반응형 테스트

**8.1 화면 크기 테스트**
- [ ] 모바일 (375px): 레이아웃 정상 표시
- [ ] 태블릿 (768px): 적절한 여백 및 크기
- [ ] 데스크톱 (1024px+): 중앙 정렬 및 최대 너비 제한

**8.2 터치 인터페이스**
- [ ] 버튼 크기 적절성 (최소 44px)
- [ ] 스크롤 동작 원활성
- [ ] 터치 피드백 정상 작동

### 9. 성능 테스트

**9.1 로딩 시간**
- [ ] 초기 페이지 로드 < 3초
- [ ] API 응답 시간 < 1초
- [ ] 페이지 전환 < 1초

**9.2 사용자 경험**
- [ ] 로딩 인디케이터 표시
- [ ] 에러 메시지 적절성
- [ ] 성공 피드백 명확성

## ✅ 테스트 체크리스트

### 필수 기능
- [ ] 역할 전환 (구직자 ↔ 고용주)
- [ ] 위치 기반 사용자 매칭
- [ ] 공고 등록 및 관리
- [ ] 번쩍콜 시스템
- [ ] 사용자 프로필 시스템
- [ ] 지원 및 즉시 수락 기능

### 부가 기능
- [ ] 검색 및 필터링
- [ ] 평점 및 리뷰 시스템
- [ ] 알림 시스템
- [ ] 반응형 디자인
- [ ] API 문서화

### 데이터 관리
- [ ] JSON 파일 구조 일관성
- [ ] 데이터 영속성
- [ ] 에러 처리

## 🐛 알려진 이슈

1. **실시간 알림**: 현재 폴링 방식, WebSocket 구현 필요
2. **위치 서비스**: 실제 GPS 연동 필요
3. **인증 시스템**: 실제 사용자 인증 구현 필요
4. **결제 시스템**: 급여 지급 시스템 연동 필요

## 📈 개선 계획

1. **단기**: 실시간 채팅, 푸시 알림
2. **중기**: AI 매칭, 결제 연동
3. **장기**: 모바일 앱, 다국어 지원

---

**테스트 완료 후 모든 체크박스가 체크되었는지 확인하세요!** ✅
