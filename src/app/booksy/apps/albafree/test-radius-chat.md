# AlbaFree Radius Filtering & Chat Test Script

## Overview
This test script verifies the new radius filtering and chat functionality added to AlbaFree.

## New Features Added

### 1. Radius Filtering
- **2 km**: Very close jobs/users only
- **5 km**: Local area
- **10 km**: Default setting, city district level
- **20 km**: Extended area
- **Không giới hạn (No limit)**: For online jobs and remote work

### 2. Job Details
- Click on any job card to view detailed information
- Shows job requirements, benefits, employer info
- Apply or quick-accept functionality

### 3. Chat System
- Click "Chat" button on any user card
- Real-time messaging interface
- Message history persistence
- Read/unread status indicators

## Test Steps

### Step 1: Test Radius Filtering
1. Open AlbaFree: `http://localhost:3000/booksy/apps/albafree`
2. Click the Filter button (funnel icon)
3. Try different radius settings:
   - Set to 2km → Should show very few results
   - Set to 10km → Should show moderate results
   - Set to "Không giới hạn" → Should show all results including online jobs
4. Verify the user count updates correctly

### Step 2: Test Search + Radius Combination
1. Enter search term: "cà phê"
2. Set radius to 5km
3. Verify results are filtered by both criteria
4. Clear search and verify radius filter still works

### Step 3: Test Job Details
1. Switch to "Tìm việc" mode (worker role)
2. Click on any emergency job card (red background)
3. Verify navigation to job detail page
4. Check all job information is displayed correctly
5. Test apply/quick-accept buttons

### Step 4: Test User Profiles
1. Switch to "Tuyển người" mode (owner role) or stay in worker mode
2. Click on any user card (not the Chat button)
3. Verify navigation to user profile page
4. Check all user information is displayed

### Step 5: Test Chat Functionality
1. Click the green "Chat" button on any user card
2. Verify navigation to chat page with correct user
3. Send a test message: "Xin chào! Tôi quan tâm đến công việc này."
4. Verify message appears in chat
5. Check message timestamp and read status
6. Go back and try chatting with different user

### Step 6: Test API Integration
1. Open browser developer tools → Network tab
2. Change radius filter
3. Verify API calls to `/api/booksy/albafree/nearby` with radius parameter
4. Send a chat message
5. Verify API call to `/api/booksy/albafree/chat/send`

## Expected Results

### Radius Filtering
- ✅ Filter dropdown shows all radius options
- ✅ User count updates when radius changes
- ✅ API calls include radius parameter
- ✅ Results are filtered by distance
- ✅ "Không giới hạn" shows all results

### Job Details
- ✅ Job cards are clickable
- ✅ Navigation to `/booksy/apps/albafree/job/[id]` works
- ✅ Job details page shows complete information
- ✅ Apply functionality works

### Chat System
- ✅ Chat buttons are visible on user cards
- ✅ Navigation to `/booksy/apps/albafree/chat/[id]` works
- ✅ Chat interface loads correctly
- ✅ Messages can be sent and received
- ✅ Message history persists
- ✅ Read/unread status works

### User Experience
- ✅ Empty state shows helpful message when no results
- ✅ Loading states work properly
- ✅ Error handling for API failures
- ✅ Responsive design on mobile

## Data Files Created/Modified

### New Files
- `src/app/booksy/apps/albafree/chat/[id]/page.tsx` - Chat interface
- `src/app/api/booksy/albafree/chat/[userId1]/[userId2]/route.ts` - Get messages API
- `src/app/api/booksy/albafree/chat/send/route.ts` - Send message API
- `data/apps/booksy/albafree/chats.json` - Chat messages storage

### Modified Files
- `src/app/booksy/apps/albafree/backup-page.tsx` - Added radius filtering and chat
- `src/app/booksy/apps/albafree/page.tsx` - Updated to use enhanced backup page

## API Endpoints

### Existing (Enhanced)
- `GET /api/booksy/albafree/nearby?radius=10` - Now supports radius parameter
- `GET /api/booksy/albafree/jobs?radius=10` - Now supports radius parameter

### New
- `GET /api/booksy/albafree/chat/[userId1]/[userId2]` - Get chat messages
- `POST /api/booksy/albafree/chat/send` - Send message

## Troubleshooting

### If radius filtering doesn't work:
1. Check if API endpoints support radius parameter
2. Verify user location is set correctly
3. Check browser console for API errors

### If chat doesn't work:
1. Verify chat API endpoints are accessible
2. Check if chat data directory exists
3. Verify user IDs are correct

### If job details don't work:
1. Check if job detail page exists
2. Verify job IDs in data match URL parameters
3. Check API response format

## Success Criteria
- All radius options work correctly
- Job details page loads and functions
- Chat system sends and receives messages
- API integration works without errors
- User experience is smooth and intuitive
