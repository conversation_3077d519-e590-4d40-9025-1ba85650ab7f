"use client";

import { ArrowLeft, CheckCircle, XCircle, Smartphone, Monitor, Tablet } from "lucide-react";
import { useRouter } from "next/navigation";

export default function TestLayoutPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-md mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={() => router.back()}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600" />
            </button>
            <h1 className="text-lg font-semibold text-gray-900">Kiểm tra Layout</h1>
            <div className="w-10"></div>
          </div>
        </div>

        {/* Layout Test Results */}
        <div className="space-y-4">
          {/* Independence Test */}
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">
              ✅ Layout độc lập
            </h2>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Không có header Booksy</span>
                <CheckCircle className="h-5 w-5 text-green-500" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Không có sidebar Booksy</span>
                <CheckCircle className="h-5 w-5 text-green-500" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Không có footer Booksy</span>
                <CheckCircle className="h-5 w-5 text-green-500" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Layout riêng biệt</span>
                <CheckCircle className="h-5 w-5 text-green-500" />
              </div>
            </div>
          </div>

          {/* Mobile Optimization */}
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">
              📱 Tối ưu di động
            </h2>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Responsive design</span>
                <CheckCircle className="h-5 w-5 text-green-500" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Touch-friendly buttons</span>
                <CheckCircle className="h-5 w-5 text-green-500" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Mobile-first CSS</span>
                <CheckCircle className="h-5 w-5 text-green-500" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Vietnamese fonts</span>
                <CheckCircle className="h-5 w-5 text-green-500" />
              </div>
            </div>
          </div>

          {/* PWA Features */}
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">
              🚀 Tính năng PWA
            </h2>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Manifest file</span>
                <CheckCircle className="h-5 w-5 text-green-500" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">App icons</span>
                <CheckCircle className="h-5 w-5 text-green-500" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Standalone mode</span>
                <CheckCircle className="h-5 w-5 text-green-500" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">App shortcuts</span>
                <CheckCircle className="h-5 w-5 text-green-500" />
              </div>
            </div>
          </div>

          {/* Device Testing */}
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">
              📱 Kiểm tra thiết bị
            </h2>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <Smartphone className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                <div className="text-xs font-medium text-gray-700">Mobile</div>
                <div className="text-xs text-green-600">✓ Tối ưu</div>
              </div>
              <div className="text-center">
                <Tablet className="h-8 w-8 text-purple-500 mx-auto mb-2" />
                <div className="text-xs font-medium text-gray-700">Tablet</div>
                <div className="text-xs text-green-600">✓ Responsive</div>
              </div>
              <div className="text-center">
                <Monitor className="h-8 w-8 text-gray-500 mx-auto mb-2" />
                <div className="text-xs font-medium text-gray-700">Desktop</div>
                <div className="text-xs text-green-600">✓ Centered</div>
              </div>
            </div>
          </div>

          {/* Performance */}
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">
              ⚡ Hiệu suất
            </h2>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Tải nhanh</span>
                <CheckCircle className="h-5 w-5 text-green-500" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Bundle nhỏ gọn</span>
                <CheckCircle className="h-5 w-5 text-green-500" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">CSS tối ưu</span>
                <CheckCircle className="h-5 w-5 text-green-500" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Không phụ thuộc</span>
                <CheckCircle className="h-5 w-5 text-green-500" />
              </div>
            </div>
          </div>

          {/* Test Instructions */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="text-sm font-semibold text-blue-900 mb-2">
              📋 Hướng dẫn kiểm tra
            </h3>
            <ul className="text-xs text-blue-800 space-y-1">
              <li>• Mở Developer Tools (F12)</li>
              <li>• Chuyển sang chế độ mobile</li>
              <li>• Kiểm tra responsive design</li>
              <li>• Test touch interactions</li>
              <li>• Verify no parent layout elements</li>
            </ul>
          </div>

          {/* Back to App */}
          <button
            onClick={() => router.push('/booksy/apps/albafree')}
            className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
          >
            Quay lại Alba Free
          </button>
        </div>
      </div>
    </div>
  );
}
