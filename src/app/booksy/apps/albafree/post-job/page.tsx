"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  MapPin,
  Clock,
  DollarSign,
  Calendar,
  AlertTriangle,
  Zap,
  Users,
  FileText,
  Plus,
  Minus,
  CheckCircle
} from "lucide-react";

interface JobForm {
  title: string;
  description: string;
  category: string;
  urgency: 'low' | 'normal' | 'high' | 'emergency';
  budget: {
    min: number;
    max: number;
    currency: string;
    type: 'hourly' | 'fixed';
  };
  duration: string;
  location: {
    address: string;
    lat: number;
    lng: number;
  };
  requirements: string[];
  startsAt: string;
  bbeonjjeokCall: boolean;
  tags: string[];
  benefits: string[];
}

export default function PostJobPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<any[]>([]);
  const [newRequirement, setNewRequirement] = useState("");
  const [newBenefit, setNewBenefit] = useState("");
  
  const [jobForm, setJobForm] = useState<JobForm>({
    title: "",
    description: "",
    category: "",
    urgency: "normal",
    budget: {
      min: 10000,
      max: 15000,
      currency: "KRW",
      type: "hourly"
    },
    duration: "",
    location: {
      address: "",
      lat: 37.5665,
      lng: 126.9780
    },
    requirements: [],
    startsAt: "",
    bbeonjjeokCall: false,
    tags: [],
    benefits: []
  });

  useEffect(() => {
    loadCategories();
    // Set default start time to current time + 1 hour
    const defaultStartTime = new Date();
    defaultStartTime.setHours(defaultStartTime.getHours() + 1);
    setJobForm(prev => ({
      ...prev,
      startsAt: defaultStartTime.toISOString().slice(0, 16)
    }));
  }, []);

  const loadCategories = async () => {
    try {
      const response = await fetch('/api/booksy/albafree/config');
      const config = await response.json();
      setCategories(config.categories || []);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!jobForm.title || !jobForm.description || !jobForm.category) {
      alert('Vui lòng điền đầy đủ các mục bắt buộc.');
      return;
    }

    setLoading(true);
    
    try {
      const response = await fetch('/api/booksy/albafree/jobs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...jobForm,
          postedBy: 'user-010', // Current user ID
          isEmergency: jobForm.urgency === 'emergency'
        })
      });

      if (response.ok) {
        const newJob = await response.json();
        
        // If it's an emergency job with bbeonjjeok call, send notifications
        if (jobForm.bbeonjjeokCall && jobForm.urgency === 'emergency') {
          await sendBbeonjjeokCallToNearbyWorkers(newJob.id);
        }
        
        alert('Tin tuyển dụng đã được đăng thành công!');
        router.push('/booksy/apps/albafree');
      } else {
        throw new Error('Failed to create job');
      }
    } catch (error) {
      console.error('Error creating job:', error);
      alert('Đăng tin thất bại. Vui lòng thử lại.');
    } finally {
      setLoading(false);
    }
  };

  const sendBbeonjjeokCallToNearbyWorkers = async (jobId: string) => {
    try {
      // Get nearby workers
      const response = await fetch(`/api/booksy/albafree/nearby?role=worker&lat=${jobForm.location.lat}&lng=${jobForm.location.lng}&radius=3`);
      const nearbyWorkers = await response.json();
      
      // Send bbeonjjeok call to each worker
      for (const worker of nearbyWorkers.slice(0, 10)) { // Limit to 10 workers
        await fetch('/api/booksy/albafree/bbeonjjeok-call', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            targetUserId: worker.id,
            senderId: 'user-010',
            jobId: jobId,
            message: `Tuyển dụng khẩn cấp! ${jobForm.title} - Tìm người có thể làm việc ngay!`
          })
        });
      }
    } catch (error) {
      console.error('Error sending bbeonjjeok calls:', error);
    }
  };

  const addRequirement = () => {
    if (newRequirement.trim()) {
      setJobForm(prev => ({
        ...prev,
        requirements: [...prev.requirements, newRequirement.trim()]
      }));
      setNewRequirement("");
    }
  };

  const removeRequirement = (index: number) => {
    setJobForm(prev => ({
      ...prev,
      requirements: prev.requirements.filter((_, i) => i !== index)
    }));
  };

  const addBenefit = () => {
    if (newBenefit.trim()) {
      setJobForm(prev => ({
        ...prev,
        benefits: [...prev.benefits, newBenefit.trim()]
      }));
      setNewBenefit("");
    }
  };

  const removeBenefit = (index: number) => {
    setJobForm(prev => ({
      ...prev,
      benefits: prev.benefits.filter((_, i) => i !== index)
    }));
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'emergency': return 'bg-red-100 text-red-700 border-red-300';
      case 'high': return 'bg-orange-100 text-orange-700 border-orange-300';
      case 'normal': return 'bg-blue-100 text-blue-700 border-blue-300';
      default: return 'bg-gray-100 text-gray-700 border-gray-300';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-md mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <button
              onClick={() => router.back()}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              <ArrowLeft className="h-5 w-5 text-gray-600" />
            </button>
            <h1 className="text-lg font-semibold text-gray-900">Đăng tin tuyển dụng</h1>
            <div className="w-10"></div>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="max-w-md mx-auto px-4 py-6 space-y-6">
        {/* Basic Info */}
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Thông tin cơ bản</h2>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tiêu đề *
              </label>
              <input
                type="text"
                value={jobForm.title}
                onChange={(e) => setJobForm(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Ví dụ: Cần gấp nhân viên phục vụ quán cà phê!"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Mô tả *
              </label>
              <textarea
                value={jobForm.description}
                onChange={(e) => setJobForm(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Mô tả chi tiết nội dung công việc..."
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Danh mục *
              </label>
              <select
                value={jobForm.category}
                onChange={(e) => setJobForm(prev => ({ ...prev, category: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                <option value="">Chọn danh mục</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.name}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Urgency & Timing */}
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Mức độ khẩn cấp & Thời gian</h2>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Mức độ khẩn cấp
              </label>
              <div className="grid grid-cols-2 gap-2">
                {[
                  { value: 'normal', label: 'Bình thường', desc: 'Trong 4 giờ' },
                  { value: 'high', label: 'Gấp', desc: 'Trong 1 giờ' },
                  { value: 'emergency', label: 'Rất gấp', desc: 'Trong 15 phút' }
                ].map((urgency) => (
                  <button
                    key={urgency.value}
                    type="button"
                    onClick={() => setJobForm(prev => ({
                      ...prev,
                      urgency: urgency.value as any,
                      bbeonjjeokCall: urgency.value === 'emergency' ? true : prev.bbeonjjeokCall
                    }))}
                    className={`p-3 border-2 rounded-lg text-left ${
                      jobForm.urgency === urgency.value
                        ? getUrgencyColor(urgency.value)
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="font-medium">{urgency.label}</div>
                    <div className="text-xs text-gray-500">{urgency.desc}</div>
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Thời gian bắt đầu *
              </label>
              <input
                type="datetime-local"
                value={jobForm.startsAt}
                onChange={(e) => setJobForm(prev => ({ ...prev, startsAt: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Thời gian làm việc
              </label>
              <input
                type="text"
                value={jobForm.duration}
                onChange={(e) => setJobForm(prev => ({ ...prev, duration: e.target.value }))}
                placeholder="Ví dụ: 4 giờ, 2h chiều - 6h chiều"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {jobForm.urgency === 'emergency' && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-center space-x-2 mb-2">
                  <Zap className="h-5 w-5 text-yellow-600" />
                  <span className="font-medium text-yellow-800">Tính năng gọi khẩn cấp</span>
                </div>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={jobForm.bbeonjjeokCall}
                    onChange={(e) => setJobForm(prev => ({ ...prev, bbeonjjeokCall: e.target.checked }))}
                    className="rounded border-gray-300 text-yellow-600 focus:ring-yellow-500"
                  />
                  <span className="text-sm text-yellow-700">
                    Gửi thông báo ngay lập tức cho người lao động xung quanh
                  </span>
                </label>
              </div>
            )}
          </div>
        </div>

        {/* Budget */}
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Lương</h2>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Loại lương
              </label>
              <div className="flex space-x-2">
                <button
                  type="button"
                  onClick={() => setJobForm(prev => ({
                    ...prev,
                    budget: { ...prev.budget, type: 'hourly' }
                  }))}
                  className={`flex-1 py-2 px-3 border rounded-lg ${
                    jobForm.budget.type === 'hourly'
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                >
                  Theo giờ
                </button>
                <button
                  type="button"
                  onClick={() => setJobForm(prev => ({
                    ...prev,
                    budget: { ...prev.budget, type: 'fixed' }
                  }))}
                  className={`flex-1 py-2 px-3 border rounded-lg ${
                    jobForm.budget.type === 'fixed'
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                >
                  Cố định
                </button>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Số tiền tối thiểu
                </label>
                <div className="relative">
                  <input
                    type="number"
                    value={jobForm.budget.min}
                    onChange={(e) => setJobForm(prev => ({
                      ...prev,
                      budget: { ...prev.budget, min: parseInt(e.target.value) || 0 }
                    }))}
                    className="w-full px-3 py-2 pr-8 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <span className="absolute right-2 top-2 text-gray-500 text-sm">đ</span>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Số tiền tối đa
                </label>
                <div className="relative">
                  <input
                    type="number"
                    value={jobForm.budget.max}
                    onChange={(e) => setJobForm(prev => ({
                      ...prev,
                      budget: { ...prev.budget, max: parseInt(e.target.value) || 0 }
                    }))}
                    className="w-full px-3 py-2 pr-8 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <span className="absolute right-2 top-2 text-gray-500 text-sm">đ</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <button
            type="submit"
            disabled={loading}
            className={`w-full py-4 rounded-lg font-medium flex items-center justify-center space-x-2 ${
              loading
                ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>Đang đăng...</span>
              </>
            ) : (
              <>
                <CheckCircle className="h-5 w-5" />
                <span>Đăng tin tuyển dụng</span>
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
