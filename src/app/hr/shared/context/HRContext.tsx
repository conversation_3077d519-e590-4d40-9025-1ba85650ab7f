'use client';

import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { Permission<PERSON>hecker } from '../middleware/permissions';
import { useHRAuth, UseHRAuthReturn } from '../../hooks/useHRAuth';
import { HROneIDUser } from '../../services/HROneIDIntegration';

// User interface - extend from OneID integration
export interface HRUser extends HROneIDUser {
  currentModule?: string;
}

// Module state interface
export interface ModuleState {
  currentModule: string;
  breadcrumbs: Array<{ title: string; href?: string }>;
  pageTitle?: string;
  loading: boolean;
  error?: string;
}

// Context interface
export interface HRContextType extends Omit<UseHRAuthReturn, 'user'> {
  // User state (overridden from UseHRAuthReturn)
  user: HRUser | null;
  
  // Module state
  moduleState: ModuleState;
  setModuleState: (state: Partial<ModuleState>) => void;
  
  // Override permission checking methods (already provided by UseHRAuthReturn)
  canAccessModule: (module: string) => boolean;
  
  // Navigation state
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
  
  // Notifications
  notifications: Array<{
    id: string;
    type: 'info' | 'success' | 'warning' | 'error';
    title: string;
    message: string;
    timestamp: string;
    read: boolean;
  }>;
  addNotification: (notification: Omit<HRContextType['notifications'][0], 'id' | 'timestamp' | 'read'>) => void;
  markNotificationRead: (id: string) => void;
  clearNotifications: () => void;
  
  // Cross-module data sharing
  sharedData: Record<string, any>;
  setSharedData: (key: string, value: any) => void;
  getSharedData: (key: string) => any;
  
  // Loading states
  globalLoading: boolean;
  setGlobalLoading: (loading: boolean) => void;
}

// Create context
const HRContext = createContext<HRContextType | undefined>(undefined);

// Default module state
const defaultModuleState: ModuleState = {
  currentModule: 'dashboard',
  breadcrumbs: [],
  pageTitle: undefined,
  loading: false,
  error: undefined
};

// Provider component
export function HRProvider({ children }: { children: ReactNode }) {
  // Use OneID authentication
  const auth = useHRAuth();
  
  // Local state for HR-specific features
  const [moduleState, setModuleStateInternal] = useState<ModuleState>(defaultModuleState);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [notifications, setNotifications] = useState<HRContextType['notifications']>([]);
  const [sharedData, setSharedDataInternal] = useState<Record<string, any>>({});
  const [globalLoading, setGlobalLoading] = useState(false);
  const [permissionChecker, setPermissionChecker] = useState<PermissionChecker | null>(null);

  // Initialize permission checker when user changes
  useEffect(() => {
    if (auth.user) {
      setPermissionChecker(new PermissionChecker(auth.user.roles));
    } else {
      setPermissionChecker(null);
    }
  }, [auth.user]);

  // Load shared data from localStorage
  useEffect(() => {
    const savedSharedData = localStorage.getItem('hr-shared-data');
    if (savedSharedData) {
      try {
        const parsedData = JSON.parse(savedSharedData);
        setSharedDataInternal(parsedData);
      } catch (error) {
        console.error('Failed to parse shared data:', error);
        localStorage.removeItem('hr-shared-data');
      }
    }
  }, []);

  // Save shared data to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('hr-shared-data', JSON.stringify(sharedData));
  }, [sharedData]);

  // Helper functions
  const setModuleState = useCallback((state: Partial<ModuleState>) => {
    setModuleStateInternal(prev => ({ ...prev, ...state }));
  }, []);

  // Override canAccessModule to use HR-specific logic
  const canAccessModule = useCallback((module: string): boolean => {
    // Use the auth's hasModuleAccess method which includes HR-specific logic
    return auth.hasModuleAccess(module);
  }, [auth.hasModuleAccess]);

  const markNotificationRead = useCallback((id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id
          ? { ...notification, read: true }
          : notification
      )
    );
  }, []);

  const addNotification = useCallback((notification: Omit<HRContextType['notifications'][0], 'id' | 'timestamp' | 'read'>) => {
    const newNotification = {
      ...notification,
      id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      read: false
    };

    setNotifications(prev => [newNotification, ...prev]);

    // Auto-remove info notifications after 5 seconds
    if (notification.type === 'info') {
      setTimeout(() => {
        markNotificationRead(newNotification.id);
      }, 5000);
    }
  }, [markNotificationRead]);



  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  const setSharedData = useCallback((key: string, value: any) => {
    setSharedDataInternal(prev => ({ ...prev, [key]: value }));
  }, []);

  const getSharedData = useCallback((key: string) => {
    return sharedData[key];
  }, [sharedData]);

  const setGlobalLoadingCallback = useCallback((loading: boolean) => {
    setGlobalLoading(loading);
  }, []);

  // Context value - merge auth with HR-specific functionality
  const contextValue: HRContextType = {
    // Spread all auth functionality (login, logout, permissions, etc.)
    ...auth,
    // Override user to be HRUser type
    user: auth.user,
    // Add HR-specific module and UI state
    moduleState,
    setModuleState,
    canAccessModule,
    sidebarOpen,
    setSidebarOpen,
    notifications,
    addNotification,
    markNotificationRead,
    clearNotifications,
    sharedData,
    setSharedData,
    getSharedData,
    globalLoading,
    setGlobalLoading: setGlobalLoadingCallback
  };

  return (
    <HRContext.Provider value={contextValue}>
      {children}
    </HRContext.Provider>
  );
}

// Hook to use the context
export function useHR(): HRContextType {
  const context = useContext(HRContext);
  if (context === undefined) {
    throw new Error('useHR must be used within an HRProvider');
  }
  return context;
}

// Hook for module-specific functionality
export function useModule(moduleName: string) {
  const { setModuleState, hasPermission, canAccessModule, addNotification } = useHR();

  // Set current module
  useEffect(() => {
    setModuleState({ currentModule: moduleName });
  }, [moduleName, setModuleState]);

  // Check module access
  const hasAccess = canAccessModule(moduleName);

  // Module-specific notification
  const addModuleNotification = (notification: Omit<HRContextType['notifications'][0], 'id' | 'timestamp' | 'read'>) => {
    addNotification({
      ...notification,
      title: `[${moduleName.toUpperCase()}] ${notification.title}`
    });
  };

  return {
    hasAccess,
    hasPermission,
    addNotification: addModuleNotification,
    setModuleState
  };
}

// Hook for cross-module data sharing
export function useCrossModuleData() {
  const { sharedData, setSharedData, getSharedData } = useHR();

  const shareEntity = (entityType: string, entityId: string, data: any) => {
    const key = `${entityType}:${entityId}`;
    setSharedData(key, {
      ...data,
      sharedAt: new Date().toISOString(),
      sharedBy: entityType
    });
  };

  const getSharedEntity = (entityType: string, entityId: string) => {
    const key = `${entityType}:${entityId}`;
    return getSharedData(key);
  };

  const shareWorkflowData = (workflowId: string, data: any) => {
    const key = `workflow:${workflowId}`;
    setSharedData(key, {
      ...data,
      sharedAt: new Date().toISOString()
    });
  };

  const getWorkflowData = (workflowId: string) => {
    const key = `workflow:${workflowId}`;
    return getSharedData(key);
  };

  return {
    shareEntity,
    getSharedEntity,
    shareWorkflowData,
    getWorkflowData,
    allSharedData: sharedData
  };
}

// Hook for notifications
export function useNotifications() {
  const { notifications, addNotification, markNotificationRead, clearNotifications } = useHR();

  const unreadCount = notifications.filter(n => !n.read).length;
  const recentNotifications = notifications.slice(0, 10);

  const addSuccessNotification = (title: string, message: string) => {
    addNotification({ type: 'success', title, message });
  };

  const addErrorNotification = (title: string, message: string) => {
    addNotification({ type: 'error', title, message });
  };

  const addWarningNotification = (title: string, message: string) => {
    addNotification({ type: 'warning', title, message });
  };

  const addInfoNotification = (title: string, message: string) => {
    addNotification({ type: 'info', title, message });
  };

  return {
    notifications,
    unreadCount,
    recentNotifications,
    addNotification,
    addSuccessNotification,
    addErrorNotification,
    addWarningNotification,
    addInfoNotification,
    markNotificationRead,
    clearNotifications
  };
}

// Export context for direct access if needed
export { HRContext };
