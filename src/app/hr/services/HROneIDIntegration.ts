'use client';

import { HRUser } from '../shared/services/HRDataSync';

export interface HROneIDUser extends HRUser {
  // OneID specific fields
  oneIdToken?: string;
  universalToken?: string;
  loginTimestamp?: string;
  sessionExpiry?: string;
  // HR-specific extensions
  employeeId?: string;
  department?: string;
  position?: string;
  managerId?: string;
  companyId?: string;
  companyName?: string;
}

export interface HRAuthResult {
  success: boolean;
  user?: HROneIDUser;
  token?: string;
  error?: string;
}

export class HROneIDIntegration {
  private static instance: HROneIDIntegration | null = null;

  static getInstance(): HROneIDIntegration {
    if (!HROneIDIntegration.instance) {
      HROneIDIntegration.instance = new HROneIDIntegration();
    }
    return HROneIDIntegration.instance;
  }

  // Initialize and check for existing OneID session
  async initialize(): Promise<HRAuthResult> {
    try {
      // Check for existing tokens in localStorage (universal session)
      const tokens = this.getStoredTokens();
      console.log('HR Init: Found tokens:', tokens);
      
      // Also check for EDU tokens as they should work universally
      const eduToken = localStorage.getItem('edu_oneid_token');
      const availableToken = tokens.oneIdToken || tokens.universalToken || eduToken;
      
      if (availableToken) {
        console.log('HR Init: Validating token:', availableToken.substring(0, 20) + '...');
        // Validate existing session
        const validation = await this.validateSession(availableToken);
        if (validation.success && validation.user) {
          console.log('HR Init: Session validation successful');
          // Store the validated session in HR format
          this.storeTokens(availableToken, validation.user);
          return validation;
        } else {
          console.log('HR Init: Session validation failed');
        }
      } else {
        console.log('HR Init: No tokens found');
      }

      return { success: false, error: 'No valid session found' };
    } catch (error) {
      console.error('HR OneID initialization failed:', error);
      return { success: false, error: 'Initialization failed' };
    }
  }

  // Authenticate user with OneID
  async authenticateUser(username: string, password: string): Promise<HRAuthResult> {
    try {
      const response = await fetch('/api/backbone/oneid/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
      });

      const data = await response.json();

      if (data.success && data.user && data.token) {
        // Convert OneID user to HR user format
        const hrUser = this.convertToHRUser(data.user, data.token);
        
        // Store tokens for universal session
        this.storeTokens(data.token, hrUser);
        
        return { 
          success: true, 
          user: hrUser, 
          token: data.token 
        };
      } else {
        return { 
          success: false, 
          error: data.error || 'Authentication failed' 
        };
      }
    } catch (error) {
      console.error('HR OneID authentication failed:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Network error' 
      };
    }
  }

  // Validate existing session
  async validateSession(token: string): Promise<HRAuthResult> {
    try {
      console.log('HR Validate: Validating token type:', token.substring(0, 20) + '...');

      // For OneID universal tokens, do basic validation
      if (token.startsWith('universal_token_')) {
        console.log('HR Validate: Universal token detected');
        // Get user data from localStorage
        const userData = this.getStoredUserData();
        if (userData) {
          console.log('HR Validate: Found stored user data');
          return { success: true, user: userData };
        } else {
          // Try to get universal user data and convert to HR format
          const universalUserData = localStorage.getItem('universal_auth_user');
          if (universalUserData) {
            console.log('HR Validate: Converting universal user to HR format');
            const universalUser = JSON.parse(universalUserData);
            const hrUser = this.convertToHRUser(universalUser, token);
            return { success: true, user: hrUser };
          }
        }
      }

      // Try to decode base64 token (for test tokens like eduwise_student)
      try {
        console.log('HR Validate: Trying base64 token decode');
        const tokenData = JSON.parse(Buffer.from(token, 'base64').toString());
        console.log('HR Validate: Base64 token decoded successfully for user:', tokenData.username);

        // Check if token is expired
        if (tokenData.exp && tokenData.exp < Math.floor(Date.now() / 1000)) {
          console.log('HR Validate: Base64 token expired');
          return { success: false, error: 'Token expired' };
        }

        // Create HR user from token data
        const hrUser = this.convertToHRUser(tokenData, token);
        console.log('HR Validate: Converted base64 token to HR user:', hrUser.name);
        return { success: true, user: hrUser };
      } catch (base64Error) {
        console.log('HR Validate: Base64 decode failed, trying server validation');
      }

      // Try server-side validation for other token types (like edu_oneid_token)
      console.log('HR Validate: Trying server-side validation');
      try {
        const response = await fetch('/api/platforms/edu/auth/oneid-validate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ token })
        });

        if (response.ok) {
          const validation = await response.json();
          console.log('HR Validate: Server validation response:', validation);

          if (validation.valid && validation.user) {
            const hrUser = this.convertToHRUser(validation.user, token);
            console.log('HR Validate: Converted to HR user:', hrUser.name);
            return { success: true, user: hrUser };
          }
        } else {
          console.log('HR Validate: Server validation failed with status:', response.status);
        }
      } catch (serverError) {
        console.log('HR Validate: Server validation error:', serverError);
      }

      console.log('HR Validate: All validation methods failed');
      return { success: false, error: 'Session validation failed' };
    } catch (error) {
      console.error('Session validation error:', error);
      return { success: false, error: 'Validation error' };
    }
  }

  // Logout user and clear session
  async logout(token?: string): Promise<void> {
    try {
      if (token) {
        // Attempt server-side logout
        await fetch('/api/platforms/edu/auth/oneid-logout', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ token })
        });
      }
    } catch (error) {
      console.warn('Server logout failed:', error);
    } finally {
      // Always clear local storage
      this.clearStoredTokens();
    }
  }

  // Convert OneID user to HR user format
  private convertToHRUser(oneIdUser: any, token: string): HROneIDUser {
    // Map HR-specific roles based on OneID user data
    const hrRoles = this.mapToHRRoles(oneIdUser);
    const hrPermissions = this.mapToHRPermissions(hrRoles, oneIdUser);

    return {
      id: oneIdUser.id || oneIdUser.username,
      name: oneIdUser.name || `${oneIdUser.firstName || ''} ${oneIdUser.lastName || ''}`.trim() || oneIdUser.username,
      email: oneIdUser.email,
      avatar: oneIdUser.avatar,
      roles: hrRoles,
      permissions: hrPermissions,
      oneIdToken: token,
      universalToken: token.startsWith('universal_token_') ? token : undefined,
      loginTimestamp: new Date().toISOString(),
      sessionExpiry: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
      // HR-specific fields
      employeeId: oneIdUser.employeeId,
      department: oneIdUser.department || 'General',
      position: oneIdUser.position || 'Employee',
      managerId: oneIdUser.managerId,
      companyId: oneIdUser.companyId,
      companyName: oneIdUser.companyName,
      preferences: {
        language: 'en',
        theme: 'light',
        notifications: true
      }
    };
  }

  // Map OneID user data to HR roles
  private mapToHRRoles(user: any): string[] {
    const roles: string[] = [];

    // Default role
    roles.push('hr-employee');

    // Map based on user type or specific fields
    if (user.userType === 'admin' || user.role === 'admin') {
      roles.push('hr-admin');
    }

    if (user.userType === 'manager' || user.role === 'manager' || user.position?.toLowerCase().includes('manager')) {
      roles.push('hr-manager');
    }

    if (user.companyId && user.role === 'corporate_admin') {
      roles.push('hr-company-admin');
    }

    // Add module-specific roles based on user permissions
    if (user.permissions?.includes('recruitment:access')) {
      roles.push('hr-recruiter');
    }

    if (user.permissions?.includes('payroll:access')) {
      roles.push('hr-payroll');
    }

    return roles;
  }

  // Map HR roles to specific permissions
  private mapToHRPermissions(roles: string[], user: any): string[] {
    const permissions: string[] = [];

    // Base permissions for all HR users
    permissions.push('hr:access', 'hr:profile:read');

    roles.forEach(role => {
      switch (role) {
        case 'hr-admin':
          permissions.push(
            'hr:admin:access',
            'hr:users:manage',
            'hr:modules:all',
            'hr:reports:all',
            'hr:settings:manage'
          );
          break;

        case 'hr-manager':
          permissions.push(
            'hr:manager:access',
            'hr:team:manage',
            'hr:reports:team',
            'hr:recruitment:manage',
            'hr:onboarding:manage'
          );
          break;

        case 'hr-recruiter':
          permissions.push(
            'hr:recruitment:access',
            'hr:jobs:manage',
            'hr:candidates:manage',
            'hr:hunter:access',
            'hr:upwork:access'
          );
          break;

        case 'hr-payroll':
          permissions.push(
            'hr:payroll:access',
            'hr:payroll:process',
            'hr:reports:payroll'
          );
          break;

        case 'hr-company-admin':
          permissions.push(
            'hr:company:admin',
            'hr:company:manage',
            'hr:employees:manage',
            'hr:contracts:manage'
          );
          break;

        case 'hr-employee':
          permissions.push(
            'hr:employee:access',
            'hr:profile:update',
            'hr:jobs:view',
            'hr:onboarding:access'
          );
          break;
      }
    });

    return [...new Set(permissions)]; // Remove duplicates
  }

  // Get stored tokens from localStorage and cookies
  private getStoredTokens(): { oneIdToken?: string; universalToken?: string } {
    // Check localStorage first
    const localOneIdToken = localStorage.getItem('hr_oneid_token');
    const localUniversalToken = localStorage.getItem('universal_auth_token');

    // Check cookies as fallback (for middleware-set tokens)
    const cookieOneIdToken = this.getCookie('hr_oneid_token') || this.getCookie('oneid-token');
    const cookieUniversalToken = this.getCookie('universal_auth_token');
    const cookieEduToken = this.getCookie('edu_oneid_token');

    return {
      oneIdToken: localOneIdToken || cookieOneIdToken || cookieEduToken || undefined,
      universalToken: localUniversalToken || cookieUniversalToken || undefined
    };
  }

  // Get stored user data
  private getStoredUserData(): HROneIDUser | null {
    const userData = localStorage.getItem('hr_oneid_user') || localStorage.getItem('universal_auth_user');
    if (userData) {
      try {
        return JSON.parse(userData);
      } catch (error) {
        console.error('Failed to parse stored user data:', error);
      }
    }
    return null;
  }

  // Store tokens and user data
  private storeTokens(token: string, user: HROneIDUser): void {
    // Store HR-specific tokens in localStorage
    localStorage.setItem('hr_oneid_token', token);
    localStorage.setItem('hr_oneid_user', JSON.stringify(user));

    // Also store as universal tokens for cross-platform session
    if (token.startsWith('universal_token_')) {
      localStorage.setItem('universal_auth_token', token);
      localStorage.setItem('universal_auth_user', JSON.stringify(user));
    }

    // Store legacy HR user format for compatibility
    localStorage.setItem('hr-user', JSON.stringify(user));

    // Store tokens in cookies for middleware access
    this.setCookie('hr_oneid_token', token, 24); // 24 hours
    if (token.startsWith('universal_token_')) {
      this.setCookie('universal_auth_token', token, 24);
    }
  }

  // Clear stored tokens and user data
  private clearStoredTokens(): void {
    // Clear HR-specific storage
    localStorage.removeItem('hr_oneid_token');
    localStorage.removeItem('hr_oneid_user');
    
    // Clear universal storage
    localStorage.removeItem('universal_auth_token');
    localStorage.removeItem('universal_auth_user');
    
    // Clear legacy HR storage
    localStorage.removeItem('hr-user');
    localStorage.removeItem('hr-shared-data');

    // Clear cookies
    this.deleteCookie('hr_oneid_token');
    this.deleteCookie('universal_auth_token');
    this.deleteCookie('edu_oneid_token');
  }

  // Check if user has access to specific HR module
  hasModuleAccess(user: HROneIDUser, module: string): boolean {
    const modulePermissions: { [key: string]: string[] } = {
      'hunter': ['hr:recruitment:access', 'hr:jobs:manage'],
      'upwork': ['hr:recruitment:access', 'hr:upwork:access'],
      'abnrfp': ['hr:procurement:access', 'hr:manager:access'],
      'abnreferee': ['hr:legal:access', 'hr:disputes:manage'],
      'hrm': ['hr:employee:access', 'hr:hrm:access'],
      'headhunter': ['hr:recruitment:access', 'hr:ai:access'],
      'onboarding': ['hr:onboarding:access', 'hr:employee:access'],
      'talents': ['hr:global:access', 'hr:eor:access']
    };

    const requiredPermissions = modulePermissions[module] || ['hr:access'];
    return requiredPermissions.some(permission => user.permissions.includes(permission));
  }

  // Cookie helper methods for middleware access
  private getCookie(name: string): string | null {
    if (typeof document === 'undefined') return null;

    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
      return parts.pop()?.split(';').shift() || null;
    }
    return null;
  }

  private setCookie(name: string, value: string, hours: number): void {
    const expires = new Date();
    expires.setTime(expires.getTime() + (hours * 60 * 60 * 1000));
    document.cookie = `${name}=${value}; expires=${expires.toUTCString()}; path=/; SameSite=Lax`;
  }

  private deleteCookie(name: string): void {
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax`;
  }
}