'use client'

import React, { useState } from 'react'
import Header from '@/components/Header'
import Section from '@/components/section'
import NewsEditor from '@/components/news/NewsEditor'
import AdminAuthWrapper from '@/components/auth/AdminAuthWrapper'

const NewsAdmin = () => {
  return (
    <AdminAuthWrapper>
      <>
        <Header />
        <Section>
          <h2 className='text-xl font-semibold'>Quản lý tin tức</h2>
          <div className='mt-4'>
            <NewsEditor />
          </div>
        </Section>
      </>
    </AdminAuthWrapper>
  )
}

export default NewsAdmin