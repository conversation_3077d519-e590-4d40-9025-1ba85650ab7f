import 'server-only';
import fs from 'fs';
import path from 'path';
import { DatabaseConfig, StorageType, ConnectionConfig } from '../core/interfaces';

/**
 * Configuration manager for the unified database access layer
 */
export class ConfigManager {
  private static instance: ConfigManager;
  private configs: Map<string, DatabaseConfig> = new Map();
  private configPath: string;
  private defaultConfig: Partial<DatabaseConfig>;
  
  private constructor() {
    this.configPath = path.join(process.cwd(), 'data/apps/backbone/dataaccess/config');
    this.defaultConfig = {
      storage: {
        type: 'json',
        connection: {
          path: path.join(process.cwd(), 'data/apps/backbone/dataaccess')
        },
        fallback: 'json',
        options: {
          cache: true,
          timeout: 30000,
          retries: 3
        }
      },
      schema: {
        validation: false,
        strict: false,
        migrations: true,
        versioning: false
      },
      performance: {
        monitoring: true,
        logging: true,
        metrics: false,
        caching: true
      }
    };
    
    this.ensureConfigDirectory();
  }
  
  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }
  
  private ensureConfigDirectory(): void {
    if (!fs.existsSync(this.configPath)) {
      fs.mkdirSync(this.configPath, { recursive: true });
    }
  }
  
  /**
   * Get configuration for an app
   */
  public async getConfig(appName: string): Promise<DatabaseConfig> {
    // Check cache first
    if (this.configs.has(appName)) {
      return this.configs.get(appName)!;
    }
    
    // Try to load from file
    const config = await this.loadConfigFromFile(appName);
    if (config) {
      this.configs.set(appName, config);
      return config;
    }
    
    // Create default config
    const defaultConfig = this.createDefaultConfig(appName);
    await this.saveConfig(appName, defaultConfig);
    return defaultConfig;
  }
  
  /**
   * Save configuration for an app
   */
  public async saveConfig(appName: string, config: DatabaseConfig): Promise<void> {
    const configFile = path.join(this.configPath, `${appName}.json`);
    
    try {
      await fs.promises.writeFile(configFile, JSON.stringify(config, null, 2));
      this.configs.set(appName, config);
    } catch (error) {
      throw new Error(`Failed to save config for ${appName}: ${error}`);
    }
  }
  
  /**
   * Update configuration for an app
   */
  public async updateConfig(appName: string, updates: Partial<DatabaseConfig>): Promise<DatabaseConfig> {
    const currentConfig = await this.getConfig(appName);
    const updatedConfig = this.mergeConfigs(currentConfig, updates);
    await this.saveConfig(appName, updatedConfig);
    return updatedConfig;
  }
  
  /**
   * Delete configuration for an app
   */
  public async deleteConfig(appName: string): Promise<void> {
    const configFile = path.join(this.configPath, `${appName}.json`);
    
    try {
      if (fs.existsSync(configFile)) {
        await fs.promises.unlink(configFile);
      }
      this.configs.delete(appName);
    } catch (error) {
      throw new Error(`Failed to delete config for ${appName}: ${error}`);
    }
  }
  
  /**
   * List all configured apps
   */
  public async listApps(): Promise<string[]> {
    try {
      const files = await fs.promises.readdir(this.configPath);
      return files
        .filter(file => file.endsWith('.json'))
        .map(file => file.replace('.json', ''));
    } catch (error) {
      return [];
    }
  }
  
  /**
   * Get connection config with environment variable overrides
   */
  public getConnectionConfig(appName: string, config: DatabaseConfig): ConnectionConfig {
    const envPrefix = `DB_${appName.toUpperCase().replace(/[^A-Z0-9]/g, '_')}`;
    const connection = { ...config.storage.connection };
    
    // Override with environment variables
    if (process.env[`${envPrefix}_URI`]) {
      connection.uri = process.env[`${envPrefix}_URI`];
    }
    
    if (process.env[`${envPrefix}_HOST`]) {
      connection.host = process.env[`${envPrefix}_HOST`];
    }
    
    if (process.env[`${envPrefix}_PORT`]) {
      connection.port = parseInt(process.env[`${envPrefix}_PORT`]);
    }
    
    if (process.env[`${envPrefix}_DATABASE`]) {
      connection.database = process.env[`${envPrefix}_DATABASE`];
    }
    
    if (process.env[`${envPrefix}_USERNAME`]) {
      connection.username = process.env[`${envPrefix}_USERNAME`];
    }
    
    if (process.env[`${envPrefix}_PASSWORD`]) {
      connection.password = process.env[`${envPrefix}_PASSWORD`];
    }
    
    if (process.env[`${envPrefix}_PATH`]) {
      connection.path = process.env[`${envPrefix}_PATH`];
    }
    
    return connection;
  }
  
  /**
   * Get storage type with environment variable override
   */
  public getStorageType(appName: string, config: DatabaseConfig): StorageType {
    const envPrefix = `DB_${appName.toUpperCase().replace(/[^A-Z0-9]/g, '_')}`;
    const envType = process.env[`${envPrefix}_TYPE`] as StorageType;
    
    if (envType && this.isValidStorageType(envType)) {
      return envType;
    }
    
    return config.storage.type;
  }
  
  /**
   * Validate configuration
   */
  public validateConfig(config: DatabaseConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!config.app) {
      errors.push('App name is required');
    }
    
    if (!config.storage) {
      errors.push('Storage configuration is required');
    } else {
      if (!this.isValidStorageType(config.storage.type)) {
        errors.push(`Invalid storage type: ${config.storage.type}`);
      }
      
      if (!config.storage.connection) {
        errors.push('Connection configuration is required');
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
  
  /**
   * Create a default configuration for an app
   */
  private createDefaultConfig(appName: string): DatabaseConfig {
    return {
      app: appName,
      ...this.defaultConfig,
      storage: {
        ...this.defaultConfig.storage!,
        connection: {
          ...this.defaultConfig.storage!.connection,
          path: path.join(process.cwd(), 'data/apps/backbone/dataaccess', appName)
        }
      }
    } as DatabaseConfig;
  }
  
  /**
   * Load configuration from file
   */
  private async loadConfigFromFile(appName: string): Promise<DatabaseConfig | null> {
    const configFile = path.join(this.configPath, `${appName}.json`);
    
    try {
      if (!fs.existsSync(configFile)) {
        return null;
      }
      
      const content = await fs.promises.readFile(configFile, 'utf8');
      const config = JSON.parse(content) as DatabaseConfig;
      
      // Merge with defaults to ensure all properties exist
      return this.mergeConfigs(this.createDefaultConfig(appName), config);
    } catch (error) {
      console.error(`Failed to load config for ${appName}:`, error);
      return null;
    }
  }
  
  /**
   * Deep merge two configurations
   */
  private mergeConfigs(base: DatabaseConfig, override: Partial<DatabaseConfig>): DatabaseConfig {
    const result = { ...base };
    
    for (const [key, value] of Object.entries(override)) {
      if (value !== undefined) {
        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          result[key as keyof DatabaseConfig] = {
            ...(result[key as keyof DatabaseConfig] as any),
            ...value
          };
        } else {
          result[key as keyof DatabaseConfig] = value as any;
        }
      }
    }
    
    return result;
  }
  
  /**
   * Check if storage type is valid
   */
  private isValidStorageType(type: string): type is StorageType {
    return ['json', 'mongodb', 'postgresql', 'sqlite', 'memory'].includes(type);
  }
  
  /**
   * Export configuration to JSON
   */
  public async exportConfig(appName: string): Promise<string> {
    const config = await this.getConfig(appName);
    return JSON.stringify(config, null, 2);
  }
  
  /**
   * Import configuration from JSON
   */
  public async importConfig(appName: string, configJson: string): Promise<DatabaseConfig> {
    try {
      const config = JSON.parse(configJson) as DatabaseConfig;
      config.app = appName; // Ensure app name matches
      
      const validation = this.validateConfig(config);
      if (!validation.valid) {
        throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);
      }
      
      await this.saveConfig(appName, config);
      return config;
    } catch (error) {
      throw new Error(`Failed to import config: ${error}`);
    }
  }
  
  /**
   * Get global configuration settings
   */
  public async getGlobalConfig(): Promise<any> {
    const globalConfigFile = path.join(this.configPath, 'global.json');
    
    try {
      if (fs.existsSync(globalConfigFile)) {
        const content = await fs.promises.readFile(globalConfigFile, 'utf8');
        return JSON.parse(content);
      }
    } catch (error) {
      console.error('Failed to load global config:', error);
    }
    
    return {
      monitoring: {
        enabled: true,
        retention: '30d',
        metrics: ['queries', 'errors', 'performance']
      },
      security: {
        encryption: false,
        audit: true
      },
      performance: {
        defaultTimeout: 30000,
        maxConnections: 10,
        cacheSize: 100
      }
    };
  }
  
  /**
   * Save global configuration settings
   */
  public async saveGlobalConfig(config: any): Promise<void> {
    const globalConfigFile = path.join(this.configPath, 'global.json');
    
    try {
      await fs.promises.writeFile(globalConfigFile, JSON.stringify(config, null, 2));
    } catch (error) {
      throw new Error(`Failed to save global config: ${error}`);
    }
  }
}
