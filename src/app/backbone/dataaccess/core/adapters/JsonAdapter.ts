import 'server-only';
import fs from 'fs';
import path from 'path';
import { 
  ConnectionConfig, 
  QueryFilter, 
  QueryOptions, 
  QueryResult, 
  OperationResult, 
  UpdateOptions, 
  DeleteOptions, 
  Transaction 
} from '../interfaces';
import { BaseAdapter } from './BaseAdapter';

/**
 * JSON file-based database adapter
 */
export class JsonAdapter extends BaseAdapter {
  public readonly name = 'JSON File Adapter';
  public readonly type = 'json' as const;
  
  private basePath: string = '';
  private cache: Map<string, any[]> = new Map();
  private cacheEnabled: boolean = true;
  
  constructor(config?: ConnectionConfig) {
    super(config);
    this.basePath = config?.path || path.join(process.cwd(), 'data/apps/backbone/dataaccess');
  }
  
  public async connect(config: ConnectionConfig): Promise<void> {
    this.config = config;
    this.basePath = config.path || this.basePath;
    
    // Ensure base directory exists
    await this.ensureDirectory(this.basePath);
    
    this.setConnected(true);
  }
  
  public async disconnect(): Promise<void> {
    this.cache.clear();
    this.setConnected(false);
  }
  
  private async ensureDirectory(dirPath: string): Promise<void> {
    if (!fs.existsSync(dirPath)) {
      await fs.promises.mkdir(dirPath, { recursive: true });
    }
  }
  
  private getCollectionPath(collection: string): string {
    return path.join(this.basePath, `${collection}.json`);
  }
  
  private async loadCollection(collection: string): Promise<any[]> {
    if (this.cacheEnabled && this.cache.has(collection)) {
      return this.cache.get(collection)!;
    }
    
    const filePath = this.getCollectionPath(collection);
    
    try {
      if (!fs.existsSync(filePath)) {
        const emptyData: any[] = [];
        await this.saveCollection(collection, emptyData);
        return emptyData;
      }
      
      const fileContent = await fs.promises.readFile(filePath, 'utf8');
      const data = JSON.parse(fileContent);
      
      if (this.cacheEnabled) {
        this.cache.set(collection, data);
      }
      
      return data;
    } catch (error) {
      throw new Error(`Failed to load collection ${collection}: ${error}`);
    }
  }
  
  private async saveCollection(collection: string, data: any[]): Promise<void> {
    const filePath = this.getCollectionPath(collection);
    
    try {
      await this.ensureDirectory(path.dirname(filePath));
      await fs.promises.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
      
      if (this.cacheEnabled) {
        this.cache.set(collection, [...data]);
      }
    } catch (error) {
      throw new Error(`Failed to save collection ${collection}: ${error}`);
    }
  }
  
  public async create<T = any>(collection: string, data: Partial<T>, options?: any): Promise<OperationResult<T>> {
    return this.withTiming('create', collection, async () => {
      const items = await this.loadCollection(collection);
      
      const newItem = {
        id: this.generateId(),
        ...data,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      } as T;
      
      items.push(newItem);
      await this.saveCollection(collection, items);
      
      return this.createResult(newItem, { created: true });
    });
  }
  
  public async findOne<T = any>(collection: string, filter: QueryFilter, options?: QueryOptions): Promise<T | null> {
    return this.withTiming('findOne', collection, async () => {
      const items = await this.loadCollection(collection);
      
      for (const item of items) {
        if (this.matchesFilter(item, filter)) {
          return options?.select ? this.selectFields(item, options.select) : item;
        }
      }
      
      return null;
    });
  }
  
  public async findMany<T = any>(collection: string, filter: QueryFilter, options?: QueryOptions): Promise<QueryResult<T>> {
    return this.withTiming('findMany', collection, async () => {
      const items = await this.loadCollection(collection);
      
      // Filter items
      const filteredItems = items.filter(item => this.matchesFilter(item, filter));
      
      // Apply query options (sorting, pagination, field selection)
      const processedItems = this.applyQueryOptions(filteredItems, options);
      
      return this.createQueryResult(processedItems, filteredItems.length, options);
    });
  }
  
  public async updateOne<T = any>(collection: string, filter: QueryFilter, update: Partial<T>, options?: UpdateOptions): Promise<OperationResult<T>> {
    return this.withTiming('updateOne', collection, async () => {
      const items = await this.loadCollection(collection);
      
      for (let i = 0; i < items.length; i++) {
        if (this.matchesFilter(items[i], filter)) {
          const updatedItem = {
            ...items[i],
            ...update,
            updatedAt: new Date().toISOString()
          };
          
          items[i] = updatedItem;
          await this.saveCollection(collection, items);
          
          return this.createResult(updatedItem, { updated: true, affected: 1 });
        }
      }
      
      if (options?.upsert) {
        const newItem = {
          id: this.generateId(),
          ...update,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        } as T;
        
        items.push(newItem);
        await this.saveCollection(collection, items);
        
        return this.createResult(newItem, { created: true, affected: 1 });
      }
      
      return this.createResult(undefined, { affected: 0 });
    });
  }
  
  public async updateMany<T = any>(collection: string, filter: QueryFilter, update: Partial<T>, options?: UpdateOptions): Promise<OperationResult<T[]>> {
    return this.withTiming('updateMany', collection, async () => {
      const items = await this.loadCollection(collection);
      const updatedItems: T[] = [];
      let affected = 0;
      
      for (let i = 0; i < items.length; i++) {
        if (this.matchesFilter(items[i], filter)) {
          const updatedItem = {
            ...items[i],
            ...update,
            updatedAt: new Date().toISOString()
          };
          
          items[i] = updatedItem;
          updatedItems.push(updatedItem);
          affected++;
        }
      }
      
      if (affected > 0) {
        await this.saveCollection(collection, items);
      }
      
      return this.createResult(updatedItems, { updated: true, affected });
    });
  }
  
  public async deleteOne(collection: string, filter: QueryFilter, options?: DeleteOptions): Promise<OperationResult> {
    return this.withTiming('deleteOne', collection, async () => {
      const items = await this.loadCollection(collection);
      
      for (let i = 0; i < items.length; i++) {
        if (this.matchesFilter(items[i], filter)) {
          if (options?.soft) {
            items[i].deletedAt = new Date().toISOString();
            items[i].deleted = true;
          } else {
            items.splice(i, 1);
          }
          
          await this.saveCollection(collection, items);
          return this.createResult(undefined, { deleted: true, affected: 1 });
        }
      }
      
      return this.createResult(undefined, { affected: 0 });
    });
  }
  
  public async deleteMany(collection: string, filter: QueryFilter, options?: DeleteOptions): Promise<OperationResult> {
    return this.withTiming('deleteMany', collection, async () => {
      const items = await this.loadCollection(collection);
      let affected = 0;
      
      for (let i = items.length - 1; i >= 0; i--) {
        if (this.matchesFilter(items[i], filter)) {
          if (options?.soft) {
            items[i].deletedAt = new Date().toISOString();
            items[i].deleted = true;
          } else {
            items.splice(i, 1);
          }
          affected++;
        }
      }
      
      if (affected > 0) {
        await this.saveCollection(collection, items);
      }
      
      return this.createResult(undefined, { deleted: true, affected });
    });
  }
  
  public async count(collection: string, filter: QueryFilter): Promise<number> {
    return this.withTiming('count', collection, async () => {
      const items = await this.loadCollection(collection);
      return items.filter(item => this.matchesFilter(item, filter)).length;
    });
  }
  
  public async exists(collection: string, filter: QueryFilter): Promise<boolean> {
    return this.withTiming('exists', collection, async () => {
      const items = await this.loadCollection(collection);
      return items.some(item => this.matchesFilter(item, filter));
    });
  }
  
  public async aggregate<T = any>(collection: string, pipeline: any[]): Promise<T[]> {
    return this.withTiming('aggregate', collection, async () => {
      // Basic aggregation support for JSON adapter
      const items = await this.loadCollection(collection);
      let result = [...items];
      
      for (const stage of pipeline) {
        if (stage.$match) {
          result = result.filter(item => this.matchesFilter(item, stage.$match));
        } else if (stage.$sort) {
          result.sort((a: any, b: any) => {
            for (const [field, direction] of Object.entries(stage.$sort)) {
              const aVal = this.getNestedValue(a, field);
              const bVal = this.getNestedValue(b, field);
              
              if (aVal < bVal) return direction === 1 ? -1 : 1;
              if (aVal > bVal) return direction === 1 ? 1 : -1;
            }
            return 0;
          });
        } else if (stage.$limit) {
          result = result.slice(0, stage.$limit);
        } else if (stage.$skip) {
          result = result.slice(stage.$skip);
        }
        // Add more aggregation stages as needed
      }
      
      return result;
    });
  }
  
  public async startTransaction(): Promise<Transaction> {
    // JSON adapter doesn't support real transactions, return a mock
    return {
      id: this.generateId(),
      commit: async () => {},
      rollback: async () => {},
      isActive: () => false
    };
  }
  
  public async createCollection(name: string, schema?: any): Promise<void> {
    const filePath = this.getCollectionPath(name);
    if (!fs.existsSync(filePath)) {
      await this.saveCollection(name, []);
    }
  }
  
  public async dropCollection(name: string): Promise<void> {
    const filePath = this.getCollectionPath(name);
    if (fs.existsSync(filePath)) {
      await fs.promises.unlink(filePath);
      this.cache.delete(name);
    }
  }
  
  public async listCollections(): Promise<string[]> {
    const files = await fs.promises.readdir(this.basePath);
    return files
      .filter(file => file.endsWith('.json'))
      .map(file => file.replace('.json', ''));
  }
  
  public async backup(backupPath?: string): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const defaultPath = path.join(this.basePath, '..', 'backups', `backup-${timestamp}`);
    const targetPath = backupPath || defaultPath;
    
    await this.ensureDirectory(targetPath);
    
    const collections = await this.listCollections();
    for (const collection of collections) {
      const sourcePath = this.getCollectionPath(collection);
      const targetFilePath = path.join(targetPath, `${collection}.json`);
      await fs.promises.copyFile(sourcePath, targetFilePath);
    }
    
    return targetPath;
  }
  
  public async restore(backupPath: string): Promise<void> {
    if (!fs.existsSync(backupPath)) {
      throw new Error(`Backup path does not exist: ${backupPath}`);
    }
    
    const files = await fs.promises.readdir(backupPath);
    const jsonFiles = files.filter(file => file.endsWith('.json'));
    
    for (const file of jsonFiles) {
      const sourcePath = path.join(backupPath, file);
      const targetPath = path.join(this.basePath, file);
      await fs.promises.copyFile(sourcePath, targetPath);
    }
    
    // Clear cache to force reload
    this.cache.clear();
  }
  
  public async migrate(version: string): Promise<void> {
    // JSON adapter migration is handled by file structure changes
    console.log(`Migration to version ${version} completed for JSON adapter`);
  }
  
  public async getStats(): Promise<any> {
    const collections = await this.listCollections();
    const stats: any = {
      adapter: this.name,
      type: this.type,
      connected: this.isConnected(),
      basePath: this.basePath,
      collections: {},
      totalCollections: collections.length,
      cacheEnabled: this.cacheEnabled,
      cachedCollections: this.cache.size
    };
    
    for (const collection of collections) {
      const items = await this.loadCollection(collection);
      const filePath = this.getCollectionPath(collection);
      const fileStats = await fs.promises.stat(filePath);
      
      stats.collections[collection] = {
        count: items.length,
        size: fileStats.size,
        lastModified: fileStats.mtime
      };
    }
    
    return stats;
  }
  
  public clearCache(): Promise<void> {
    this.cache.clear();
    return Promise.resolve();
  }
  
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }
  
  private selectFields(obj: any, fields: string[]): any {
    const result: any = {};
    
    for (const field of fields) {
      if (field.includes('.')) {
        const value = this.getNestedValue(obj, field);
        this.setNestedValue(result, field, value);
      } else {
        result[field] = obj[field];
      }
    }
    
    return result;
  }
  
  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    
    let current = obj;
    for (const key of keys) {
      if (!(key in current)) {
        current[key] = {};
      }
      current = current[key];
    }
    
    current[lastKey] = value;
  }
}
