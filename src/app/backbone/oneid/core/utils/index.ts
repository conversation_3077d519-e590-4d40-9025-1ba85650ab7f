// Utility functions for ABN ONEID Authentication System

import 'server-only';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import { z } from 'zod';

// ID Generation
export const generateId = (): string => {
  return uuidv4();
};

export const generateShortId = (length: number = 8): string => {
  return crypto.randomBytes(length).toString('hex').substring(0, length);
};

export const generateSecureToken = (length: number = 32): string => {
  return crypto.randomBytes(length).toString('hex');
};

// Date Utilities
export const getCurrentTimestamp = (): string => {
  return new Date().toISOString();
};

export const addMinutes = (date: Date, minutes: number): Date => {
  return new Date(date.getTime() + minutes * 60000);
};

export const addHours = (date: Date, hours: number): Date => {
  return new Date(date.getTime() + hours * 3600000);
};

export const addDays = (date: Date, days: number): Date => {
  return new Date(date.getTime() + days * 86400000);
};

export const isExpired = (expiresAt: string): boolean => {
  return new Date(expiresAt) < new Date();
};

// String Utilities
export const normalizeUsername = (username: string): string => {
  return username.toLowerCase().trim();
};

export const normalizeEmail = (email: string): string => {
  return email.toLowerCase().trim();
};

export const sanitizeString = (input: string): string => {
  return input.replace(/[<>\"'&]/g, '');
};

export const generateUsername = (firstName: string, lastName: string): string => {
  const base = `${firstName.toLowerCase()}_${lastName.toLowerCase()}`;
  const sanitized = base.replace(/[^a-z0-9_]/g, '');
  return sanitized;
};

// Validation Schemas
export const usernameSchema = z.string()
  .min(3, 'Username must be at least 3 characters')
  .max(30, 'Username must be less than 30 characters')
  .regex(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores')
  .transform(normalizeUsername);

export const emailSchema = z.string()
  .email('Invalid email format')
  .transform(normalizeEmail)
  .optional();

export const passwordSchema = z.string()
  .min(8, 'Password must be at least 8 characters')
  .max(128, 'Password must be less than 128 characters');

export const nameSchema = z.string()
  .min(1, 'Name is required')
  .max(50, 'Name must be less than 50 characters')
  .regex(/^[a-zA-Z\s'-]+$/, 'Name can only contain letters, spaces, hyphens, and apostrophes');

export const companyNameSchema = z.string()
  .min(2, 'Company name must be at least 2 characters')
  .max(100, 'Company name must be less than 100 characters');

// Validation Functions
export const validatePassword = (password: string): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (password.length > 128) {
    errors.push('Password must be less than 128 characters');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/[0-9]/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validateUsername = (username: string): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (username.length < 3) {
    errors.push('Username must be at least 3 characters long');
  }
  
  if (username.length > 30) {
    errors.push('Username must be less than 30 characters');
  }
  
  if (!/^[a-zA-Z0-9_]+$/.test(username)) {
    errors.push('Username can only contain letters, numbers, and underscores');
  }
  
  if (/^[0-9]/.test(username)) {
    errors.push('Username cannot start with a number');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};

// Data Transformation Utilities
export const maskEmail = (email: string): string => {
  if (!email) return '';
  
  const [localPart, domain] = email.split('@');
  if (!domain) return email;
  
  const maskedLocal = localPart.length > 2 
    ? localPart[0] + '*'.repeat(localPart.length - 2) + localPart[localPart.length - 1]
    : localPart;
  
  return `${maskedLocal}@${domain}`;
};

export const formatUserDisplayName = (firstName: string, lastName: string): string => {
  return `${firstName} ${lastName}`.trim();
};

export const extractInitials = (firstName: string, lastName: string): string => {
  const firstInitial = firstName.charAt(0).toUpperCase();
  const lastInitial = lastName.charAt(0).toUpperCase();
  return `${firstInitial}${lastInitial}`;
};

// Error Handling Utilities
export class OneIDError extends Error {
  public code: string;
  public statusCode: number;
  public details?: any;

  constructor(message: string, code: string, statusCode: number = 400, details?: any) {
    super(message);
    this.name = 'OneIDError';
    this.code = code;
    this.statusCode = statusCode;
    this.details = details;
  }
}

export const createError = (message: string, code: string, statusCode: number = 400, details?: any): OneIDError => {
  return new OneIDError(message, code, statusCode, details);
};

// Common error codes
export const ErrorCodes = {
  // Authentication errors
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  INVALID_TOKEN: 'INVALID_TOKEN',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  SESSION_EXPIRED: 'SESSION_EXPIRED',
  
  // Registration errors
  USERNAME_TAKEN: 'USERNAME_TAKEN',
  EMAIL_TAKEN: 'EMAIL_TAKEN',
  COMPANY_NAME_TAKEN: 'COMPANY_NAME_TAKEN',
  INVALID_INVITATION: 'INVALID_INVITATION',
  
  // Validation errors
  INVALID_INPUT: 'INVALID_INPUT',
  WEAK_PASSWORD: 'WEAK_PASSWORD',
  INVALID_EMAIL: 'INVALID_EMAIL',
  INVALID_USERNAME: 'INVALID_USERNAME',
  
  // Permission errors
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  ACCOUNT_SUSPENDED: 'ACCOUNT_SUSPENDED',
  ACCOUNT_INACTIVE: 'ACCOUNT_INACTIVE',
  EMAIL_NOT_VERIFIED: 'EMAIL_NOT_VERIFIED',
  
  // Rate limiting
  TOO_MANY_ATTEMPTS: 'TOO_MANY_ATTEMPTS',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  
  // System errors
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  DATA_CORRUPTION: 'DATA_CORRUPTION'
} as const;

// Response Utilities
export const createSuccessResponse = <T>(data: T, message?: string) => {
  return {
    success: true,
    data,
    message,
    timestamp: getCurrentTimestamp()
  };
};

export const createErrorResponse = (error: string, code?: string, details?: any) => {
  return {
    success: false,
    error,
    code,
    details,
    timestamp: getCurrentTimestamp()
  };
};

export const createPaginatedResponse = <T>(
  data: T[],
  page: number,
  limit: number,
  total: number
) => {
  return {
    success: true,
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    },
    timestamp: getCurrentTimestamp()
  };
};

// File Path Utilities
export const getDataPath = (basePath: string, ...segments: string[]): string => {
  return [basePath, ...segments].join('/');
};

export const ensureDirectoryExists = async (dirPath: string): Promise<void> => {
  const fs = await import('fs/promises');
  const path = await import('path');

  // Convert relative path to absolute path
  const absolutePath = path.isAbsolute(dirPath) ? dirPath : path.join(process.cwd(), dirPath);

  try {
    await fs.access(absolutePath);
  } catch {
    await fs.mkdir(absolutePath, { recursive: true });
  }
};

// Configuration Utilities
export const loadConfig = (): any => {
  return {
    jwt: {
      secret: process.env.ONEID_JWT_SECRET || 'default-secret-change-in-production',
      expiresIn: process.env.ONEID_JWT_EXPIRES_IN || '24h',
      refreshExpiresIn: process.env.ONEID_JWT_REFRESH_EXPIRES_IN || '7d'
    },
    magicLink: {
      expiresIn: process.env.ONEID_MAGIC_LINK_EXPIRES_IN || '15m',
      baseUrl: process.env.ONEID_BASE_URL || 'http://localhost:3000'
    },
    email: {
      from: process.env.ONEID_EMAIL_FROM || '<EMAIL>',
      smtp: {
        host: process.env.ONEID_EMAIL_SMTP_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.ONEID_EMAIL_SMTP_PORT || '587'),
        secure: process.env.ONEID_EMAIL_SMTP_SECURE === 'true',
        auth: {
          user: process.env.ONEID_EMAIL_SMTP_USER || '',
          pass: process.env.ONEID_EMAIL_SMTP_PASS || ''
        }
      }
    },
    security: {
      maxLoginAttempts: parseInt(process.env.ONEID_MAX_LOGIN_ATTEMPTS || '5'),
      lockoutDuration: parseInt(process.env.ONEID_LOCKOUT_DURATION || '15'),
      passwordMinLength: parseInt(process.env.ONEID_PASSWORD_MIN_LENGTH || '8'),
      sessionTimeout: parseInt(process.env.ONEID_SESSION_TIMEOUT || '1440')
    },
    dataPath: process.env.ONEID_DATA_PATH || './data/apps/backbone/oneid'
  };
};
