import 'server-only';
// Magic Link Repository Implementation using JSON Files

import { IMagicLinkRepository } from '../../core/interfaces';
import { MagicLinkToken } from '../../core/types';
import { generateId, getCurrentTimestamp, isExpired, getDataPath, ensureDirectoryExists } from '../../core/utils';
import fs from 'fs/promises';
import path from 'path';

export class MagicLinkRepository implements IMagicLinkRepository {
  private readonly dataPath: string;
  private readonly fileName = 'magic_links.json';

  constructor() {
    this.dataPath = getDataPath('data/apps/backbone/oneid', 'auth');
    this.ensureDataDirectory();
  }

  private async ensureDataDirectory() {
    await ensureDirectoryExists(this.dataPath);
  }

  private get filePath(): string {
    return path.join(this.dataPath, this.fileName);
  }

  private async readData(): Promise<MagicLinkToken[]> {
    try {
      const data = await fs.readFile(this.filePath, 'utf-8');
      return JSON.parse(data);
    } catch (error) {
      // File doesn't exist or is empty, return empty array
      return [];
    }
  }

  private async writeData(data: MagicLinkToken[]): Promise<void> {
    await this.ensureDataDirectory();
    await fs.writeFile(this.filePath, JSON.stringify(data, null, 2), 'utf-8');
  }

  async create(magicLinkData: Omit<MagicLinkToken, 'id' | 'createdAt'>): Promise<MagicLinkToken> {
    const magicLink: MagicLinkToken = {
      ...magicLinkData,
      id: generateId(),
      createdAt: getCurrentTimestamp()
    };

    try {
      const data = await this.readData();
      data.push(magicLink);
      await this.writeData(data);
      return magicLink;
    } catch (error) {
      console.error('Error creating magic link:', error);
      throw new Error('Failed to create magic link');
    }
  }

  async findByToken(token: string): Promise<MagicLinkToken | null> {
    try {
      const data = await this.readData();
      return data.find(link => link.token === token) || null;
    } catch (error) {
      console.error('Error finding magic link by token:', error);
      return null;
    }
  }

  async findByEmail(email: string): Promise<MagicLinkToken[]> {
    try {
      const data = await this.readData();
      return data.filter(link => link.email?.toLowerCase() === email.toLowerCase());
    } catch (error) {
      console.error('Error finding magic links by email:', error);
      return [];
    }
  }

  async markAsUsed(id: string): Promise<boolean> {
    try {
      const data = await this.readData();
      const index = data.findIndex(link => link.id === id);

      if (index === -1) {
        return false;
      }

      data[index] = {
        ...data[index],
        usedAt: getCurrentTimestamp()
      };

      await this.writeData(data);
      return true;
    } catch (error) {
      console.error('Error marking magic link as used:', error);
      return false;
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      const data = await this.readData();
      const initialLength = data.length;
      const filteredData = data.filter(link => link.id !== id);

      if (filteredData.length === initialLength) {
        return false; // No item was found to delete
      }

      await this.writeData(filteredData);
      return true;
    } catch (error) {
      console.error('Error deleting magic link:', error);
      return false;
    }
  }

  async deleteExpired(): Promise<number> {
    try {
      const data = await this.readData();
      const initialCount = data.length;

      const validLinks = data.filter(link => !isExpired(link.expiresAt));

      await this.writeData(validLinks);

      return initialCount - validLinks.length;
    } catch (error) {
      console.error('Error deleting expired magic links:', error);
      return 0;
    }
  }

  async deleteByEmail(email: string): Promise<number> {
    try {
      const data = await this.readData();
      const initialCount = data.length;

      const filteredData = data.filter(link =>
        link.email?.toLowerCase() !== email.toLowerCase()
      );

      await this.writeData(filteredData);

      return initialCount - filteredData.length;
    } catch (error) {
      console.error('Error deleting magic links by email:', error);
      return 0;
    }
  }

  // Additional utility methods
  async isValidToken(token: string): Promise<boolean> {
    const magicLink = await this.findByToken(token);
    if (!magicLink) return false;
    
    return !magicLink.usedAt && !isExpired(magicLink.expiresAt);
  }

  async getActiveTokensForEmail(email: string): Promise<MagicLinkToken[]> {
    const magicLinks = await this.findByEmail(email);
    return magicLinks.filter(link => 
      !link.usedAt && !isExpired(link.expiresAt)
    );
  }

  async cleanupOldTokens(olderThanDays: number = 7): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      const data = await this.readData();
      const initialCount = data.length;

      const recentLinks = data.filter(link =>
        new Date(link.createdAt) >= cutoffDate
      );

      await this.writeData(recentLinks);

      return initialCount - recentLinks.length;
    } catch (error) {
      console.error('Error cleaning up old magic links:', error);
      return 0;
    }
  }
}
