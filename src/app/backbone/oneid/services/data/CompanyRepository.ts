import 'server-only';
// Company Repository Implementation using Direct File Access

import 'server-only';
import fs from 'fs/promises';
import path from 'path';
import { ICompanyRepository } from '../../core/interfaces';
import { Company, PaginatedResponse } from '../../core/types';
import { generateId, getCurrentTimestamp, createPaginatedResponse } from '../../core/utils';

export class CompanyRepository implements ICompanyRepository {
  private readonly dataPath: string;
  private readonly filePath: string;

  constructor() {
    this.dataPath = path.join(process.cwd(), 'data/apps/backbone/oneid/companies');
    this.filePath = path.join(this.dataPath, 'companies.json');
  }

  private async ensureDataDirectory() {
    try {
      await fs.mkdir(this.dataPath, { recursive: true });
    } catch (error) {
      // Directory already exists
    }
  }

  private async readCompanies(): Promise<Company[]> {
    try {
      await this.ensureDataDirectory();
      const data = await fs.readFile(this.filePath, 'utf-8');
      return JSON.parse(data);
    } catch (error) {
      // File doesn't exist, return empty array
      return [];
    }
  }

  private async writeCompanies(companies: Company[]): Promise<void> {
    await this.ensureDataDirectory();
    await fs.writeFile(this.filePath, JSON.stringify(companies, null, 2));
  }

  async create(companyData: Omit<Company, 'id' | 'createdAt' | 'updatedAt'>): Promise<Company> {
    const companies = await this.readCompanies();

    const company: Company = {
      ...companyData,
      id: generateId(),
      createdAt: getCurrentTimestamp(),
      updatedAt: getCurrentTimestamp()
    };

    companies.push(company);
    await this.writeCompanies(companies);

    return company;
  }

  async findById(id: string): Promise<Company | null> {
    const companies = await this.readCompanies();
    return companies.find(company => company.id === id) || null;
  }

  async findAll(): Promise<Company[]> {
    return await this.readCompanies();
  }

  async findByName(name: string): Promise<Company | null> {
    await this.ensureInitialized();
    
    try {
      const companies = await this.dataService.findMany(this.collectionName, { 
        name: name.toLowerCase() 
      });
      return companies.length > 0 ? companies[0] : null;
    } catch (error) {
      return null;
    }
  }

  async update(id: string, updates: Partial<Company>): Promise<Company | null> {
    await this.ensureInitialized();
    
    try {
      const updateData = {
        ...updates,
        updatedAt: getCurrentTimestamp()
      };
      
      const updated = await this.dataService.update(this.collectionName, id, updateData);
      return updated || null;
    } catch (error) {
      return null;
    }
  }

  async delete(id: string): Promise<boolean> {
    await this.ensureInitialized();
    
    try {
      await this.dataService.delete(this.collectionName, id);
      return true;
    } catch (error) {
      return false;
    }
  }

  async findMany(
    filters: Partial<Company>, 
    options: {
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<PaginatedResponse<Company>> {
    await this.ensureInitialized();
    
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = options;
    
    try {
      // Get all matching companies
      const allCompanies = await this.dataService.findMany(this.collectionName, filters);
      
      // Sort companies
      allCompanies.sort((a: Company, b: Company) => {
        const aValue = a[sortBy as keyof Company];
        const bValue = b[sortBy as keyof Company];
        
        if (sortOrder === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });
      
      // Paginate
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedCompanies = allCompanies.slice(startIndex, endIndex);
      
      return createPaginatedResponse(paginatedCompanies, page, limit, allCompanies.length);
    } catch (error) {
      return createPaginatedResponse([], page, limit, 0);
    }
  }

  async updateStatus(id: string, status: Company['status']): Promise<boolean> {
    await this.ensureInitialized();
    
    try {
      const updated = await this.update(id, { status });
      return updated !== null;
    } catch (error) {
      return false;
    }
  }

  // Additional utility methods
  async isNameAvailable(name: string): Promise<boolean> {
    const company = await this.findByName(name);
    return company === null;
  }

  async getCompanyStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    suspended: number;
    byIndustry: Record<string, number>;
  }> {
    await this.ensureInitialized();
    
    try {
      const allCompanies = await this.dataService.findMany(this.collectionName, {});
      
      const stats = {
        total: allCompanies.length,
        active: 0,
        inactive: 0,
        suspended: 0,
        byIndustry: {} as Record<string, number>
      };

      allCompanies.forEach((company: Company) => {
        // Count by status
        if (company.status === 'active') stats.active++;
        else if (company.status === 'inactive') stats.inactive++;
        else if (company.status === 'suspended') stats.suspended++;
        
        // Count by industry
        if (company.industry) {
          stats.byIndustry[company.industry] = (stats.byIndustry[company.industry] || 0) + 1;
        }
      });

      return stats;
    } catch (error) {
      return {
        total: 0,
        active: 0,
        inactive: 0,
        suspended: 0,
        byIndustry: {}
      };
    }
  }

  async searchCompanies(query: string, filters: {
    status?: Company['status'];
    industry?: string;
  } = {}): Promise<Company[]> {
    await this.ensureInitialized();
    
    try {
      const allCompanies = await this.dataService.findMany(this.collectionName, filters);
      
      if (!query) return allCompanies;
      
      const searchQuery = query.toLowerCase();
      return allCompanies.filter((company: Company) => 
        company.name.toLowerCase().includes(searchQuery) ||
        (company.displayName && company.displayName.toLowerCase().includes(searchQuery)) ||
        (company.description && company.description.toLowerCase().includes(searchQuery)) ||
        (company.industry && company.industry.toLowerCase().includes(searchQuery))
      );
    } catch (error) {
      return [];
    }
  }

  async getCompaniesByCreator(createdBy: string): Promise<Company[]> {
    await this.ensureInitialized();
    
    try {
      const companies = await this.dataService.findMany(this.collectionName, { 
        createdBy 
      });
      return companies || [];
    } catch (error) {
      return [];
    }
  }

  async getActiveCompanies(): Promise<Company[]> {
    await this.ensureInitialized();
    
    try {
      const companies = await this.dataService.findMany(this.collectionName, { 
        status: 'active' 
      });
      return companies || [];
    } catch (error) {
      return [];
    }
  }

  async updateSettings(id: string, settings: Partial<Company['settings']>): Promise<boolean> {
    await this.ensureInitialized();
    
    try {
      const company = await this.findById(id);
      if (!company) return false;

      const updatedSettings = {
        ...company.settings,
        ...settings
      };

      const updated = await this.update(id, { settings: updatedSettings });
      return updated !== null;
    } catch (error) {
      return false;
    }
  }

  async getCompaniesWithEmployeeCount(): Promise<Array<Company & { employeeCount: number }>> {
    await this.ensureInitialized();
    
    try {
      const companies = await this.dataService.findMany(this.collectionName, {});
      const userService = await initializeDataAccess('backbone-oneid');
      
      const companiesWithCount = await Promise.all(
        companies.map(async (company: Company) => {
          try {
            const employees = await userService.findMany('users', { companyId: company.id });
            return {
              ...company,
              employeeCount: employees.length
            };
          } catch {
            return {
              ...company,
              employeeCount: 0
            };
          }
        })
      );

      return companiesWithCount;
    } catch (error) {
      return [];
    }
  }
}
