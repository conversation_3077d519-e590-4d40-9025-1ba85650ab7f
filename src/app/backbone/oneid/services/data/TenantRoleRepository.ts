import 'server-only';
// Tenant Role Repository - Data access layer for tenant role management

import { ITenantRoleRepository } from '../../core/interfaces';
import { TenantRole } from '../../core/types';
import { getDataPath } from '../../core/utils';
import * as fs from 'fs/promises';
import * as path from 'path';

export class TenantRoleRepository implements ITenantRoleRepository {
  private dataPath: string;

  constructor() {
    this.dataPath = getDataPath('tenant-roles');
  }

  async create(role: TenantRole): Promise<TenantRole | null> {
    try {
      await this.ensureDataDirectory();
      await this.ensureTenantDirectory(role.tenantId);
      
      const filePath = path.join(this.dataPath, role.tenantId, `${role.id}.json`);
      await fs.writeFile(filePath, JSON.stringify(role, null, 2));
      
      return role;
    } catch (error) {
      console.error('Error creating tenant role:', error);
      return null;
    }
  }

  async findById(id: string): Promise<TenantRole | null> {
    try {
      // Search across all tenants for the role
      const tenantDirs = await this.getTenantDirectories();
      
      for (const tenantId of tenantDirs) {
        const filePath = path.join(this.dataPath, tenantId, `${id}.json`);
        try {
          const data = await fs.readFile(filePath, 'utf-8');
          return JSON.parse(data);
        } catch {
          // Continue searching in other tenants
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error finding tenant role by ID:', error);
      return null;
    }
  }

  async findByTenant(tenantId: string): Promise<TenantRole[]> {
    try {
      await this.ensureTenantDirectory(tenantId);
      
      const tenantPath = path.join(this.dataPath, tenantId);
      const files = await fs.readdir(tenantPath);
      const roleFiles = files.filter(file => file.endsWith('.json'));
      
      const roles: TenantRole[] = [];
      
      for (const file of roleFiles) {
        try {
          const filePath = path.join(tenantPath, file);
          const data = await fs.readFile(filePath, 'utf-8');
          const role = JSON.parse(data);
          roles.push(role);
        } catch (error) {
          console.error(`Error reading role file ${file}:`, error);
        }
      }

      // Sort by creation date (newest first)
      roles.sort((a, b) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

      return roles;
    } catch (error) {
      console.error('Error finding roles by tenant:', error);
      return [];
    }
  }

  async findByName(tenantId: string, name: string): Promise<TenantRole | null> {
    try {
      const roles = await this.findByTenant(tenantId);
      return roles.find(role => 
        role.name.toLowerCase() === name.toLowerCase()
      ) || null;
    } catch (error) {
      console.error('Error finding role by name:', error);
      return null;
    }
  }

  async findSystemRoles(tenantId: string): Promise<TenantRole[]> {
    try {
      const roles = await this.findByTenant(tenantId);
      return roles.filter(role => role.isSystemRole);
    } catch (error) {
      console.error('Error finding system roles:', error);
      return [];
    }
  }

  async findDefaultRole(tenantId: string): Promise<TenantRole | null> {
    try {
      const roles = await this.findByTenant(tenantId);
      return roles.find(role => role.isDefault) || null;
    } catch (error) {
      console.error('Error finding default role:', error);
      return null;
    }
  }

  async update(id: string, updates: Partial<TenantRole>): Promise<TenantRole | null> {
    try {
      const existing = await this.findById(id);
      if (!existing) {
        return null;
      }

      const updated: TenantRole = {
        ...existing,
        ...updates,
        id: existing.id, // Ensure ID cannot be changed
        tenantId: existing.tenantId, // Ensure tenantId cannot be changed
        createdAt: existing.createdAt, // Ensure createdAt cannot be changed
        updatedAt: new Date().toISOString()
      };

      const filePath = path.join(this.dataPath, existing.tenantId, `${id}.json`);
      await fs.writeFile(filePath, JSON.stringify(updated, null, 2));
      
      return updated;
    } catch (error) {
      console.error('Error updating tenant role:', error);
      return null;
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      const existing = await this.findById(id);
      if (!existing) {
        return false;
      }

      // Prevent deletion of system roles
      if (existing.isSystemRole) {
        console.warn(`Cannot delete system role: ${existing.name}`);
        return false;
      }

      const filePath = path.join(this.dataPath, existing.tenantId, `${id}.json`);
      await fs.unlink(filePath);
      
      return true;
    } catch (error) {
      console.error('Error deleting tenant role:', error);
      return false;
    }
  }

  // Private helper methods

  private async ensureDataDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.dataPath, { recursive: true });
    } catch (error) {
      console.error('Error creating tenant roles data directory:', error);
    }
  }

  private async ensureTenantDirectory(tenantId: string): Promise<void> {
    try {
      const tenantPath = path.join(this.dataPath, tenantId);
      await fs.mkdir(tenantPath, { recursive: true });
    } catch (error) {
      console.error('Error creating tenant directory:', error);
    }
  }

  private async getTenantDirectories(): Promise<string[]> {
    try {
      await this.ensureDataDirectory();
      const items = await fs.readdir(this.dataPath, { withFileTypes: true });
      return items
        .filter(item => item.isDirectory())
        .map(item => item.name);
    } catch (error) {
      console.error('Error getting tenant directories:', error);
      return [];
    }
  }
}
