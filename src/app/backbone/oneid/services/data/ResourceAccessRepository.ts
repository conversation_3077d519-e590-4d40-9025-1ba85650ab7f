import 'server-only';
// ResourceAccess Repository Implementation using Unified Data Access Layer

import { initializeDataAccess } from '@/app/backbone/dataaccess';
import { IResourceAccessRepository } from '../../core/interfaces';
import { ResourceAccess, ResourceType } from '../../core/types';
import { generateId, getCurrentTimestamp } from '../../core/utils';

export class ResourceAccessRepository implements IResourceAccessRepository {
  private dataService: any;
  private readonly collectionName = 'resource_access';

  constructor() {
    this.initialize();
  }

  private async initialize() {
    this.dataService = await initializeDataAccess('backbone-oneid');
  }

  private async ensureInitialized() {
    if (!this.dataService) {
      await this.initialize();
    }
  }

  async create(resourceData: Omit<ResourceAccess, 'id' | 'createdAt' | 'updatedAt'>): Promise<ResourceAccess> {
    await this.ensureInitialized();
    
    const resource: ResourceAccess = {
      ...resourceData,
      id: generateId(),
      createdAt: getCurrentTimestamp(),
      updatedAt: getCurrentTimestamp()
    };

    await this.dataService.create(this.collectionName, resource);
    return resource;
  }

  async findById(id: string): Promise<ResourceAccess | null> {
    await this.ensureInitialized();
    
    try {
      return await this.dataService.findById(this.collectionName, id);
    } catch (error) {
      return null;
    }
  }

  async findByPath(resourcePath: string): Promise<ResourceAccess | null> {
    await this.ensureInitialized();
    
    try {
      const resources = await this.dataService.findMany(this.collectionName, { resourcePath });
      return resources && resources.length > 0 ? resources[0] : null;
    } catch (error) {
      return null;
    }
  }

  async findByType(resourceType: ResourceType): Promise<ResourceAccess[]> {
    await this.ensureInitialized();
    
    try {
      const resources = await this.dataService.findMany(this.collectionName, { resourceType });
      return resources || [];
    } catch (error) {
      return [];
    }
  }

  async findActive(): Promise<ResourceAccess[]> {
    await this.ensureInitialized();
    
    try {
      const resources = await this.dataService.findMany(this.collectionName, { isActive: true });
      return resources || [];
    } catch (error) {
      return [];
    }
  }

  async findMany(filters: Partial<ResourceAccess> = {}): Promise<ResourceAccess[]> {
    await this.ensureInitialized();
    
    try {
      const resources = await this.dataService.findMany(this.collectionName, filters);
      return resources || [];
    } catch (error) {
      return [];
    }
  }

  async update(id: string, updates: Partial<ResourceAccess>): Promise<ResourceAccess | null> {
    await this.ensureInitialized();
    
    try {
      const updatedData = {
        ...updates,
        updatedAt: getCurrentTimestamp()
      };
      
      return await this.dataService.update(this.collectionName, id, updatedData);
    } catch (error) {
      return null;
    }
  }

  async delete(id: string): Promise<boolean> {
    await this.ensureInitialized();
    
    try {
      return await this.dataService.delete(this.collectionName, id);
    } catch (error) {
      return false;
    }
  }

  async findAll(): Promise<ResourceAccess[]> {
    await this.ensureInitialized();
    
    try {
      const resources = await this.dataService.findAll(this.collectionName);
      return resources || [];
    } catch (error) {
      return [];
    }
  }

  // Helper methods for common resource access queries
  async findByPathPattern(pathPattern: string): Promise<ResourceAccess[]> {
    await this.ensureInitialized();
    
    try {
      const allResources = await this.findActive();
      
      // Simple pattern matching - could be enhanced with regex
      return allResources.filter(resource => 
        resource.resourcePath.includes(pathPattern) ||
        pathPattern.includes(resource.resourcePath)
      );
    } catch (error) {
      return [];
    }
  }

  async findByPermission(permissionId: string): Promise<ResourceAccess[]> {
    await this.ensureInitialized();
    
    try {
      const allResources = await this.findActive();
      
      return allResources.filter(resource => 
        resource.requiredPermissions.includes(permissionId)
      );
    } catch (error) {
      return [];
    }
  }

  async findByRole(roleId: string): Promise<ResourceAccess[]> {
    await this.ensureInitialized();
    
    try {
      const allResources = await this.findActive();
      
      return allResources.filter(resource => 
        resource.allowedRoles && resource.allowedRoles.includes(roleId)
      );
    } catch (error) {
      return [];
    }
  }

  async findCompanyRestrictedResources(): Promise<ResourceAccess[]> {
    await this.ensureInitialized();
    
    try {
      const resources = await this.dataService.findMany(this.collectionName, { 
        companyRestricted: true,
        isActive: true 
      });
      return resources || [];
    } catch (error) {
      return [];
    }
  }

  async searchResources(query: string): Promise<ResourceAccess[]> {
    await this.ensureInitialized();
    
    try {
      const allResources = await this.findActive();
      
      if (!query) return allResources;
      
      const searchQuery = query.toLowerCase();
      return allResources.filter(resource => 
        resource.name.toLowerCase().includes(searchQuery) ||
        resource.description.toLowerCase().includes(searchQuery) ||
        resource.resourcePath.toLowerCase().includes(searchQuery) ||
        resource.resourceType.toLowerCase().includes(searchQuery)
      );
    } catch (error) {
      return [];
    }
  }

  async getResourcesByType(): Promise<Record<ResourceType, ResourceAccess[]>> {
    await this.ensureInitialized();
    
    try {
      const allResources = await this.findActive();
      const resourcesByType: Record<string, ResourceAccess[]> = {};
      
      for (const resource of allResources) {
        if (!resourcesByType[resource.resourceType]) {
          resourcesByType[resource.resourceType] = [];
        }
        resourcesByType[resource.resourceType].push(resource);
      }
      
      return resourcesByType as Record<ResourceType, ResourceAccess[]>;
    } catch (error) {
      return {} as Record<ResourceType, ResourceAccess[]>;
    }
  }

  async countResourcesByType(): Promise<Record<ResourceType, number>> {
    await this.ensureInitialized();
    
    try {
      const resourcesByType = await this.getResourcesByType();
      const counts: Record<string, number> = {};
      
      for (const [type, resources] of Object.entries(resourcesByType)) {
        counts[type] = resources.length;
      }
      
      return counts as Record<ResourceType, number>;
    } catch (error) {
      return {} as Record<ResourceType, number>;
    }
  }
}
