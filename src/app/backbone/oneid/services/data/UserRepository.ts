import 'server-only';
// User Repository Implementation using Direct File Access

import fs from 'fs/promises';
import path from 'path';
import { IUserRepository } from '../../core/interfaces';
import { User, PaginatedResponse } from '../../core/types';
import { generateId, getCurrentTimestamp, createPaginatedResponse } from '../../core/utils';

export class UserRepository implements IUserRepository {
  private readonly dataPath: string;
  private readonly filePath: string;

  constructor() {
    this.dataPath = path.join(process.cwd(), 'data/apps/backbone/oneid/users');
    this.filePath = path.join(this.dataPath, 'users.json');
  }

  private async ensureDataDirectory() {
    try {
      await fs.mkdir(this.dataPath, { recursive: true });
    } catch (error) {
      // Directory already exists
    }
  }

  private async readUsers(): Promise<User[]> {
    try {
      await this.ensureDataDirectory();
      const data = await fs.readFile(this.filePath, 'utf-8');
      return JSON.parse(data);
    } catch (error) {
      // File doesn't exist, return empty array
      return [];
    }
  }

  private async writeUsers(users: User[]): Promise<void> {
    await this.ensureDataDirectory();
    await fs.writeFile(this.filePath, JSON.stringify(users, null, 2));
  }

  async create(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    const users = await this.readUsers();

    const user: User = {
      ...userData,
      id: generateId(),
      createdAt: getCurrentTimestamp(),
      updatedAt: getCurrentTimestamp()
    };

    users.push(user);
    await this.writeUsers(users);

    return user;
  }

  async findById(id: string): Promise<User | null> {
    const users = await this.readUsers();
    return users.find(user => user.id === id) || null;
  }

  async findByUsername(username: string): Promise<User | null> {
    const users = await this.readUsers();
    return users.find(user => user.username.toLowerCase() === username.toLowerCase()) || null;
  }

  async findByEmail(email: string): Promise<User | null> {
    if (!email) return null;

    const users = await this.readUsers();
    return users.find(user => user.email?.toLowerCase() === email.toLowerCase()) || null;
  }

  async update(id: string, updates: Partial<User>): Promise<User | null> {
    const users = await this.readUsers();
    const userIndex = users.findIndex(user => user.id === id);

    if (userIndex === -1) return null;

    const updateData = {
      ...updates,
      updatedAt: getCurrentTimestamp()
    };

    users[userIndex] = { ...users[userIndex], ...updateData };
    await this.writeUsers(users);

    return users[userIndex];
  }

  async delete(id: string): Promise<boolean> {
    const users = await this.readUsers();
    const userIndex = users.findIndex(user => user.id === id);

    if (userIndex === -1) return false;

    users.splice(userIndex, 1);
    await this.writeUsers(users);

    return true;
  }

  async findMany(
    filters: Partial<User>,
    options: {
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<PaginatedResponse<User>> {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = options;

    const users = await this.readUsers();

    // Filter users
    const filteredUsers = users.filter(user => {
      return Object.entries(filters).every(([key, value]) => {
        if (value === undefined) return true;
        const userValue = user[key as keyof User];
        return userValue === value;
      });
    });

    // Sort users
    filteredUsers.sort((a: User, b: User) => {
      const aValue = a[sortBy as keyof User];
      const bValue = b[sortBy as keyof User];

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    // Paginate
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

    return createPaginatedResponse(paginatedUsers, page, limit, filteredUsers.length);
  }

  async findByCompanyId(companyId: string): Promise<User[]> {
    const users = await this.readUsers();
    return users.filter(user => user.companyId === companyId);
  }

  async countByCompanyId(companyId: string): Promise<number> {
    const users = await this.findByCompanyId(companyId);
    return users.length;
  }

  async updateStatus(id: string, status: User['status']): Promise<boolean> {
    const updated = await this.update(id, { status });
    return updated !== null;
  }

  async updateLastLogin(id: string): Promise<boolean> {
    const updated = await this.update(id, {
      lastLoginAt: getCurrentTimestamp()
    });
    return updated !== null;
  }

  // Additional utility methods
  async findByIdentifier(identifier: string): Promise<User | null> {
    // Try to find by username first, then by email
    let user = await this.findByUsername(identifier);
    if (!user && identifier.includes('@')) {
      user = await this.findByEmail(identifier);
    }
    return user;
  }

  async isUsernameAvailable(username: string): Promise<boolean> {
    const user = await this.findByUsername(username);
    return user === null;
  }

  async isEmailAvailable(email: string): Promise<boolean> {
    if (!email) return true;
    const user = await this.findByEmail(email);
    return user === null;
  }

  async getUserStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    suspended: number;
    byType: Record<User['userType'], number>;
  }> {
    const allUsers = await this.readUsers();

    const stats = {
      total: allUsers.length,
      active: 0,
      inactive: 0,
      suspended: 0,
      byType: {
        individual: 0,
        employee: 0,
        company_admin: 0
      } as Record<User['userType'], number>
    };

    allUsers.forEach((user: User) => {
      // Count by status
      if (user.status === 'active') stats.active++;
      else if (user.status === 'inactive') stats.inactive++;
      else if (user.status === 'suspended') stats.suspended++;

      // Count by type
      stats.byType[user.userType]++;
    });

    return stats;
  }

  async searchUsers(query: string, filters: {
    userType?: User['userType'];
    status?: User['status'];
    companyId?: string;
  } = {}): Promise<User[]> {
    const users = await this.readUsers();

    // Apply filters
    let filteredUsers = users.filter(user => {
      if (filters.userType && user.userType !== filters.userType) return false;
      if (filters.status && user.status !== filters.status) return false;
      if (filters.companyId && user.companyId !== filters.companyId) return false;
      return true;
    });

    if (!query) return filteredUsers;

    const searchQuery = query.toLowerCase();
    return filteredUsers.filter((user: User) =>
      user.username.toLowerCase().includes(searchQuery) ||
      user.firstName.toLowerCase().includes(searchQuery) ||
      user.lastName.toLowerCase().includes(searchQuery) ||
      (user.email && user.email.toLowerCase().includes(searchQuery))
    );
  }

  async findAll(): Promise<User[]> {
    return await this.readUsers();
  }
}
