import 'server-only';
// Tenant Repository - Data access layer for tenant management

import { ITenantRepository } from '../../core/interfaces';
import { Tenant, PaginatedResponse } from '../../core/types';
import { getDataPath } from '../../core/utils';
import * as fs from 'fs/promises';
import * as path from 'path';

export class TenantRepository implements ITenantRepository {
  private dataPath: string;

  constructor() {
    this.dataPath = getDataPath('tenants');
  }

  async create(tenant: Tenant): Promise<Tenant | null> {
    try {
      await this.ensureDataDirectory();
      
      const filePath = path.join(this.dataPath, `${tenant.id}.json`);
      await fs.writeFile(filePath, JSON.stringify(tenant, null, 2));
      
      // Also maintain an index file for quick lookups
      await this.updateTenantIndex(tenant);
      
      return tenant;
    } catch (error) {
      console.error('Error creating tenant:', error);
      return null;
    }
  }

  async findById(id: string): Promise<Tenant | null> {
    try {
      const filePath = path.join(this.dataPath, `${id}.json`);
      const data = await fs.readFile(filePath, 'utf-8');
      return JSON.parse(data);
    } catch (error) {
      if ((error as NodeJS.ErrnoException).code !== 'ENOENT') {
        console.error('Error finding tenant by ID:', error);
      }
      return null;
    }
  }

  async findByName(name: string): Promise<Tenant | null> {
    try {
      const tenants = await this.findAll();
      return tenants.data?.find(tenant => 
        tenant.name.toLowerCase() === name.toLowerCase()
      ) || null;
    } catch (error) {
      console.error('Error finding tenant by name:', error);
      return null;
    }
  }

  async findBySubdomain(subdomain: string): Promise<Tenant | null> {
    try {
      const tenants = await this.findAll();
      return tenants.data?.find(tenant => 
        tenant.subdomain?.toLowerCase() === subdomain.toLowerCase()
      ) || null;
    } catch (error) {
      console.error('Error finding tenant by subdomain:', error);
      return null;
    }
  }

  async findAll(options: {
    page?: number;
    limit?: number;
    status?: Tenant['status'];
    search?: string;
  } = {}): Promise<PaginatedResponse<Tenant>> {
    try {
      await this.ensureDataDirectory();
      
      const files = await fs.readdir(this.dataPath);
      const tenantFiles = files.filter(file => file.endsWith('.json') && file !== 'index.json');
      
      const tenants: Tenant[] = [];
      
      for (const file of tenantFiles) {
        try {
          const filePath = path.join(this.dataPath, file);
          const data = await fs.readFile(filePath, 'utf-8');
          const tenant = JSON.parse(data);
          tenants.push(tenant);
        } catch (error) {
          console.error(`Error reading tenant file ${file}:`, error);
        }
      }

      // Apply filters
      let filteredTenants = tenants;

      if (options.status) {
        filteredTenants = filteredTenants.filter(tenant => tenant.status === options.status);
      }

      if (options.search) {
        const searchLower = options.search.toLowerCase();
        filteredTenants = filteredTenants.filter(tenant =>
          tenant.name.toLowerCase().includes(searchLower) ||
          tenant.displayName?.toLowerCase().includes(searchLower) ||
          tenant.subdomain?.toLowerCase().includes(searchLower)
        );
      }

      // Sort by creation date (newest first)
      filteredTenants.sort((a, b) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

      // Apply pagination
      const page = options.page || 1;
      const limit = options.limit || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      
      const paginatedTenants = filteredTenants.slice(startIndex, endIndex);

      return {
        success: true,
        data: paginatedTenants,
        pagination: {
          page,
          limit,
          total: filteredTenants.length,
          totalPages: Math.ceil(filteredTenants.length / limit),
          hasNext: endIndex < filteredTenants.length,
          hasPrev: page > 1
        }
      };
    } catch (error) {
      console.error('Error finding all tenants:', error);
      return {
        success: false,
        error: 'Failed to retrieve tenants',
        data: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false
        }
      };
    }
  }

  async update(id: string, updates: Partial<Tenant>): Promise<Tenant | null> {
    try {
      const existing = await this.findById(id);
      if (!existing) {
        return null;
      }

      const updated: Tenant = {
        ...existing,
        ...updates,
        id: existing.id, // Ensure ID cannot be changed
        createdAt: existing.createdAt, // Ensure createdAt cannot be changed
        updatedAt: new Date().toISOString()
      };

      const filePath = path.join(this.dataPath, `${id}.json`);
      await fs.writeFile(filePath, JSON.stringify(updated, null, 2));
      
      // Update index
      await this.updateTenantIndex(updated);
      
      return updated;
    } catch (error) {
      console.error('Error updating tenant:', error);
      return null;
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      const filePath = path.join(this.dataPath, `${id}.json`);
      await fs.unlink(filePath);
      
      // Remove from index
      await this.removeFromTenantIndex(id);
      
      return true;
    } catch (error) {
      console.error('Error deleting tenant:', error);
      return false;
    }
  }

  async findByStatus(status: Tenant['status']): Promise<Tenant[]> {
    try {
      const result = await this.findAll({ status });
      return result.data || [];
    } catch (error) {
      console.error('Error finding tenants by status:', error);
      return [];
    }
  }

  // Private helper methods

  private async ensureDataDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.dataPath, { recursive: true });
    } catch (error) {
      console.error('Error creating tenant data directory:', error);
    }
  }

  private async updateTenantIndex(tenant: Tenant): Promise<void> {
    try {
      const indexPath = path.join(this.dataPath, 'index.json');
      let index: Record<string, { name: string; subdomain?: string; status: string; createdAt: string }> = {};
      
      try {
        const indexData = await fs.readFile(indexPath, 'utf-8');
        index = JSON.parse(indexData);
      } catch {
        // Index file doesn't exist or is invalid, start fresh
      }

      index[tenant.id] = {
        name: tenant.name,
        subdomain: tenant.subdomain,
        status: tenant.status,
        createdAt: tenant.createdAt
      };

      await fs.writeFile(indexPath, JSON.stringify(index, null, 2));
    } catch (error) {
      console.error('Error updating tenant index:', error);
    }
  }

  private async removeFromTenantIndex(tenantId: string): Promise<void> {
    try {
      const indexPath = path.join(this.dataPath, 'index.json');
      
      try {
        const indexData = await fs.readFile(indexPath, 'utf-8');
        const index = JSON.parse(indexData);
        
        delete index[tenantId];
        
        await fs.writeFile(indexPath, JSON.stringify(index, null, 2));
      } catch {
        // Index file doesn't exist or is invalid, nothing to remove
      }
    } catch (error) {
      console.error('Error removing from tenant index:', error);
    }
  }
}
