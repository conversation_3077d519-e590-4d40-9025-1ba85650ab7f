import 'server-only';
// Tenant Module Repository - Data access layer for tenant module management

import { ITenantModuleRepository } from '../../core/interfaces';
import { TenantModule } from '../../core/types';
import { getDataPath } from '../../core/utils';
import * as fs from 'fs/promises';
import * as path from 'path';

export class TenantModuleRepository implements ITenantModuleRepository {
  private dataPath: string;

  constructor() {
    this.dataPath = getDataPath('tenant-modules');
  }

  async create(moduleData: TenantModule): Promise<TenantModule | null> {
    try {
      await this.ensureDataDirectory();
      await this.ensureTenantDirectory(moduleData.tenantId);
      
      const filePath = path.join(this.dataPath, moduleData.tenantId, `${moduleData.id}.json`);
      await fs.writeFile(filePath, JSON.stringify(moduleData, null, 2));
      
      return moduleData;
    } catch (error) {
      console.error('Error creating tenant module:', error);
      return null;
    }
  }

  async findById(id: string): Promise<TenantModule | null> {
    try {
      // Search across all tenants for the module
      const tenantDirs = await this.getTenantDirectories();
      
      for (const tenantId of tenantDirs) {
        const filePath = path.join(this.dataPath, tenantId, `${id}.json`);
        try {
          const data = await fs.readFile(filePath, 'utf-8');
          return JSON.parse(data);
        } catch {
          // Continue searching in other tenants
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error finding tenant module by ID:', error);
      return null;
    }
  }

  async findByTenant(tenantId: string): Promise<TenantModule[]> {
    try {
      await this.ensureTenantDirectory(tenantId);
      
      const tenantPath = path.join(this.dataPath, tenantId);
      const files = await fs.readdir(tenantPath);
      const moduleFiles = files.filter(file => file.endsWith('.json'));
      
      const modules: TenantModule[] = [];
      
      for (const file of moduleFiles) {
        try {
          const filePath = path.join(tenantPath, file);
          const data = await fs.readFile(filePath, 'utf-8');
          const moduleData = JSON.parse(data);
          modules.push(moduleData);
        } catch (error) {
          console.error(`Error reading module file ${file}:`, error);
        }
      }

      // Sort by creation date (newest first)
      modules.sort((a, b) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

      return modules;
    } catch (error) {
      console.error('Error finding modules by tenant:', error);
      return [];
    }
  }

  async findByName(tenantId: string, name: string): Promise<TenantModule | null> {
    try {
      const modules = await this.findByTenant(tenantId);
      return modules.find(module => 
        module.name.toLowerCase() === name.toLowerCase()
      ) || null;
    } catch (error) {
      console.error('Error finding module by name:', error);
      return null;
    }
  }

  async findEnabledModules(tenantId: string): Promise<TenantModule[]> {
    try {
      const modules = await this.findByTenant(tenantId);
      return modules.filter(module => module.enabled);
    } catch (error) {
      console.error('Error finding enabled modules:', error);
      return [];
    }
  }

  async update(id: string, updates: Partial<TenantModule>): Promise<TenantModule | null> {
    try {
      const existing = await this.findById(id);
      if (!existing) {
        return null;
      }

      const updated: TenantModule = {
        ...existing,
        ...updates,
        id: existing.id, // Ensure ID cannot be changed
        tenantId: existing.tenantId, // Ensure tenantId cannot be changed
        createdAt: existing.createdAt, // Ensure createdAt cannot be changed
        updatedAt: new Date().toISOString()
      };

      const filePath = path.join(this.dataPath, existing.tenantId, `${id}.json`);
      await fs.writeFile(filePath, JSON.stringify(updated, null, 2));
      
      return updated;
    } catch (error) {
      console.error('Error updating tenant module:', error);
      return null;
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      const existing = await this.findById(id);
      if (!existing) {
        return false;
      }

      const filePath = path.join(this.dataPath, existing.tenantId, `${id}.json`);
      await fs.unlink(filePath);
      
      return true;
    } catch (error) {
      console.error('Error deleting tenant module:', error);
      return false;
    }
  }

  // Helper method to get available module templates
  async getAvailableModules(): Promise<Array<{
    name: string;
    displayName: string;
    description: string;
    defaultPermissions: string[];
    defaultSettings: Record<string, unknown>;
  }>> {
    return [
      {
        name: 'user_management',
        displayName: 'User Management',
        description: 'Comprehensive user and role management capabilities',
        defaultPermissions: [
          'user.read', 'user.write', 'user.delete',
          'role.read', 'role.write', 'role.assign'
        ],
        defaultSettings: {
          allowSelfRegistration: false,
          requireEmailVerification: true,
          passwordPolicy: {
            minLength: 8,
            requireUppercase: true,
            requireNumbers: true
          }
        }
      },
      {
        name: 'billing',
        displayName: 'Billing & Payments',
        description: 'Billing, invoicing, and payment processing',
        defaultPermissions: [
          'billing.read', 'billing.write',
          'invoice.read', 'invoice.write',
          'payment.read', 'payment.process'
        ],
        defaultSettings: {
          currency: 'USD',
          taxRate: 0.0,
          paymentMethods: ['card', 'bank_transfer']
        }
      },
      {
        name: 'reporting',
        displayName: 'Reports & Analytics',
        description: 'Advanced reporting and analytics dashboard',
        defaultPermissions: [
          'report.read', 'report.write',
          'analytics.read', 'dashboard.read'
        ],
        defaultSettings: {
          defaultDateRange: '30d',
          autoRefresh: true,
          exportFormats: ['pdf', 'csv', 'excel']
        }
      },
      {
        name: 'integrations',
        displayName: 'Third-party Integrations',
        description: 'Connect with external services and APIs',
        defaultPermissions: [
          'integration.read', 'integration.write',
          'api.read', 'webhook.manage'
        ],
        defaultSettings: {
          maxIntegrations: 10,
          rateLimiting: true,
          webhookRetries: 3
        }
      },
      {
        name: 'api_access',
        displayName: 'API Access',
        description: 'REST API access and management',
        defaultPermissions: [
          'api.read', 'api.write',
          'api_key.manage', 'api.admin'
        ],
        defaultSettings: {
          rateLimit: 1000,
          allowedMethods: ['GET', 'POST', 'PUT', 'DELETE'],
          requireApiKey: true
        }
      },
      {
        name: 'custom_branding',
        displayName: 'Custom Branding',
        description: 'Customize the look and feel of your tenant',
        defaultPermissions: [
          'branding.read', 'branding.write',
          'theme.read', 'theme.write'
        ],
        defaultSettings: {
          allowLogoUpload: true,
          allowCustomCSS: true,
          maxLogoSize: '2MB'
        }
      },
      {
        name: 'sso',
        displayName: 'Single Sign-On',
        description: 'SAML and OAuth SSO integration',
        defaultPermissions: [
          'sso.read', 'sso.write',
          'saml.manage', 'oauth.manage'
        ],
        defaultSettings: {
          protocols: ['saml2', 'oauth2'],
          autoProvisioning: false,
          roleMapping: {}
        }
      },
      {
        name: 'audit_logs',
        displayName: 'Audit Logs',
        description: 'Comprehensive audit logging and compliance',
        defaultPermissions: [
          'audit.read', 'audit.export',
          'compliance.read'
        ],
        defaultSettings: {
          retentionPeriod: '1y',
          logLevel: 'info',
          exportFormats: ['json', 'csv']
        }
      },
      {
        name: 'workflow',
        displayName: 'Workflow Management',
        description: 'Custom workflows and approval processes',
        defaultPermissions: [
          'workflow.read', 'workflow.write',
          'approval.read', 'approval.process'
        ],
        defaultSettings: {
          maxWorkflows: 50,
          allowConditional: true,
          notificationChannels: ['email', 'webhook']
        }
      },
      {
        name: 'notifications',
        displayName: 'Notifications',
        description: 'Multi-channel notification system',
        defaultPermissions: [
          'notification.read', 'notification.write',
          'template.read', 'template.write'
        ],
        defaultSettings: {
          channels: ['email', 'sms', 'push'],
          templates: {},
          rateLimiting: true
        }
      }
    ];
  }

  // Private helper methods

  private async ensureDataDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.dataPath, { recursive: true });
    } catch (error) {
      console.error('Error creating tenant modules data directory:', error);
    }
  }

  private async ensureTenantDirectory(tenantId: string): Promise<void> {
    try {
      const tenantPath = path.join(this.dataPath, tenantId);
      await fs.mkdir(tenantPath, { recursive: true });
    } catch (error) {
      console.error('Error creating tenant directory:', error);
    }
  }

  private async getTenantDirectories(): Promise<string[]> {
    try {
      await this.ensureDataDirectory();
      const items = await fs.readdir(this.dataPath, { withFileTypes: true });
      return items
        .filter(item => item.isDirectory())
        .map(item => item.name);
    } catch (error) {
      console.error('Error getting tenant directories:', error);
      return [];
    }
  }
}
