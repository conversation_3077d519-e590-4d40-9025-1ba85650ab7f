import 'server-only';
// UserRole Repository Implementation using Unified Data Access Layer

import { initializeDataAccess } from '@/app/backbone/dataaccess';
import { IUserRoleRepository } from '../../core/interfaces';
import { UserRole } from '../../core/types';
import { generateId, getCurrentTimestamp } from '../../core/utils';

export class UserRoleRepository implements IUserRoleRepository {
  private dataService: any;
  private readonly collectionName = 'user_roles';

  constructor() {
    this.initialize();
  }

  private async initialize() {
    this.dataService = await initializeDataAccess('backbone-oneid');
  }

  private async ensureInitialized() {
    if (!this.dataService) {
      await this.initialize();
    }
  }

  async create(userRoleData: Omit<UserRole, 'id'>): Promise<UserRole> {
    await this.ensureInitialized();
    
    const userRole: UserRole = {
      ...userRoleData,
      id: generateId()
    };

    await this.dataService.create(this.collectionName, userRole);
    return userRole;
  }

  async findById(id: string): Promise<UserRole | null> {
    await this.ensureInitialized();
    
    try {
      return await this.dataService.findById(this.collectionName, id);
    } catch (error) {
      return null;
    }
  }

  async findByUserId(userId: string): Promise<UserRole[]> {
    await this.ensureInitialized();
    
    try {
      const userRoles = await this.dataService.findMany(this.collectionName, { userId });
      return userRoles || [];
    } catch (error) {
      return [];
    }
  }

  async findByRoleId(roleId: string): Promise<UserRole[]> {
    await this.ensureInitialized();
    
    try {
      const userRoles = await this.dataService.findMany(this.collectionName, { roleId });
      return userRoles || [];
    } catch (error) {
      return [];
    }
  }

  async findByUserAndCompany(userId: string, companyId?: string): Promise<UserRole[]> {
    await this.ensureInitialized();
    
    try {
      const filters: any = { userId };
      if (companyId) {
        filters.companyId = companyId;
      }
      
      const userRoles = await this.dataService.findMany(this.collectionName, filters);
      return userRoles || [];
    } catch (error) {
      return [];
    }
  }

  async findActiveByUserId(userId: string): Promise<UserRole[]> {
    await this.ensureInitialized();
    
    try {
      const allUserRoles = await this.findByUserId(userId);
      const now = new Date().toISOString();
      
      return allUserRoles.filter(userRole => 
        userRole.isActive && 
        (!userRole.expiresAt || userRole.expiresAt > now)
      );
    } catch (error) {
      return [];
    }
  }

  async update(id: string, updates: Partial<UserRole>): Promise<UserRole | null> {
    await this.ensureInitialized();
    
    try {
      return await this.dataService.update(this.collectionName, id, updates);
    } catch (error) {
      return null;
    }
  }

  async delete(id: string): Promise<boolean> {
    await this.ensureInitialized();
    
    try {
      return await this.dataService.delete(this.collectionName, id);
    } catch (error) {
      return false;
    }
  }

  async deactivateExpired(): Promise<number> {
    await this.ensureInitialized();
    
    try {
      const allUserRoles = await this.dataService.findAll(this.collectionName);
      const now = new Date().toISOString();
      let deactivatedCount = 0;
      
      for (const userRole of allUserRoles) {
        if (userRole.isActive && userRole.expiresAt && userRole.expiresAt <= now) {
          await this.update(userRole.id, { isActive: false });
          deactivatedCount++;
        }
      }
      
      return deactivatedCount;
    } catch (error) {
      return 0;
    }
  }

  async revokeUserRole(userId: string, roleId: string, companyId?: string): Promise<boolean> {
    await this.ensureInitialized();
    
    try {
      const filters: any = { userId, roleId };
      if (companyId) {
        filters.companyId = companyId;
      }
      
      const userRoles = await this.dataService.findMany(this.collectionName, filters);
      
      for (const userRole of userRoles) {
        await this.update(userRole.id, { isActive: false });
      }
      
      return userRoles.length > 0;
    } catch (error) {
      return false;
    }
  }

  // Helper methods for common user role queries
  async findActiveByUserAndCompany(userId: string, companyId?: string): Promise<UserRole[]> {
    await this.ensureInitialized();
    
    try {
      const userRoles = await this.findByUserAndCompany(userId, companyId);
      const now = new Date().toISOString();
      
      return userRoles.filter(userRole => 
        userRole.isActive && 
        (!userRole.expiresAt || userRole.expiresAt > now)
      );
    } catch (error) {
      return [];
    }
  }

  async hasActiveRole(userId: string, roleId: string, companyId?: string): Promise<boolean> {
    await this.ensureInitialized();
    
    try {
      const activeRoles = await this.findActiveByUserAndCompany(userId, companyId);
      return activeRoles.some(userRole => userRole.roleId === roleId);
    } catch (error) {
      return false;
    }
  }

  async getUserRolesByCompany(userId: string): Promise<Record<string, UserRole[]>> {
    await this.ensureInitialized();
    
    try {
      const userRoles = await this.findActiveByUserId(userId);
      const rolesByCompany: Record<string, UserRole[]> = {};
      
      for (const userRole of userRoles) {
        const companyKey = userRole.companyId || 'global';
        if (!rolesByCompany[companyKey]) {
          rolesByCompany[companyKey] = [];
        }
        rolesByCompany[companyKey].push(userRole);
      }
      
      return rolesByCompany;
    } catch (error) {
      return {};
    }
  }

  async countActiveUsersByRole(roleId: string): Promise<number> {
    await this.ensureInitialized();
    
    try {
      const activeUserRoles = await this.dataService.findMany(this.collectionName, { 
        roleId, 
        isActive: true 
      });
      
      // Count unique users
      const uniqueUsers = new Set(activeUserRoles.map((ur: UserRole) => ur.userId));
      return uniqueUsers.size;
    } catch (error) {
      return 0;
    }
  }
}
