import 'server-only';
// Wallet Repository for OneID Integration
import { WalletData, WalletTransaction } from '../../core/types';
import { initializeDataAccess, DataAccessService } from '../../../dataaccess';
import walletDataConfig from '../../config/wallet-data-config';

export interface IWalletRepository {
  // Wallet operations
  createWallet(wallet: WalletData): Promise<WalletData>;
  getWalletById(walletId: string): Promise<WalletData | null>;
  getWalletByUserId(userId: string): Promise<WalletData | null>;
  updateWallet(walletId: string, wallet: WalletData): Promise<WalletData>;
  deleteWallet(walletId: string): Promise<boolean>;
  
  // Transaction operations
  createTransaction(transaction: WalletTransaction): Promise<WalletTransaction>;
  getTransactionById(transactionId: string): Promise<WalletTransaction | null>;
  getTransactionsByUserId(userId: string, limit?: number, offset?: number): Promise<WalletTransaction[]>;
  getTransactionsByWalletId(walletId: string, limit?: number, offset?: number): Promise<WalletTransaction[]>;
  updateTransaction(transactionId: string, transaction: WalletTransaction): Promise<WalletTransaction>;
  
  // Utility operations
  getAllWallets(): Promise<WalletData[]>;
  getWalletStats(): Promise<{ totalWallets: number; totalBalance: number; activeCurrencies: string[] }>;
}

export class WalletRepository implements IWalletRepository {
  private dataService: DataAccessService | null = null;
  private readonly walletsCollection = 'wallets';
  private readonly transactionsCollection = 'wallet_transactions';

  constructor() {
    this.initialize();
  }

  private async initialize() {
    try {
      this.dataService = await initializeDataAccess('backbone-oneid-wallet');
      await this.dataService.configure(walletDataConfig);
    } catch (error) {
      console.error('Failed to initialize wallet data service:', error);
    }
  }

  private async ensureInitialized() {
    if (!this.dataService) {
      await this.initialize();
    }
    if (!this.dataService) {
      throw new Error('Failed to initialize wallet data service');
    }
  }

  // Wallet operations
  async createWallet(wallet: WalletData): Promise<WalletData> {
    await this.ensureInitialized();

    // Check if wallet already exists for this user
    const existingWallet = await this.getWalletByUserId(wallet.userId);
    if (existingWallet) {
      throw new Error('Wallet already exists for this user');
    }

    await this.dataService.create(this.walletsCollection, wallet);
    return wallet;
  }

  async getWalletById(walletId: string): Promise<WalletData | null> {
    await this.ensureInitialized();

    try {
      return await this.dataService.findOne(this.walletsCollection, { id: walletId });
    } catch (error) {
      console.error('Error getting wallet by ID:', error);
      return null;
    }
  }

  async getWalletByUserId(userId: string): Promise<WalletData | null> {
    await this.ensureInitialized();

    try {
      return await this.dataService.findOne(this.walletsCollection, { userId });
    } catch (error) {
      console.error('Error getting wallet by user ID:', error);
      return null;
    }
  }

  async updateWallet(walletId: string, wallet: WalletData): Promise<WalletData> {
    await this.ensureInitialized();

    const updated = await this.dataService.updateOne(this.walletsCollection, { id: walletId }, wallet);
    return updated || wallet;
  }

  async deleteWallet(walletId: string): Promise<boolean> {
    await this.ensureInitialized();

    try {
      return await this.dataService.deleteOne(this.walletsCollection, { id: walletId });
    } catch (error) {
      console.error('Error deleting wallet:', error);
      return false;
    }
  }

  // Transaction operations
  async createTransaction(transaction: WalletTransaction): Promise<WalletTransaction> {
    await this.ensureInitialized();

    await this.dataService.create(this.transactionsCollection, transaction);
    return transaction;
  }

  async getTransactionById(transactionId: string): Promise<WalletTransaction | null> {
    await this.ensureInitialized();

    try {
      return await this.dataService.findOne(this.transactionsCollection, { id: transactionId });
    } catch (error) {
      console.error('Error getting transaction by ID:', error);
      return null;
    }
  }

  async getTransactionsByUserId(userId: string, limit: number = 50, offset: number = 0): Promise<WalletTransaction[]> {
    await this.ensureInitialized();
    
    try {
      const transactions = await this.dataService.findMany(this.transactionsCollection, { userId });
      
      // Sort by creation date (newest first) and apply pagination
      const sortedTransactions = transactions
        .sort((a: WalletTransaction, b: WalletTransaction) => 
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )
        .slice(offset, offset + limit);
      
      return sortedTransactions;
    } catch (error) {
      console.error('Error getting transactions by user ID:', error);
      return [];
    }
  }

  async getTransactionsByWalletId(walletId: string, limit: number = 50, offset: number = 0): Promise<WalletTransaction[]> {
    await this.ensureInitialized();
    
    try {
      const transactions = await this.dataService.findMany(this.transactionsCollection, { walletId });
      
      // Sort by creation date (newest first) and apply pagination
      const sortedTransactions = transactions
        .sort((a: WalletTransaction, b: WalletTransaction) => 
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )
        .slice(offset, offset + limit);
      
      return sortedTransactions;
    } catch (error) {
      console.error('Error getting transactions by wallet ID:', error);
      return [];
    }
  }

  async updateTransaction(transactionId: string, transaction: WalletTransaction): Promise<WalletTransaction> {
    await this.ensureInitialized();

    const updated = await this.dataService.updateOne(this.transactionsCollection, { id: transactionId }, transaction);
    return updated || transaction;
  }

  // Utility operations
  async getAllWallets(): Promise<WalletData[]> {
    await this.ensureInitialized();
    
    try {
      return await this.dataService.findMany(this.walletsCollection, {});
    } catch (error) {
      console.error('Error getting all wallets:', error);
      return [];
    }
  }

  async getWalletStats(): Promise<{ totalWallets: number; totalBalance: number; activeCurrencies: string[] }> {
    await this.ensureInitialized();
    
    try {
      const wallets = await this.getAllWallets();
      const activeWallets = wallets.filter(w => w.status === 'active');
      
      const totalWallets = activeWallets.length;
      const totalBalance = activeWallets.reduce((sum, wallet) => sum + wallet.balance, 0);
      const activeCurrencies = [...new Set(activeWallets.map(w => w.currency))];
      
      return {
        totalWallets,
        totalBalance,
        activeCurrencies
      };
    } catch (error) {
      console.error('Error getting wallet stats:', error);
      return {
        totalWallets: 0,
        totalBalance: 0,
        activeCurrencies: []
      };
    }
  }

  // Additional utility methods for wallet management
  async getWalletsByStatus(status: WalletData['status']): Promise<WalletData[]> {
    await this.ensureInitialized();
    
    try {
      return await this.dataService.findMany(this.walletsCollection, { status });
    } catch (error) {
      console.error('Error getting wallets by status:', error);
      return [];
    }
  }

  async getTransactionsByStatus(status: WalletTransaction['status'], limit: number = 100): Promise<WalletTransaction[]> {
    await this.ensureInitialized();
    
    try {
      const transactions = await this.dataService.findMany(this.transactionsCollection, { status });
      
      // Sort by creation date (newest first) and apply limit
      return transactions
        .sort((a: WalletTransaction, b: WalletTransaction) => 
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )
        .slice(0, limit);
    } catch (error) {
      console.error('Error getting transactions by status:', error);
      return [];
    }
  }

  async getTransactionsByType(type: WalletTransaction['type'], limit: number = 100): Promise<WalletTransaction[]> {
    await this.ensureInitialized();
    
    try {
      const transactions = await this.dataService.findMany(this.transactionsCollection, { type });
      
      // Sort by creation date (newest first) and apply limit
      return transactions
        .sort((a: WalletTransaction, b: WalletTransaction) => 
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )
        .slice(0, limit);
    } catch (error) {
      console.error('Error getting transactions by type:', error);
      return [];
    }
  }

  async getTransactionsByDateRange(
    startDate: string, 
    endDate: string, 
    userId?: string
  ): Promise<WalletTransaction[]> {
    await this.ensureInitialized();
    
    try {
      const filter = userId ? { userId } : {};
      const transactions = await this.dataService.findMany(this.transactionsCollection, filter);
      
      // Filter by date range
      return transactions.filter((transaction: WalletTransaction) => {
        const transactionDate = new Date(transaction.createdAt);
        const start = new Date(startDate);
        const end = new Date(endDate);
        return transactionDate >= start && transactionDate <= end;
      });
    } catch (error) {
      console.error('Error getting transactions by date range:', error);
      return [];
    }
  }
}
