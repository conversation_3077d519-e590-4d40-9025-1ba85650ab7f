import 'server-only';
// Role Repository Implementation using Unified Data Access Layer

import { initializeDataAccess } from '@/app/backbone/dataaccess';
import { IRoleRepository } from '../../core/interfaces';
import { Role } from '../../core/types';
import { generateId, getCurrentTimestamp } from '../../core/utils';

export class RoleRepository implements IRoleRepository {
  private dataService: any;
  private readonly collectionName = 'roles';

  constructor() {
    this.initialize();
  }

  private async initialize() {
    this.dataService = await initializeDataAccess('backbone-oneid');
  }

  private async ensureInitialized() {
    if (!this.dataService) {
      await this.initialize();
    }
  }

  async create(roleData: Omit<Role, 'id' | 'createdAt' | 'updatedAt'>): Promise<Role> {
    await this.ensureInitialized();
    
    const role: Role = {
      ...roleData,
      id: generateId(),
      createdAt: getCurrentTimestamp(),
      updatedAt: getCurrentTimestamp()
    };

    await this.dataService.create(this.collectionName, role);
    return role;
  }

  async findById(id: string): Promise<Role | null> {
    await this.ensureInitialized();
    
    try {
      return await this.dataService.findById(this.collectionName, id);
    } catch (error) {
      return null;
    }
  }

  async findByName(name: string): Promise<Role | null> {
    await this.ensureInitialized();
    
    try {
      const roles = await this.dataService.findMany(this.collectionName, { name });
      return roles && roles.length > 0 ? roles[0] : null;
    } catch (error) {
      return null;
    }
  }

  async findByCompany(companyId: string): Promise<Role[]> {
    await this.ensureInitialized();
    
    try {
      const roles = await this.dataService.findMany(this.collectionName, { companyId });
      return roles || [];
    } catch (error) {
      return [];
    }
  }

  async findSystemRoles(): Promise<Role[]> {
    await this.ensureInitialized();
    
    try {
      const roles = await this.dataService.findMany(this.collectionName, { isSystemRole: true });
      return roles || [];
    } catch (error) {
      return [];
    }
  }

  async findMany(filters: Partial<Role> = {}): Promise<Role[]> {
    await this.ensureInitialized();
    
    try {
      const roles = await this.dataService.findMany(this.collectionName, filters);
      return roles || [];
    } catch (error) {
      return [];
    }
  }

  async update(id: string, updates: Partial<Role>): Promise<Role | null> {
    await this.ensureInitialized();
    
    try {
      const updatedData = {
        ...updates,
        updatedAt: getCurrentTimestamp()
      };
      
      return await this.dataService.update(this.collectionName, id, updatedData);
    } catch (error) {
      return null;
    }
  }

  async delete(id: string): Promise<boolean> {
    await this.ensureInitialized();
    
    try {
      // Check if it's a system role
      const role = await this.findById(id);
      if (role && role.isSystemRole) {
        throw new Error('Cannot delete system role');
      }
      
      return await this.dataService.delete(this.collectionName, id);
    } catch (error) {
      return false;
    }
  }

  async findAll(): Promise<Role[]> {
    await this.ensureInitialized();
    
    try {
      const roles = await this.dataService.findAll(this.collectionName);
      return roles || [];
    } catch (error) {
      return [];
    }
  }

  // Helper methods for common role queries
  async findGlobalRoles(): Promise<Role[]> {
    await this.ensureInitialized();
    
    try {
      // Global roles are those without a specific company ID
      const roles = await this.dataService.findMany(this.collectionName, { 
        companyId: { $exists: false } 
      });
      return roles || [];
    } catch (error) {
      // Fallback: filter manually
      const allRoles = await this.findAll();
      return allRoles.filter(role => !role.companyId);
    }
  }

  async searchRoles(query: string, companyId?: string): Promise<Role[]> {
    await this.ensureInitialized();
    
    try {
      let roles: Role[];
      
      if (companyId) {
        // Search within company roles and global roles
        const companyRoles = await this.findByCompany(companyId);
        const globalRoles = await this.findGlobalRoles();
        roles = [...companyRoles, ...globalRoles];
      } else {
        roles = await this.findAll();
      }
      
      if (!query) return roles;
      
      const searchQuery = query.toLowerCase();
      return roles.filter(role => 
        role.name.toLowerCase().includes(searchQuery) ||
        role.description.toLowerCase().includes(searchQuery)
      );
    } catch (error) {
      return [];
    }
  }

  async getRoleWithPermissions(id: string): Promise<Role & { permissionDetails?: any[] } | null> {
    await this.ensureInitialized();
    
    try {
      const role = await this.findById(id);
      if (!role) return null;

      // This would typically join with permissions, but since we're using JSON storage,
      // we'll need to fetch permissions separately in the service layer
      return role;
    } catch (error) {
      return null;
    }
  }

  async countRolesByCompany(companyId: string): Promise<number> {
    await this.ensureInitialized();
    
    try {
      const roles = await this.findByCompany(companyId);
      return roles.length;
    } catch (error) {
      return 0;
    }
  }
}
