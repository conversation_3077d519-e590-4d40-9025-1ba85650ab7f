import 'server-only';
// Tenant User Repository - Data access layer for tenant user management

import { ITenantUserRepository } from '../../core/interfaces';
import { TenantUser, PaginatedResponse } from '../../core/types';
import { getDataPath } from '../../core/utils';
import * as fs from 'fs/promises';
import * as path from 'path';

export class TenantUserRepository implements ITenantUserRepository {
  private dataPath: string;

  constructor() {
    this.dataPath = getDataPath('tenant-users');
  }

  async create(user: TenantUser): Promise<TenantUser | null> {
    try {
      await this.ensureDataDirectory();
      await this.ensureTenantDirectory(user.tenantId);
      
      const filePath = path.join(this.dataPath, user.tenantId, `${user.id}.json`);
      await fs.writeFile(filePath, JSON.stringify(user, null, 2));
      
      // Update tenant user index
      await this.updateUserIndex(user);
      
      return user;
    } catch (error) {
      console.error('Error creating tenant user:', error);
      return null;
    }
  }

  async findById(id: string): Promise<TenantUser | null> {
    try {
      // Search across all tenants for the user
      const tenantDirs = await this.getTenantDirectories();
      
      for (const tenantId of tenantDirs) {
        const filePath = path.join(this.dataPath, tenantId, `${id}.json`);
        try {
          const data = await fs.readFile(filePath, 'utf-8');
          return JSON.parse(data);
        } catch {
          // Continue searching in other tenants
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error finding tenant user by ID:', error);
      return null;
    }
  }

  async findByTenant(
    tenantId: string,
    options: {
      page?: number;
      limit?: number;
      role?: string;
      department?: string;
      status?: TenantUser['status'];
    } = {}
  ): Promise<PaginatedResponse<TenantUser>> {
    try {
      await this.ensureTenantDirectory(tenantId);
      
      const tenantPath = path.join(this.dataPath, tenantId);
      const files = await fs.readdir(tenantPath);
      const userFiles = files.filter(file => file.endsWith('.json') && file !== 'index.json');
      
      const users: TenantUser[] = [];
      
      for (const file of userFiles) {
        try {
          const filePath = path.join(tenantPath, file);
          const data = await fs.readFile(filePath, 'utf-8');
          const user = JSON.parse(data);
          users.push(user);
        } catch (error) {
          console.error(`Error reading user file ${file}:`, error);
        }
      }

      // Apply filters
      let filteredUsers = users;

      if (options.role) {
        filteredUsers = filteredUsers.filter(user => 
          user.roles.includes(options.role!)
        );
      }

      if (options.department) {
        filteredUsers = filteredUsers.filter(user => 
          user.department === options.department
        );
      }

      if (options.status) {
        filteredUsers = filteredUsers.filter(user => 
          user.status === options.status
        );
      }

      // Sort by creation date (newest first)
      filteredUsers.sort((a, b) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );

      // Apply pagination
      const page = options.page || 1;
      const limit = options.limit || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      
      const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

      return {
        success: true,
        data: paginatedUsers,
        pagination: {
          page,
          limit,
          total: filteredUsers.length,
          totalPages: Math.ceil(filteredUsers.length / limit),
          hasNext: endIndex < filteredUsers.length,
          hasPrev: page > 1
        }
      };
    } catch (error) {
      console.error('Error finding users by tenant:', error);
      return {
        success: false,
        error: 'Failed to retrieve tenant users',
        data: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false
        }
      };
    }
  }

  async findByUsername(tenantId: string, username: string): Promise<TenantUser | null> {
    try {
      const result = await this.findByTenant(tenantId);
      return result.data?.find(user => 
        user.username.toLowerCase() === username.toLowerCase()
      ) || null;
    } catch (error) {
      console.error('Error finding user by username:', error);
      return null;
    }
  }

  async findByEmail(tenantId: string, email: string): Promise<TenantUser | null> {
    try {
      const result = await this.findByTenant(tenantId);
      return result.data?.find(user => 
        user.email?.toLowerCase() === email.toLowerCase()
      ) || null;
    } catch (error) {
      console.error('Error finding user by email:', error);
      return null;
    }
  }

  async findByRole(tenantId: string, role: string): Promise<TenantUser[]> {
    try {
      const result = await this.findByTenant(tenantId, { role });
      return result.data || [];
    } catch (error) {
      console.error('Error finding users by role:', error);
      return [];
    }
  }

  async findByDepartment(tenantId: string, department: string): Promise<TenantUser[]> {
    try {
      const result = await this.findByTenant(tenantId, { department });
      return result.data || [];
    } catch (error) {
      console.error('Error finding users by department:', error);
      return [];
    }
  }

  async update(id: string, updates: Partial<TenantUser>): Promise<TenantUser | null> {
    try {
      const existing = await this.findById(id);
      if (!existing) {
        return null;
      }

      const updated: TenantUser = {
        ...existing,
        ...updates,
        id: existing.id, // Ensure ID cannot be changed
        tenantId: existing.tenantId, // Ensure tenantId cannot be changed
        createdAt: existing.createdAt, // Ensure createdAt cannot be changed
        updatedAt: new Date().toISOString()
      };

      const filePath = path.join(this.dataPath, existing.tenantId, `${id}.json`);
      await fs.writeFile(filePath, JSON.stringify(updated, null, 2));
      
      // Update index
      await this.updateUserIndex(updated);
      
      return updated;
    } catch (error) {
      console.error('Error updating tenant user:', error);
      return null;
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      const existing = await this.findById(id);
      if (!existing) {
        return false;
      }

      const filePath = path.join(this.dataPath, existing.tenantId, `${id}.json`);
      await fs.unlink(filePath);
      
      // Remove from index
      await this.removeFromUserIndex(existing.tenantId, id);
      
      return true;
    } catch (error) {
      console.error('Error deleting tenant user:', error);
      return false;
    }
  }

  // Private helper methods

  private async ensureDataDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.dataPath, { recursive: true });
    } catch (error) {
      console.error('Error creating tenant users data directory:', error);
    }
  }

  private async ensureTenantDirectory(tenantId: string): Promise<void> {
    try {
      const tenantPath = path.join(this.dataPath, tenantId);
      await fs.mkdir(tenantPath, { recursive: true });
    } catch (error) {
      console.error('Error creating tenant directory:', error);
    }
  }

  private async getTenantDirectories(): Promise<string[]> {
    try {
      await this.ensureDataDirectory();
      const items = await fs.readdir(this.dataPath, { withFileTypes: true });
      return items
        .filter(item => item.isDirectory())
        .map(item => item.name);
    } catch (error) {
      console.error('Error getting tenant directories:', error);
      return [];
    }
  }

  private async updateUserIndex(user: TenantUser): Promise<void> {
    try {
      const indexPath = path.join(this.dataPath, user.tenantId, 'index.json');
      let index: Record<string, { 
        username: string; 
        email?: string; 
        roles: string[]; 
        status: string; 
        createdAt: string;
      }> = {};
      
      try {
        const indexData = await fs.readFile(indexPath, 'utf-8');
        index = JSON.parse(indexData);
      } catch {
        // Index file doesn't exist or is invalid, start fresh
      }

      index[user.id] = {
        username: user.username,
        email: user.email,
        roles: user.roles,
        status: user.status,
        createdAt: user.createdAt
      };

      await fs.writeFile(indexPath, JSON.stringify(index, null, 2));
    } catch (error) {
      console.error('Error updating user index:', error);
    }
  }

  private async removeFromUserIndex(tenantId: string, userId: string): Promise<void> {
    try {
      const indexPath = path.join(this.dataPath, tenantId, 'index.json');
      
      try {
        const indexData = await fs.readFile(indexPath, 'utf-8');
        const index = JSON.parse(indexData);
        
        delete index[userId];
        
        await fs.writeFile(indexPath, JSON.stringify(index, null, 2));
      } catch {
        // Index file doesn't exist or is invalid, nothing to remove
      }
    } catch (error) {
      console.error('Error removing from user index:', error);
    }
  }
}
