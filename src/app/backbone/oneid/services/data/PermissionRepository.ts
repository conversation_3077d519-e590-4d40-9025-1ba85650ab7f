import 'server-only';
// Permission Repository Implementation using Unified Data Access Layer

import { initializeDataAccess } from '@/app/backbone/dataaccess';
import { IPermissionRepository } from '../../core/interfaces';
import { Permission, ResourceType, PermissionAction } from '../../core/types';
import { generateId, getCurrentTimestamp } from '../../core/utils';

export class PermissionRepository implements IPermissionRepository {
  private dataService: any;
  private readonly collectionName = 'permissions';

  constructor() {
    this.initialize();
  }

  private async initialize() {
    this.dataService = await initializeDataAccess('backbone-oneid');
  }

  private async ensureInitialized() {
    if (!this.dataService) {
      await this.initialize();
    }
  }

  async create(permissionData: Omit<Permission, 'id' | 'createdAt' | 'updatedAt'>): Promise<Permission> {
    await this.ensureInitialized();
    
    const permission: Permission = {
      ...permissionData,
      id: generateId(),
      createdAt: getCurrentTimestamp(),
      updatedAt: getCurrentTimestamp()
    };

    await this.dataService.create(this.collectionName, permission);
    return permission;
  }

  async findById(id: string): Promise<Permission | null> {
    await this.ensureInitialized();
    
    try {
      return await this.dataService.findById(this.collectionName, id);
    } catch (error) {
      return null;
    }
  }

  async findByResource(resource: ResourceType): Promise<Permission[]> {
    await this.ensureInitialized();
    
    try {
      const permissions = await this.dataService.findMany(this.collectionName, { resource });
      return permissions || [];
    } catch (error) {
      return [];
    }
  }

  async findByAction(action: PermissionAction): Promise<Permission[]> {
    await this.ensureInitialized();
    
    try {
      const permissions = await this.dataService.findMany(this.collectionName, { action });
      return permissions || [];
    } catch (error) {
      return [];
    }
  }

  async findMany(filters: Partial<Permission> = {}): Promise<Permission[]> {
    await this.ensureInitialized();
    
    try {
      const permissions = await this.dataService.findMany(this.collectionName, filters);
      return permissions || [];
    } catch (error) {
      return [];
    }
  }

  async update(id: string, updates: Partial<Permission>): Promise<Permission | null> {
    await this.ensureInitialized();
    
    try {
      const updatedData = {
        ...updates,
        updatedAt: getCurrentTimestamp()
      };
      
      return await this.dataService.update(this.collectionName, id, updatedData);
    } catch (error) {
      return null;
    }
  }

  async delete(id: string): Promise<boolean> {
    await this.ensureInitialized();
    
    try {
      return await this.dataService.delete(this.collectionName, id);
    } catch (error) {
      return false;
    }
  }

  async findAll(): Promise<Permission[]> {
    await this.ensureInitialized();
    
    try {
      const permissions = await this.dataService.findAll(this.collectionName);
      return permissions || [];
    } catch (error) {
      return [];
    }
  }

  // Helper methods for common permission queries
  async findByResourceAndAction(resource: ResourceType, action: PermissionAction): Promise<Permission[]> {
    await this.ensureInitialized();
    
    try {
      const permissions = await this.dataService.findMany(this.collectionName, { 
        resource, 
        action 
      });
      return permissions || [];
    } catch (error) {
      return [];
    }
  }

  async findSystemPermissions(): Promise<Permission[]> {
    await this.ensureInitialized();
    
    try {
      // System permissions are those that apply to core system resources
      const systemResources: ResourceType[] = ['admin', 'api', 'module'];
      const allPermissions = await this.findAll();
      
      return allPermissions.filter(permission => 
        systemResources.includes(permission.resource)
      );
    } catch (error) {
      return [];
    }
  }

  async searchPermissions(query: string): Promise<Permission[]> {
    await this.ensureInitialized();
    
    try {
      const allPermissions = await this.findAll();
      
      if (!query) return allPermissions;
      
      const searchQuery = query.toLowerCase();
      return allPermissions.filter(permission => 
        permission.name.toLowerCase().includes(searchQuery) ||
        permission.description.toLowerCase().includes(searchQuery) ||
        permission.resource.toLowerCase().includes(searchQuery) ||
        permission.action.toLowerCase().includes(searchQuery)
      );
    } catch (error) {
      return [];
    }
  }
}
