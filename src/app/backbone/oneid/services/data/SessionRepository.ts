import 'server-only';
// Session Repository Implementation using Direct File Access

import fs from 'fs/promises';
import path from 'path';
import { ISessionRepository } from '../../core/interfaces';
import { Session } from '../../core/types';
import { generateId, getCurrentTimestamp, isExpired } from '../../core/utils';

export class SessionRepository implements ISessionRepository {
  private readonly dataPath: string;
  private readonly filePath: string;

  constructor() {
    this.dataPath = path.join(process.cwd(), 'data/apps/backbone/oneid/sessions');
    this.filePath = path.join(this.dataPath, 'sessions.json');
  }

  private async ensureDataDirectory() {
    try {
      await fs.mkdir(this.dataPath, { recursive: true });
    } catch (error) {
      // Directory already exists
    }
  }

  private async readSessions(): Promise<Session[]> {
    try {
      await this.ensureDataDirectory();
      const data = await fs.readFile(this.filePath, 'utf-8');
      return JSON.parse(data);
    } catch (error) {
      // File doesn't exist, return empty array
      return [];
    }
  }

  private async writeSessions(sessions: Session[]): Promise<void> {
    await this.ensureDataDirectory();
    await fs.writeFile(this.filePath, JSON.stringify(sessions, null, 2));
  }

  async create(sessionData: Omit<Session, 'id' | 'createdAt' | 'lastAccessedAt'>): Promise<Session> {
    const sessions = await this.readSessions();

    const session: Session = {
      ...sessionData,
      id: generateId(),
      createdAt: getCurrentTimestamp(),
      lastAccessedAt: getCurrentTimestamp()
    };

    sessions.push(session);
    await this.writeSessions(sessions);

    return session;
  }

  async findById(id: string): Promise<Session | null> {
    const sessions = await this.readSessions();
    return sessions.find(session => session.id === id) || null;
  }

  async findAll(): Promise<Session[]> {
    return await this.readSessions();
  }

  async findByToken(token: string): Promise<Session | null> {
    await this.ensureInitialized();
    
    try {
      const sessions = await this.dataService.findMany(this.collectionName, { token });
      return sessions.length > 0 ? sessions[0] : null;
    } catch (error) {
      return null;
    }
  }

  async findByUserId(userId: string): Promise<Session[]> {
    await this.ensureInitialized();
    
    try {
      const sessions = await this.dataService.findMany(this.collectionName, { userId });
      return sessions || [];
    } catch (error) {
      return [];
    }
  }

  async update(id: string, updates: Partial<Session>): Promise<Session | null> {
    await this.ensureInitialized();
    
    try {
      const updated = await this.dataService.update(this.collectionName, id, updates);
      return updated || null;
    } catch (error) {
      return null;
    }
  }

  async delete(id: string): Promise<boolean> {
    await this.ensureInitialized();
    
    try {
      await this.dataService.delete(this.collectionName, id);
      return true;
    } catch (error) {
      return false;
    }
  }

  async deleteByUserId(userId: string): Promise<boolean> {
    await this.ensureInitialized();
    
    try {
      const sessions = await this.findByUserId(userId);
      await Promise.all(sessions.map(session => this.delete(session.id)));
      return true;
    } catch (error) {
      return false;
    }
  }

  async deleteExpired(): Promise<number> {
    await this.ensureInitialized();
    
    try {
      const allSessions = await this.dataService.findMany(this.collectionName, {});
      const expiredSessions = allSessions.filter((session: Session) => 
        isExpired(session.expiresAt) || session.status === 'expired'
      );
      
      await Promise.all(expiredSessions.map((session: Session) => this.delete(session.id)));
      return expiredSessions.length;
    } catch (error) {
      return 0;
    }
  }

  async updateLastAccessed(id: string): Promise<boolean> {
    await this.ensureInitialized();
    
    try {
      const updated = await this.update(id, { 
        lastAccessedAt: getCurrentTimestamp() 
      });
      return updated !== null;
    } catch (error) {
      return false;
    }
  }

  async revokeSession(id: string): Promise<boolean> {
    await this.ensureInitialized();
    
    try {
      const updated = await this.update(id, { 
        status: 'revoked' 
      });
      return updated !== null;
    } catch (error) {
      return false;
    }
  }

  async revokeAllUserSessions(userId: string): Promise<number> {
    await this.ensureInitialized();
    
    try {
      const sessions = await this.findByUserId(userId);
      const activeSessions = sessions.filter(session => session.status === 'active');
      
      await Promise.all(activeSessions.map(session => this.revokeSession(session.id)));
      return activeSessions.length;
    } catch (error) {
      return 0;
    }
  }

  // Additional utility methods
  async getActiveSessions(userId: string): Promise<Session[]> {
    await this.ensureInitialized();
    
    try {
      const sessions = await this.findByUserId(userId);
      return sessions.filter(session => 
        session.status === 'active' && !isExpired(session.expiresAt)
      );
    } catch (error) {
      return [];
    }
  }

  async isValidSession(token: string): Promise<boolean> {
    const session = await this.findByToken(token);
    if (!session) return false;
    
    return session.status === 'active' && !isExpired(session.expiresAt);
  }

  async extendSession(id: string, additionalMinutes: number): Promise<boolean> {
    await this.ensureInitialized();
    
    try {
      const session = await this.findById(id);
      if (!session) return false;

      const currentExpiry = new Date(session.expiresAt);
      const newExpiry = new Date(currentExpiry.getTime() + additionalMinutes * 60000);
      
      const updated = await this.update(id, { 
        expiresAt: newExpiry.toISOString(),
        lastAccessedAt: getCurrentTimestamp()
      });
      
      return updated !== null;
    } catch (error) {
      return false;
    }
  }

  async getSessionStats(): Promise<{
    total: number;
    active: number;
    expired: number;
    revoked: number;
    byDevice: Record<string, number>;
  }> {
    await this.ensureInitialized();
    
    try {
      const allSessions = await this.dataService.findMany(this.collectionName, {});
      
      const stats = {
        total: allSessions.length,
        active: 0,
        expired: 0,
        revoked: 0,
        byDevice: {} as Record<string, number>
      };

      allSessions.forEach((session: Session) => {
        // Count by status
        if (session.status === 'active' && !isExpired(session.expiresAt)) {
          stats.active++;
        } else if (session.status === 'revoked') {
          stats.revoked++;
        } else {
          stats.expired++;
        }
        
        // Count by device type
        if (session.deviceInfo?.type) {
          const deviceType = session.deviceInfo.type;
          stats.byDevice[deviceType] = (stats.byDevice[deviceType] || 0) + 1;
        }
      });

      return stats;
    } catch (error) {
      return {
        total: 0,
        active: 0,
        expired: 0,
        revoked: 0,
        byDevice: {}
      };
    }
  }

  async cleanupOldSessions(olderThanDays: number = 30): Promise<number> {
    await this.ensureInitialized();
    
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
      
      const allSessions = await this.dataService.findMany(this.collectionName, {});
      const oldSessions = allSessions.filter((session: Session) => 
        new Date(session.createdAt) < cutoffDate && 
        (session.status === 'expired' || session.status === 'revoked')
      );
      
      await Promise.all(oldSessions.map((session: Session) => this.delete(session.id)));
      return oldSessions.length;
    } catch (error) {
      return 0;
    }
  }

  async getUserSessionHistory(userId: string, limit: number = 10): Promise<Session[]> {
    await this.ensureInitialized();
    
    try {
      const sessions = await this.findByUserId(userId);
      
      // Sort by creation date (newest first)
      sessions.sort((a: Session, b: Session) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
      
      return sessions.slice(0, limit);
    } catch (error) {
      return [];
    }
  }
}
