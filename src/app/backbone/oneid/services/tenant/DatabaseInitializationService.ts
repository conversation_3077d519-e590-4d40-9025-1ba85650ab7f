// Database Initialization Service - Initialize tenant-specific database schemas and data

import 'server-only';
import { IDatabaseInitializationService } from '../../core/interfaces';
import { getDataPath } from '../../core/utils';
import * as fs from 'fs/promises';
import * as path from 'path';

export class DatabaseInitializationService implements IDatabaseInitializationService {
  private dataPath: string;

  constructor() {
    this.dataPath = getDataPath('database-schemas');
  }

  /**
   * Initialize complete database for a tenant
   */
  async initializeTenantDatabase(tenantId: string, modules: string[] = []): Promise<boolean> {
    try {
      console.log(`Initializing database for tenant: ${tenantId}`);

      // 1. Create tenant schema
      const schemaCreated = await this.createTenantSchema(tenantId);
      if (!schemaCreated) {
        throw new Error('Failed to create tenant schema');
      }

      // 2. Initialize core modules
      const coreModules = ['user_management', 'auth', 'permissions'];
      for (const moduleName of coreModules) {
        await this.initializeModuleData(tenantId, moduleName);
      }

      // 3. Initialize additional requested modules
      for (const moduleName of modules) {
        if (!coreModules.includes(moduleName)) {
          await this.initializeModuleData(tenantId, moduleName);
        }
      }

      // 4. Create initial data directories
      await this.createDataDirectories(tenantId);

      // 5. Set up indexes and constraints
      await this.setupDatabaseIndexes(tenantId);

      console.log(`Database initialization completed for tenant: ${tenantId}`);
      return true;

    } catch (error) {
      console.error(`Error initializing database for tenant ${tenantId}:`, error);
      return false;
    }
  }

  /**
   * Create base schema for tenant
   */
  async createTenantSchema(tenantId: string): Promise<boolean> {
    try {
      await this.ensureSchemaDirectory();
      
      const schemaConfig = {
        tenantId,
        version: '1.0.0',
        createdAt: new Date().toISOString(),
        lastMigration: null,
        tables: {
          users: {
            indexes: ['username', 'email', 'status', 'createdAt'],
            constraints: ['unique_username', 'unique_email'],
            foreignKeys: []
          },
          roles: {
            indexes: ['name', 'isSystemRole', 'isDefault'],
            constraints: ['unique_name'],
            foreignKeys: []
          },
          user_roles: {
            indexes: ['userId', 'roleId', 'isActive'],
            constraints: ['unique_user_role'],
            foreignKeys: ['userId -> users.id', 'roleId -> roles.id']
          },
          permissions: {
            indexes: ['name', 'resource', 'action'],
            constraints: ['unique_permission'],
            foreignKeys: []
          },
          role_permissions: {
            indexes: ['roleId', 'permissionId'],
            constraints: ['unique_role_permission'],
            foreignKeys: ['roleId -> roles.id', 'permissionId -> permissions.id']
          },
          sessions: {
            indexes: ['userId', 'token', 'status', 'expiresAt'],
            constraints: ['unique_token'],
            foreignKeys: ['userId -> users.id']
          },
          audit_logs: {
            indexes: ['userId', 'action', 'timestamp', 'resource'],
            constraints: [],
            foreignKeys: ['userId -> users.id']
          }
        },
        modules: {},
        metadata: {
          description: `Database schema for tenant ${tenantId}`,
          owner: 'system',
          environment: 'production'
        }
      };

      const schemaPath = path.join(this.dataPath, `${tenantId}_schema.json`);
      await fs.writeFile(schemaPath, JSON.stringify(schemaConfig, null, 2));

      return true;
    } catch (error) {
      console.error('Error creating tenant schema:', error);
      return false;
    }
  }

  /**
   * Initialize data for a specific module
   */
  async initializeModuleData(tenantId: string, moduleName: string): Promise<boolean> {
    try {
      console.log(`Initializing module data: ${moduleName} for tenant: ${tenantId}`);

      const moduleConfig = this.getModuleConfig(moduleName);
      if (!moduleConfig) {
        console.warn(`No configuration found for module: ${moduleName}`);
        return false;
      }

      // Create module-specific data directories
      await this.createModuleDirectories(tenantId, moduleName);

      // Initialize module data
      await this.createModuleInitialData(tenantId, moduleName, moduleConfig);

      // Update schema with module information
      await this.updateSchemaWithModule(tenantId, moduleName, moduleConfig);

      return true;
    } catch (error) {
      console.error(`Error initializing module ${moduleName}:`, error);
      return false;
    }
  }

  /**
   * Migrate tenant data between versions
   */
  async migrateTenantData(tenantId: string, fromVersion: string, toVersion: string): Promise<boolean> {
    try {
      console.log(`Migrating tenant ${tenantId} from version ${fromVersion} to ${toVersion}`);

      // Load current schema
      const schemaPath = path.join(this.dataPath, `${tenantId}_schema.json`);
      const schemaData = await fs.readFile(schemaPath, 'utf-8');
      const schema = JSON.parse(schemaData);

      // Apply migration scripts
      const migrationResult = await this.applyMigrations(tenantId, fromVersion, toVersion);
      
      if (migrationResult) {
        // Update schema version
        schema.version = toVersion;
        schema.lastMigration = new Date().toISOString();
        await fs.writeFile(schemaPath, JSON.stringify(schema, null, 2));
      }

      return migrationResult;
    } catch (error) {
      console.error(`Error migrating tenant data:`, error);
      return false;
    }
  }

  /**
   * Validate tenant database integrity
   */
  async validateTenantDatabase(tenantId: string): Promise<{ valid: boolean; errors?: string[] }> {
    try {
      const errors: string[] = [];

      // Check if schema exists
      const schemaPath = path.join(this.dataPath, `${tenantId}_schema.json`);
      try {
        await fs.access(schemaPath);
      } catch {
        errors.push('Tenant schema file not found');
        return { valid: false, errors };
      }

      // Load and validate schema
      const schemaData = await fs.readFile(schemaPath, 'utf-8');
      const schema = JSON.parse(schemaData);

      if (!schema.tenantId || schema.tenantId !== tenantId) {
        errors.push('Schema tenant ID mismatch');
      }

      if (!schema.version) {
        errors.push('Schema version not specified');
      }

      // Check required tables
      const requiredTables = ['users', 'roles', 'permissions', 'sessions'];
      for (const table of requiredTables) {
        if (!schema.tables[table]) {
          errors.push(`Required table '${table}' not found in schema`);
        }
      }

      // Check data directories
      const tenantDataPath = getDataPath(`tenant-data/${tenantId}`);
      try {
        await fs.access(tenantDataPath);
      } catch {
        errors.push('Tenant data directory not found');
      }

      return { valid: errors.length === 0, errors: errors.length > 0 ? errors : undefined };

    } catch (error) {
      console.error('Error validating tenant database:', error);
      return { valid: false, errors: ['Database validation failed'] };
    }
  }

  // Private helper methods

  private async ensureSchemaDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.dataPath, { recursive: true });
    } catch (error) {
      console.error('Error creating schema directory:', error);
    }
  }

  private async createDataDirectories(tenantId: string): Promise<void> {
    const basePath = getDataPath(`tenant-data/${tenantId}`);
    
    const directories = [
      'users',
      'roles',
      'permissions',
      'user_roles',
      'role_permissions',
      'sessions',
      'audit_logs',
      'modules',
      'uploads',
      'backups',
      'cache'
    ];

    for (const dir of directories) {
      try {
        await fs.mkdir(path.join(basePath, dir), { recursive: true });
      } catch (error) {
        console.error(`Error creating directory ${dir}:`, error);
      }
    }
  }

  private async createModuleDirectories(tenantId: string, moduleName: string): Promise<void> {
    const basePath = getDataPath(`tenant-data/${tenantId}/modules/${moduleName}`);
    
    try {
      await fs.mkdir(basePath, { recursive: true });
      
      // Create module-specific subdirectories
      const moduleConfig = this.getModuleConfig(moduleName);
      if (moduleConfig?.directories) {
        for (const dir of moduleConfig.directories) {
          await fs.mkdir(path.join(basePath, dir), { recursive: true });
        }
      }
    } catch (error) {
      console.error(`Error creating module directories for ${moduleName}:`, error);
    }
  }

  private async createModuleInitialData(tenantId: string, moduleName: string, moduleConfig: any): Promise<void> {
    if (!moduleConfig.initialData) return;

    const modulePath = getDataPath(`tenant-data/${tenantId}/modules/${moduleName}`);

    for (const [fileName, data] of Object.entries(moduleConfig.initialData)) {
      try {
        const filePath = path.join(modulePath, `${fileName}.json`);
        await fs.writeFile(filePath, JSON.stringify(data, null, 2));
      } catch (error) {
        console.error(`Error creating initial data file ${fileName}:`, error);
      }
    }
  }

  private async updateSchemaWithModule(tenantId: string, moduleName: string, moduleConfig: any): Promise<void> {
    try {
      const schemaPath = path.join(this.dataPath, `${tenantId}_schema.json`);
      const schemaData = await fs.readFile(schemaPath, 'utf-8');
      const schema = JSON.parse(schemaData);

      schema.modules[moduleName] = {
        version: moduleConfig.version || '1.0.0',
        initializedAt: new Date().toISOString(),
        tables: moduleConfig.tables || {},
        config: moduleConfig.config || {}
      };

      await fs.writeFile(schemaPath, JSON.stringify(schema, null, 2));
    } catch (error) {
      console.error('Error updating schema with module:', error);
    }
  }

  private async setupDatabaseIndexes(tenantId: string): Promise<void> {
    // In a real implementation, this would create database indexes
    // For JSON file storage, we maintain index files for performance
    console.log(`Setting up database indexes for tenant: ${tenantId}`);
    
    const indexPath = getDataPath(`tenant-data/${tenantId}/indexes`);
    await fs.mkdir(indexPath, { recursive: true });

    // Create index files for common queries
    const indexes = {
      users_by_username: {},
      users_by_email: {},
      users_by_status: {},
      roles_by_name: {},
      sessions_by_user: {},
      sessions_by_token: {}
    };

    for (const [indexName, indexData] of Object.entries(indexes)) {
      const indexFile = path.join(indexPath, `${indexName}.json`);
      await fs.writeFile(indexFile, JSON.stringify(indexData, null, 2));
    }
  }

  private async applyMigrations(tenantId: string, fromVersion: string, toVersion: string): Promise<boolean> {
    // In a real implementation, this would apply database migration scripts
    console.log(`Applying migrations for tenant ${tenantId}: ${fromVersion} -> ${toVersion}`);
    
    // For now, just log the migration
    const migrationLog = {
      tenantId,
      fromVersion,
      toVersion,
      appliedAt: new Date().toISOString(),
      success: true
    };

    const logPath = getDataPath(`tenant-data/${tenantId}/migrations.log`);
    await fs.appendFile(logPath, JSON.stringify(migrationLog) + '\n');

    return true;
  }

  private getModuleConfig(moduleName: string): any {
    const moduleConfigs: Record<string, any> = {
      user_management: {
        version: '1.0.0',
        directories: ['profiles', 'preferences', 'settings'],
        tables: {
          user_profiles: {
            indexes: ['userId', 'profileType'],
            constraints: ['unique_user_profile'],
            foreignKeys: ['userId -> users.id']
          },
          user_preferences: {
            indexes: ['userId', 'category'],
            constraints: [],
            foreignKeys: ['userId -> users.id']
          }
        },
        initialData: {
          default_preferences: {
            notification_settings: {
              email: true,
              sms: false,
              push: true
            },
            privacy_settings: {
              profile_visibility: 'private',
              allow_contact: false
            }
          }
        }
      },
      billing: {
        version: '1.0.0',
        directories: ['invoices', 'payments', 'subscriptions'],
        tables: {
          invoices: {
            indexes: ['userId', 'status', 'dueDate'],
            constraints: ['unique_invoice_number'],
            foreignKeys: ['userId -> users.id']
          },
          payments: {
            indexes: ['invoiceId', 'status', 'paymentDate'],
            constraints: [],
            foreignKeys: ['invoiceId -> invoices.id']
          }
        },
        initialData: {
          payment_methods: {
            supported: ['credit_card', 'bank_transfer', 'paypal'],
            default_currency: 'USD'
          }
        }
      },
      reporting: {
        version: '1.0.0',
        directories: ['reports', 'dashboards', 'exports'],
        tables: {
          reports: {
            indexes: ['userId', 'type', 'createdAt'],
            constraints: [],
            foreignKeys: ['userId -> users.id']
          },
          dashboard_widgets: {
            indexes: ['userId', 'type', 'position'],
            constraints: [],
            foreignKeys: ['userId -> users.id']
          }
        },
        initialData: {
          default_dashboards: {
            admin: ['user_stats', 'activity_overview', 'system_health'],
            user: ['profile_summary', 'recent_activity']
          }
        }
      },
      auth: {
        version: '1.0.0',
        directories: ['tokens', 'magic_links', 'oauth'],
        tables: {
          magic_links: {
            indexes: ['email', 'token', 'expiresAt'],
            constraints: ['unique_token'],
            foreignKeys: []
          },
          oauth_tokens: {
            indexes: ['userId', 'provider', 'expiresAt'],
            constraints: [],
            foreignKeys: ['userId -> users.id']
          }
        },
        initialData: {
          auth_settings: {
            session_timeout: 86400,
            password_policy: {
              min_length: 8,
              require_uppercase: true,
              require_numbers: true,
              require_special_chars: false
            }
          }
        }
      },
      permissions: {
        version: '1.0.0',
        directories: ['policies', 'access_logs'],
        tables: {
          permission_policies: {
            indexes: ['resource', 'action', 'effect'],
            constraints: [],
            foreignKeys: []
          },
          access_logs: {
            indexes: ['userId', 'resource', 'timestamp'],
            constraints: [],
            foreignKeys: ['userId -> users.id']
          }
        },
        initialData: {
          default_permissions: {
            admin: ['*'],
            manager: ['user.read', 'user.write', 'report.read'],
            user: ['profile.read', 'profile.write']
          }
        }
      }
    };

    return moduleConfigs[moduleName] || null;
  }
}
